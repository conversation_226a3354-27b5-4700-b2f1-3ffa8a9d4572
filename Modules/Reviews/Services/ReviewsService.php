<?php

namespace Modules\Reviews\Services;

use Illuminate\Support\Collection;
use Modules\Reviews\Repositories\ReviewsRepository;

class ReviewsService
{
    private ReviewsRepository $_reviews_repo;
    private Collection $_review_points;
    private float $_total_rate_count;

    public function __construct(ReviewsRepository $reviewsRepository)
    {
        $this->_reviews_repo = $reviewsRepository;
        $this->_review_points = collect([
            "cleanliness" => 5,
            "accuracy" => 5,
            "location" => 5,
            "communication" => 5,
            // "darent_service" => 5,
            // "darent_recomended" => 5
        ]);
        $this->_total_rate_count = $this->_review_points->count();
    }

    public function addOrUpdateReview(array $data, $exists_review)
    {
        $data["rating"] = $this->calculateReviews($data);
        if ($data["is_guest"])
            $data = $this->duplicateGuestRateValues($data);

        return (!$exists_review) ? $this->addNewReview($data)
            : $this->updateReviewWithExistsModel($exists_review, $data);
    }

    public function addNewReview(array $data)
    {
        return $this->_reviews_repo->addReview($data);
    }

    public function updateReviewWithExistsModel($reviewModel, array $data)
    {
        $reviewModel->update($data);
        return $reviewModel;
    }

    public function update($review_id, array $data)
    {
        $data["rating"] = $this->calculateReviews($data);
        $data = $this->duplicateGuestRateValues($data);
        return $this->_reviews_repo->updateById($review_id, $data);
    }

    public function getGuestStoreValidationRules(): array
    {
        return [
            'message' => 'required|string|min:2|max:300',
            'cleanliness' => 'required|integer|min:0|max:5',
            'accuracy' => 'required|integer|min:0|max:5',
            'location' => 'required|integer|min:0|max:5',
            'communication' => 'required|integer|min:0|max:5',
            'darent_service' => 'required|integer|min:0|max:5',
            'darent_recomended' => 'required|integer|min:0|max:5',
            'darent_message' => 'nullable|string|min:2|max:300',
            'images' => 'array|max:5',
            'images.*' => 'required|file|image|mimes:png,jpg,jpeg'
        ];
    }

    public function getHostStoreValidationRules($request, $step): array
    {
        return [
            'cleanliness' => 'required|integer|min:1|max:5',
            'cleanliness_message' => 'required_if:cleanliness_reason,1,2,3|array' . ($request->cleanliness < 4 ? '|min:1' : ''),
            'cleanliness_message.*' => 'required|string|min:1|max:255',
            'communication' => ($step >= 2 ? 'required' : 'nullable') . '|integer|min:1|max:5',
            'communication_message' => ($step >= 2 ? 'required_if:communication,1,2,3' : 'nullable') . '|array' . ($request->communication < 4 ? '|min:1' : ''),
            'communication_message.*' => 'required|string|min:1|max:255',
            'message' => ($step >= 3 ? 'required' : 'nullable') . '|string|min:2|max:1000',
            'ispublic' => ($step >= 4 ? 'required' : 'nullable') . '|integer|min:0|max:1',
            'private_message' => 'nullable|string|min:2|max:1000'
        ];
    }

    private function calculateReviews(array $reviewData): float
    {
        $user_rate = (float)collect($reviewData)->only($this->_review_points->keys())->sum();
        
        return $user_rate / $this->_total_rate_count;
//        if ($calculated_rate == 0) return 5;
//        return $calculated_rate / 2;
    }

    private function duplicateGuestRateValues($data): array
    {
        $double_keys = array_merge($this->_review_points->keys()->toArray()
            , ['darent_service', 'darent_recomended']);
        return array_reduce(
            array_keys($data),
            function ($carry, $key) use ($data, $double_keys) {
                // $carry[$key] = in_array($key, $double_keys) ? floatval($data[$key]) * 2 : $data[$key];
                $carry[$key] = $data[$key];
                return $carry;
            },
            []
        );
    }
}
