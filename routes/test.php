<?php

use App\Http\Helpers\Common;
use App\Models\BookingPaymentDetails;
use App\Providers\FCMService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use App\Models\Bookings;





Route::get('/run-migration/{name}', function ($name) {
    // Execute the migration command
    $output = Artisan::call("migrate --path=database/migrations/$name.php");

    return response()->json([
        'message' => "Migration $name executed successfully!",
        'output' => Artisan::output()
    ]);
});

Route::get('test-query-function', function (Request $request) {
    $helper = new Common();
    $checkin = Carbon::parse($request->checkin)->format('Y-m-d');
    $checkout = Carbon::parse($request->checkout)->format('Y-m-d');
    // $period = CarbonPeriod::create(Carbon::parse($checkin), Carbon::parse($checkout));
    // $chunks = array_chunk(iterator_to_array($period), 14); // Split into 14-day chunks

    // $results = [];

    // foreach ($chunks as $chunk) {
    //     $startDate = reset($chunk)->format('Y-m-d');
    //     $endDate = end($chunk)->format('Y-m-d');

    //     \DB::statement("SET SESSION group_concat_max_len = 1000000");

    //      // Get the query for each chunk
    //      $queries[] = $helper->getPropertiesPricing($startDate, $endDate);

    //     // $chunkResult = \DB::select($query);
    //     // $results = array_merge($results, $chunkResult);
    // }

    // // Combine all queries with UNION
    // // return
    // $results = implode(" UNION ALL ", $queries);
    // $results = \DB::select($results);

    $period = CarbonPeriod::create(Carbon::parse($checkin), Carbon::parse($checkout));
    $chunks = array_chunk(iterator_to_array($period), 14); // Split into 14-day chunks

    $queries = [];

    foreach ($chunks as $index => $chunk) {
        $startDate = reset($chunk)->format('Y-m-d');
        $endDate = end($chunk)->format('Y-m-d');

        // Add a 1-day overlap, except for the last chunk
        if ($index !== count($chunks) - 1) {
            $endDate = Carbon::parse($endDate)->addDay()->format('Y-m-d');
        }

        // Get the query for each chunk
        $queries[] = $helper->getPropertiesPricing($startDate, $endDate);
    }

    // Combine all queries with UNION ALL
    $combinedQuery = implode(" UNION ALL ", $queries);

    // Wrap in a final SELECT to handle duplicates
    $results = "SELECT DISTINCT * FROM ({$combinedQuery}) as combined_pricing";
    $results = \DB::select($results);
    return $results;

});

Route::get('/test-fcm/{token}', function ($token) {

// Sample token for testing
// $token = 'd3eBwEflTpChvSXKH4yUbn:APA91bGu1jTTg9R5i8KHpvFDvVv9hch_ji8o4fHGo3nJ21ABNSrvWhBYMHb3ahv5cPWZFQeGevxa8T68xtoUTEzfLeK2xhYMP66PBCjeH2jeKf41stUHiKLRewXMhLb2vKzQi4tGDa7W';

// Notification details
$notification = [
    'title' => 'Test Notification',
    'body' => 'This is a test notification.'
];

// Custom data (optional)
$data = [
    'key' => 'value',
    'another_key' => 'another_value'
];

// Send notification
$response = FCMService::send($token, $notification, $data);

// Return the response for debugging
return response()->json($response);
});

// Route::get('testpayment', [App\Http\Controllers\TestPaymentController::class, 'TestPayment'])->name('TestPayment');


// Route::get('staahBooking/{id}', [App\Http\Controllers\TestPaymentController::class, 'staahBooking'])->name('TestPayment');


Route::get('/test-ip', function () {
    dd([
        ...request()->headers,
        'ip' => request()->ip(),
    ]);
});

Route::get('/run-update-properties', function () {
    Artisan::call('license-based:update-properties');
    return response()->json(['status' => 'success', 'message' => 'Command executed successfully']);
});

Route::get('successtestpayment', function (Request $request) {
    $booking = Bookings::find($request->id);
    $postFields = [
        'createQuotationRequest' => [
            'bookingID' => $booking->id,
            'policyNumber' => env('TAWUNIYA_POLICY_NUMBER'),   //  old policy number //'202200662',
            'bookingDate' => Carbon\Carbon::parse($booking->created_at)->format('Y-m-d H:i:s'),
            "propertyCatagory" => $booking->properties->propertyType->twaniya_property_type_category_id,
            'propertyTypeCode' => $booking->properties->propertyType->twaniya_property_type_code,
            'unitCode' => $booking->properties->property_code,
            'propertySumInsured' => $booking->base_price_with_or_without_discount,
            'rentPerDay' => $booking->per_night,
            'duration' => $booking->total_night,
            'propertyAddress' => (string)$booking->properties->property_address->city,
            'accommodationNum' => $booking->code,
            'noOfBedrooms' => $booking->properties->beds,
            'checkInDate' => Carbon\Carbon::parse($booking->start_date)->format('Y-m-d H:i:s'),
            'guestDetails' => [
                'Name' => $booking->users->first_name . ' ' . $booking->users->last_name,
                'Email' => $booking->users->email ?? "<EMAIL>",
                'phoneNum' => (string)$booking->users->formatted_phone,
                'IDNum' => $booking->users->elmDocument?->code ?? null,
            ],
            'ownerDetails' => [
                'Name' => $booking->host->first_name . ' ' . $booking->host->last_name,
                'Email' => $booking->host->email ?? "<EMAIL>",
                'phoneNum' => (string)$booking->host->formatted_phone,
                'IDNum' => $booking->host->elmDocument?->code ?? null,
            ],
            'createdBy' => 'Darent',
            'comments' => 'This is new booking',
        ],
    ];
    dd($postFields);
    return "Success";
})->name('successTestPayment');

Route::get('checkmabbat', [App\Http\Controllers\TestController::class, 'checkmabbat']);

//test url for hyperbill generate invoice
Route::get('hyperbillinvoice', function (Request $request) {

    $data = (new App\Http\Services\HyperBillService())->generateInvoice(100, 'SAR', 'DB', 'testname', '<EMAIL>', '96000000098', 'en', 1);
    return redirect($data);
    // return $data;
});



Route::get('apple/testpayment', function (Request $request) {
    // return 123;
    $b = Bookings::first();
    // return $b;
    // $data = $request->data;
    // $data = (new App\Http\Services\HyperpaySdkService())->checkout(11, $b->total_with_discount ?: $b->total, $b, route('result'));
    $data = (new App\Http\Services\HyperpaySdkService())->checkout(11, 1, $b, route('result'));
    // return $data;
    return view('check', compact('data'));
});

Route::get('split-pay', function (Request $request) {
    // return 123;
    $b = Bookings::first();
    // return $b;
    // $data = $request->data;
    // $data = (new App\Http\Services\HyperpaySdkService())->checkout(11, $b->total_with_discount ?: $b->total, $b, route('result'));
    $id = BookingPaymentDetails::where('id', 4129)->first();
    $data = (new App\Http\Services\HyperpayService())->makePayout($id);
    return $data;
    // return view('check', compact('data'));
})->name('apple_test_payment');

Route::get('hyperpay/refund-payment', [App\Http\Controllers\TestController::class, 'refundPayment'])->name('hyperpay.payment.refund');

// Route::get('/callbacktest', [App\Http\Controllers\TestController::class, 'callbacktest']);

Route::get('/testphotos', [App\Http\Controllers\TestController::class, 'testphotos']);

Route::get('testMail', [App\Http\Controllers\UserTestController::class, 'testMail']);
Route::get('testNotifications', [App\Http\Controllers\UserTestController::class, 'testNotifications']);


Route::get('/property/{code}/remove/license', [App\Http\Controllers\TestController::class, 'removeLicense']);

Route::get('test-notifications', function (Request $request) {
    $bookings = Bookings::where('id', $request->id)->first();
    $notifyhost = App\Models\User::find($bookings->host_id);
    $notifyhost->notify(new App\Notifications\UserNotify(
        'guest.booking.accepted.host',
        url('managehost/all_reservation?tab=coming&id=' . $bookings->id . '&query_code=' . $bookings->code ),
        data: [
            'slug' => 'booking',
            'tab' => 'upcoming-bookings',
            'code' => $bookings->code
        ]
    ));
    // $data = (new App\Http\Services\HyperpaySdkService())->refund($bookings->transaction_id, 10, 'test refund');
    // return $data;
});

Route::get('test-notifications', function (Request $request) {
    $bookings = Bookings::where('id', $request->id)->first();
    $notifyhost = App\Models\User::find($bookings->host_id);
    $notifyhost->notify(new App\Notifications\UserNotify(
        'guest.booking.accepted.host',
        url('managehost/all_reservation?tab=coming&id=' . $bookings->id . '&query_code=' . $bookings->code ),
        data: [
            'slug' => 'booking',
            'tab' => 'upcoming-bookings',
            'code' => $bookings->code
        ]
    ));
    // $data = (new App\Http\Services\HyperpaySdkService())->refund($bookings->transaction_id, 10, 'test refund');
    // return $data;
});

Route::get('/test-wallet-cashback', function () {
    $walletService = app(WalletService::class);

    // Test Scenarios
    $scenarios = [
        // Scenario 1: Full payment with cashback
        [
            'name' => 'Full Cashback Payment',
            'userId' => 1,
            'totalAmount' => 500,
            'cashbackAmount' => 500,
            'cashbackExpiry' => now()->addDays(30)
        ],
        // Scenario 2: Partial cashback payment
        [
            'name' => 'Partial Cashback Payment',
            'userId' => 2,
            'totalAmount' => 700,
            'cashbackAmount' => 200,
            'cashbackExpiry' => now()->addDays(15)
        ],
        // Scenario 3: Expired cashback scenario
        [
            'name' => 'Expired Cashback',
            'userId' => 3,
            'totalAmount' => 600,
            'cashbackAmount' => 300,
            'cashbackExpiry' => now()->subDays(5)
        ],
        // Scenario 4: Multiple cashback entries
        [
            'name' => 'Multiple Cashback Entries',
            'userId' => 4,
            'totalAmount' => 1000,
            'cashbacks' => [
                ['amount' => 200, 'expiry' => now()->addDays(10)],
                ['amount' => 300, 'expiry' => now()->addDays(20)]
            ]
        ]
    ];

    $results = [];

    foreach ($scenarios as $scenario) {
        // Prepare mock booking
        $mockBooking = (object) [
            'id' => rand(1000, 9999),
            'user_id' => $scenario['userId'],
            'total' => $scenario['totalAmount'],
            'total_with_discount' => $scenario['totalAmount']
        ];

        // Handle cashback addition
        if (isset($scenario['cashbacks'])) {
            foreach ($scenario['cashbacks'] as $cashback) {
                $walletService->addCashback(
                    $scenario['userId'],
                    $cashback['amount'],
                    $cashback['expiry']
                );
            }
        } else {
            // Single cashback scenario
            $walletService->addCashback(
                $scenario['userId'],
                $scenario['cashbackAmount'],
                $scenario['cashbackExpiry']
            );
        }

        // Perform wallet deduction
        $result = $walletService->deductFromWallet($mockBooking);
        $result['scenario'] = $scenario['name'];
        $results[] = $result;
    }

    return response()->json($results);
});

Route::get('/campaign-wallet-test', function () {
    $walletService = app(WalletService::class);

    $campaignStartDate = now()->parse('2025-01-23');
    $campaignEndDate = now()->parse('2025-02-01');

    $scenarios = [
        // New user registration scenario
        [
            'name' => 'New User Campaign Credit',
            'userId' => rand(10000, 99999),
            'campaignAmount' => 50,
            'bookingAmount' => 100,
            'expiryDate' => $campaignEndDate
        ],
        // Inactive user reactivation scenario
        [
            'name' => 'Inactive User Reactivation',
            'userId' => rand(10000, 99999),
            'campaignAmount' => 50,
            'bookingAmount' => 200,
            'expiryDate' => $campaignEndDate
        ]
    ];

    $results = [];

    foreach ($scenarios as $scenario) {
        // Add campaign credit
        $walletService->addCashback(
            $scenario['userId'],
            $scenario['campaignAmount'],
            $scenario['expiryDate']
        );

        // Create mock booking
        $mockBooking = (object) [
            'id' => rand(1000, 9999),
            'user_id' => $scenario['userId'],
            'total' => $scenario['bookingAmount'],
            'total_with_discount' => $scenario['bookingAmount']
        ];

        // Perform wallet deduction
        $result = $walletService->deductFromWallet($mockBooking);
        $result['scenario'] = $scenario['name'];
        $results[] = $result;
    }

    return response()->json($results);
});

Route::get('/test2977440response', [App\Http\Controllers\TestController::class, 'testresponse']);
Route::get('/testview', [App\Http\Controllers\TestController::class, 'testview']);

Route::get('/codetest', [App\Http\Controllers\UserTestController::class, 'codetest']);

Route::get('/insertpromoid', [App\Http\Controllers\TestController::class, 'insertpromoid']);
Route::get('/insertdiscountid', [App\Http\Controllers\TestController::class, 'insertdiscountid']);
Route::get('/declineother', [App\Http\Controllers\TestController::class, 'declineOther']);

Route::get('/currentwallet', [App\Http\Controllers\TestController::class, 'currentwallet']);
Route::get('/withdrawal', [App\Http\Controllers\TestController::class, 'withdrawal']);

Route::get('/cities', [App\Http\Controllers\TestController::class, 'cities']);
Route::get('/bookingexpired/{id}', [App\Http\Controllers\TestController::class, 'timeExpired']);
Route::get('/invoicewebview/{id}', [App\Http\Controllers\TestController::class, 'invoiceWebView']);

Route::get('/properties-delete/{id}', [App\Http\Controllers\UserTestController::class, 'deleteProperty']);
Route::get('/properties-random', [App\Http\Controllers\UserTestController::class, 'propertyCodeGenerator']);
