<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    protected function indexExists($table, $indexName): bool
    {
        $indexes = DB::select("SHOW INDEXES FROM `{$table}` WHERE Key_name = ?", [$indexName]);
        return !empty($indexes);
    }

    protected function createIndexIfNotExists($table, $column)
    {
        $indexName = "{$table}_{$column}_index";
        if (!$this->indexExists($table, $indexName)) {
            Schema::table($table, function (Blueprint $table) use ($column) {
                $table->index($column);
            });
        }
    }

    protected function dropIndexIfExists($table, $column)
    {
        $indexName = "{$table}_{$column}_index";
        if ($this->indexExists($table, $indexName)) {
            Schema::table($table, function (Blueprint $table) use ($indexName) {
                $table->dropIndex($indexName);
            });
        }
    }
    public function up(): void
    {
        foreach ([
            'master_availability' => ['status', 'determine_price'],
            'properties' => ['property_type', 'visibility', 'status', 'slug', 'host_id', 'property_code', 'adult_guest', 'bedrooms', 'beds', 'bathrooms', 'space_type', 'min_nights'],
            'property_address' => ['property_id', 'latitude', 'longitude', 'city', 'district_id'],
            'property_price' => ['property_id'],
            'users' => ['id', 'status'],
            'cities' => ['name', 'name_ar'],
            'property_dates' => ['property_id', 'date', 'status'],
            'custom_pricing' => ['property_id', 'date', 'status'],
            'districts' => ['city_id', 'name', 'name_ar'],
            'property_description' => ['property_id']
        ] as $table => $columns) {
            foreach ($columns as $column) {
                $this->createIndexIfNotExists($table, $column);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        foreach ([
            'master_availability' => ['status', 'determine_price'],
            'properties' => ['property_type', 'visibility', 'status', 'slug', 'host_id', 'property_code', 'adult_guest', 'bedrooms', 'beds', 'bathrooms', 'space_type', 'min_nights'],
            'property_address' => ['property_id', 'latitude', 'longitude', 'city', 'district_id'],
            'property_price' => ['property_id'],
            'users' => ['id', 'status'],
            'cities' => ['name', 'name_ar'],
            'property_dates' => ['property_id', 'date', 'status'],
            'custom_pricing' => ['property_id', 'date', 'status'],
            'districts' => ['city_id', 'name', 'name_ar'],
            'property_description' => ['property_id']
        ] as $table => $columns) {
            foreach ($columns as $column) {
                $this->dropIndexIfExists($table, $column);
            }
        }
    }
};
