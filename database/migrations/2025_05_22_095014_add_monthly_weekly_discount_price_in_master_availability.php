<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('master_availability', function (Blueprint $table) {
            $table->decimal('weekly_discount_amount',10,2)->default(0)->after('determine_price');
            $table->decimal('monthly_discount_amount',10,2)->default(0)->after('weekly_discount_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('master_availability', function (Blueprint $table) {
            $table->dropColumn(['weekly_discount_amount','monthly_discount_amount']);
        });
    }
};
