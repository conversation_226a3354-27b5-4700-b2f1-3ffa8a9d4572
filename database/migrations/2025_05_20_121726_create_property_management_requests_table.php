<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('property_management_requests', function (Blueprint $table) {
            $table->id();
            $table->string('user_id')->nullable()->default(null);
            $table->string('owner_name');
            $table->string('email');
            $table->string('property_name');
            $table->integer('units')->default(0);
            $table->string('location');
            $table->string('latitude')->nullable()->default(null);
            $table->string('longitude')->nullable()->default(null);
            $table->string('street')->nullable()->default(null);
            $table->string('route')->nullable()->default(null);
            $table->string('country')->nullable()->default(null);
            $table->string('state')->nullable()->default(null);
            $table->string('postal_code')->nullable()->default(null);
            $table->integer('city_id');
            $table->string('contact_no');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('property_management_requests');
    }
};
