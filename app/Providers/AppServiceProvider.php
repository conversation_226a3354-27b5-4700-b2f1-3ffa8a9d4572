<?php

namespace App\Providers;

use App\Http\View\Composers\RequestComposer;
use App\Services\CustomTranslator;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\User;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // $this->app->singleton('translator', function ($app) {
        //     return new CustomTranslator($app['translation.loader'], $app['config']['app.locale']);
        // });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (app()->environment() == 'prod') {
            URL::forceScheme('https');
        }

        View::composer('template', RequestComposer::class);

        RateLimiter::for('otp', function (Request $request) {
            $ip = $request->header('cf-connecting-ip');

            return Limit::perHour(5)->by($request->user()?->id ?: $ip);
        });

        Gate::define('viewPulse', function (User $user) {
            return true;
        });
    }
}
