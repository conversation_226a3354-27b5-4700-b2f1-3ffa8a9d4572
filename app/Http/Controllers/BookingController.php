<?php

namespace App\Http\Controllers;

use View, Auth, DB, Session, DateTime;
use Carbon\Carbon;
use App\Notifications\UserNotify;
use App\Notifications\UserNotifySms;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Notification;


use App\Http\{
    Helpers\Common,
    Controllers\Controller,
    Controllers\EmailController,
    Requests
};
use App\Models\{
    Bank,
    Bookings,
    BookingDetails,
    Messages,
    Penalty,
    Payouts,
    Properties,
    PayoutPenalties,
    PropertyDates,
    PropertyFees,
    Settings,
    Currency,
    User,
    Country,
    UserCards,
    CancellationPolicy,
    PromoCode,
    PromoCodeUsage,
    Prompt,
    PropertyChat,
    PropertyChatHead,
    BookingStatusLog,
    CustomPricing,
    PaymentMethods
};

use Illuminate\Http\Request;
use App\Enums\PaymentTypeEnum;
use App\Enums\PropertyChatPromptEnum;
use App\Enums\PropertyChatTypeEnum;
use App\Enums\TransactionActionEnum;
use App\Http\Services\FatoorahService;
use App\Http\Services\HyperpayService;
use App\Http\Services\HyperpaySdkService;
use App\Http\Services\MoyasarService;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class BookingController extends Controller
{
    private $helper;
    public $myfatoorah_apiURL;
    public $myfatoorah_apikey;
    public $curl_ssl;

    public function __construct()
    {
        $this->helper = new Common;
        if (env('MYFATOORAH_MODE') == 'test') {
            $this->myfatoorah_apiURL = 'https://apitest.myfatoorah.com/v2/';
            $this->myfatoorah_apikey = env('MYFATOORAH_API_KEY_TEST'); //Test token value to be placed here: https://myfatoorah.readme.io/docs/test-token
            $this->curl_ssl = false;
        } else {
            $this->myfatoorah_apiURL = 'https://api-sa.myfatoorah.com/v2/';
            $this->myfatoorah_apikey = env('MYFATOORAH_API_KEY_LIVE'); //Live token value to be placed here: https://myfatoorah.readme.io/docs/live-token
            $this->curl_ssl = true;
        }
    }

    /**
     * Booking Details Show By Host
     * For request to book Accept & Decline
     *
     */
    public function index(Request $request)
    {
        // dd('Create Booking details page');
        $data['title'] = 'Booking Details';
        $data['booking_id'] = $request->id;
        $data['result'] = Bookings::find($request->id);
        if (!$data['result'] || $data['result']->host_id != Auth::user()->id) {
            abort('404');
        }
        $data['price_list'] = json_decode($this->helper->getPrice($data['result']->property_id, $data['result']->start_date, $data['result']->end_date, $data['result']->guest, true, $request->id));
        $data['symbol'] = $this->helper->getCurrentCurrencySymbol();

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.booking.detail', $data);
        }

        return view('booking.detail', $data);
    }


    function appleView($booking_id, Request $request)
    {
        if (app('PAYMENT_METHOD') == Bookings::PAY_BY_MOYASAR) {
            $booking = Bookings::with('guest')->findOrFail($booking_id);
            // $finalamount = ($booking->total_with_discount ?: $booking->total) * 100;
            // return view('payment.testapple', compact('booking', 'finalamount'));
            if ($request->session()->has('walletData') && !empty($request->session()->get('walletData'))) {
                $walletData = $request->session()->has('walletData');
                $finalamount = $walletData['finalamount'] * 100;
                return view('payment.testapple', compact('booking', 'finalamount', 'walletData'));
            }
        } else {
            $booking = Bookings::where('user_id', auth()->id())->findOrFail($booking_id);
            $curl_res = $this->helper->callAPI($this->myfatoorah_apiURL . 'InitiateSession', $this->myfatoorah_apikey, $fakeData = array(
                'key' => 'vale'
            ));
            $data['sessionId'] = $curl_res->SessionId;
            $data['booking_id'] = $booking_id;
            $data['finalamount'] = number_format((config('app.env') != 'local' ? (Session::get('finalamount')) : 1), 2, '.', '');
            return view('payment.fatoorahapple', $data);
        }
    }

    /**
     * Request to Book
     * From email link redirect to payment page
     *
     */
    public function makePayment(Request $request)
    {
        Log::debug($request->all());
        // dd($request->paymentMethodId);

        $propertyFees = PropertyFees::pluck('value', 'field');
        $guest_service_charge = $propertyFees['guest_service_charge'];
        $bookingId = $request->id;

        $booking = Bookings::with('host', 'users', 'properties')->find($bookingId);
        $booking->payment_type_id = $request->paymentMethodId;
        $booking->save();

        $rand_number = $this->helper->randomCode(3);
        $customer_ref = $rand_number . "-B-" . $bookingId;
        $finalamount = $booking->total_with_discount && !$booking->hasForcedTotal() ? $booking->total_with_discount : $booking->total;
        $couponResponse = null;
        // dd($request->couponCode, $booking->hasForcedTotal(),$request->couponCode && $booking->hasForcedTotal());
        // if ($request->couponCode && $booking->hasForcedTotal()) {
        if ($request->couponCode) {
            $request->merge(['checkin' => $booking->start_date, 'checkout' => $booking->end_date]);
            $couponResponse = $this->helper->applyCoupon($request->couponCode, $booking->property_id, $booking->base_price- $booking->host_discount_amount ?? 0, Auth::user()->id, $request);

            // dd($couponResponse);
            $service_fee_on_cleaning = $booking->cleaning_charge * $guest_service_charge / 100;
            $service_fee_on_security_money = $booking->security_money * $guest_service_charge / 100;

            if ($booking->properties->platform_id == 4) {
                $cleaning_fee_ser = ($booking->cleaning_charge / 100) * 11;
                $allothers = $booking->cleaning_charge + $cleaning_fee_ser;
            } else {
                $allothers = $service_fee_on_cleaning + $service_fee_on_security_money + $booking->cleaning_charge + $booking->additional_guest + $booking->accomodation_tax + $booking->security_money;
            }
            // dd($couponResponse->getData()->amount_after_discount + $booking->cleaning_charge + $booking->iva_tax);
            $promoCodeData = PromoCode::where(['code' => $request->couponCode])->first();

            // ------------------------------- WALLET CAMPAIGN -------------------------------------------------
            if (!$promoCodeData->is_campaign) {
                $finalamount = round($couponResponse->getData()->amount_after_discount + $allothers + $booking->iva_tax, 2);
            }
            // ------------------------------- WALLET CAMPAIGN --------------------------------------------------

            if ($couponResponse->getData()->status == 'success' || $couponResponse->getData()->status == 'campaign') {

                // dd($finalamount);
                $service_fee_on_base_price = $couponResponse->getData()->servicefee;

                $promoCode = $promoCodeData;
                $codeUsage = PromoCodeUsage::firstOrNew(['promo_code_id' => $promoCode->id, 'user_id' => Auth::user()->id, 'property_id' => $booking->property_id, 'booking_id' => $bookingId]);
                $codeUsage->fill([
                    'discount_type' => $couponResponse->getData()->discount_type,
                    'discount_value' => $couponResponse->getData()->discount_value,
                    'original_amount' => $booking->total,
                    'after_discount' => $finalamount,
                    'booking_id' => $bookingId,
                ]);

                $codeUsage->save();

                $booking->total_with_discount = $finalamount;
                $booking->total_discount = $couponResponse->getData()->discount_value;
                $booking->base_price_with_discount = $couponResponse->getData()->amount_after_discount - $couponResponse->getData()->servicefee;
                if ($booking->properties->platform_id == 4) {
                    $booking->service_charge = $service_fee_on_security_money + $booking->additional_guest + $booking->accomodation_tax + $service_fee_on_base_price + $cleaning_fee_ser;
                } else {

                    $booking->service_charge = $service_fee_on_cleaning + $service_fee_on_security_money + $booking->additional_guest + $booking->accomodation_tax + $service_fee_on_base_price;
                }

                $booking->save();
            } else {
                return response()->json(['error' => true, 'error_message' => $couponResponse->getData()->message], 200);
            }
        }

        if ($finalamount <= 0) {
            return redirect()->route('contact.admin', ['bookingid' => $bookingId]);
        }

        // dd(123);
        // $exist_card =  UserCards::where(['is_default' => 1, 'user_id' => auth()->user()->id, 'paymentMethodId' => $request->paymentMethodId])->first();

        // if (!$exist_card) {

        //     if (!request()->exists('number')) {
        //         return response()->json(['error' => true, 'error_message' => 'Please supply card details also'], 200);
        //     }

        //     $validator = Validator::make($request->all(), [
        //         'name'    => 'required',
        //         'number' => 'required|min:16,max:16',
        //         'month' => 'required|min:2,max:2',
        //         'year' => 'required|min:2,max:2',
        //         'cvv' => 'required|min:3,max:3',
        //     ]);

        //     if ($validator->fails()) {
        //         return response()->json($validator->errors(), 422);
        //     }

        //     $exist_supplied_card =  UserCards::where([
        //         'card_number' => $request->number,
        //         'is_verified' => 0,
        //         'user_id' => auth()->user()->id
        //     ])
        //         ->first();

        //     if ($exist_supplied_card) {
        //         $exist_supplied_card->forceDelete();
        //     }

        //     $usercard = new UserCards;
        //     $usercard->user_id = auth()->user()->id;
        //     $usercard->card_number = $request->number;
        //     $usercard->card_name = $request->name;
        //     $usercard->card_month = $request->month;
        //     $usercard->card_year =  $request->year;
        //     $usercard->card_cvv =  $request->cvv;
        //     $usercard->save();
        //     $exist_card = $usercard;
        // }

        try {
            // -------------------------------- WALLET INTEGRATION --------------------------------------------
            $walletData = [];
            if ($request->pay_by_wallet) {
                $walletData = $this->helper->payByWallet($finalamount, $booking);
                $finalamount = $walletData['finalamount'];
            }

            if ($request->pay_by_wallet && $finalamount == 0) {
                return response()->json(['message' => 'Success', 'data' => url('/') . '/payment/callback/' . Bookings::PAYMENT_STATUS_WALLET . '?booking=' . $bookingId], 200);
            }

            if (!!$request->apple) {
                if ($request->has('pay_by_wallet')) {
                    session([
                        'booking_id' => $bookingId,
                        'walletData' => $walletData,
                    ]);
                } else {
                    session([
                        'booking_id' => $bookingId,
                    ]);
                }
                if ($request->apple == 1) {

                    $shopperResultUrl = route('payment.hyperinitiated');
                    $booking = Bookings::with('host')->find($bookingId);
                    $data = (new HyperpaySdkService())->checkout(11, $finalamount, $booking, $shopperResultUrl, ['booking' => $booking->id, 'walletData' => $walletData]);

                    // dd($data);
                    session::put('hyperpayresponce', $data);
                    return response()->json(['message' => 'Success', 'data' => route('ProcessPayment')], 200);
                }
            }
            // -------------------------------- WALLET INTEGRATION --------------------------------------------

            if (app('PAYMENT_METHOD') == Bookings::PAY_BY_MOYASAR) {
                $paymentURL = (new MoyasarService())->payment('credit_card', $finalamount, route('payment.initiated'), $exist_card, ['payer' => auth()->user(), 'payee' => $booking->host, 'service' => $booking, 'action' => 1, 'data' => ['booking' => $booking->toArray()]], ['walletData' => $walletData]);
            } elseif (app('PAYMENT_METHOD') == Bookings::PAY_BY_HYPERPAY) {

                // $hyperpayService = new HyperpayService();
                // $shopperResultUrl = route('payment.hyperinitiated');
                // $response = $hyperpayService->makePayment(($finalamount), 'SAR', $request->paymentMethodId, $exist_card, $shopperResultUrl, ['service' => $booking], ['booking' => $booking->id, 'walletData' => $walletData]);
                // if ($response['status'] == 'success' && isset($response['redirect'])) {
                //     $redirectUrl = $response['redirect'];
                //     $redirectParams = $response['parameters'];
                //     // Build the query string from the parameters
                //     $queryParams = [];
                //     foreach ($redirectParams as $param) {
                //         if (isset($param['name']) && isset($param['value'])) {
                //             $queryParams[$param['name']] = $param['value'];
                //         }
                //     }
                //     $queryString = http_build_query($queryParams);
                //     // Redirect to the constructed URL
                //     $paymentURL = $redirectUrl . '?' . $queryString;


                // }


                $shopperResultUrl = route('payment.hyperinitiated');

                // dd($finalamount);

                $data = (new HyperpaySdkService())->checkout($request->paymentMethodId, $finalamount, $booking, $shopperResultUrl, ['booking' => $booking->id, 'walletData' => $walletData]);

                $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                    ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                    ->where('ps.id', $booking->property_id)->distinct()->get();
                Notification::send($hosts, new UserNotify(
                    'guest.booking.payment.click.host',
                    route('managehost.all_reservation'),
                    data: [
                        'slug' => 'booking',
                        'tab' => 'upcoming-bookings'
                    ]
                ));
                session::put('hyperpayresponce', $data);
                //         // $this->ProcessPayment($data);
                //  return redirect()->route('ProcessPayment');
                return response()->json(['message' => 'Success', 'type' => 'CardPayment', 'data' => $data], 200);

                //  return $data;
            } else {
                $paymentURL = (new FatoorahService())->execute($finalamount, $exist_card, $customer_ref, $booking->toArray());
            }
        } catch (Exception $e) {
            if ($e instanceof ValidationException) {
                $this->helper->one_time_message('error', "Invalid card");
                if (request()->ajax()) {
                    return response()->json(['error' => true, 'custom_error' => 'Invalid card'], 422);
                }
                return redirect()->back();
            }
        }


        return response()->json(['message' => 'Success', 'data' => $paymentURL], 200);
    }


    /**
     * Booking Complete redirect here
     *
     */
    public function requested(Request $request)
    {
        $data['booking'] = $booking = Bookings::where('code', $request->code)->first();
        $data['property_data'] = Properties::with('property_type')->where('id', $data['booking']['property_id'])->first();
        // dd($data['property_data']);
        if ($booking->status == 'Declined') {
            return redirect()->route('guest_reservation');
        }
        $data['booking_details'] = Bookings::with(['currency', 'properties'])->where('code', $request->code)->first();
        if ($data['booking_details']->status == 'Unpaid' || $data['booking_details']->status == 'Processing' || $data['booking_details']->status == 'Pending') {

            $data['title'] = trans('messages.booking_detail.request_booking');
        }

        $data['price_list'] = json_decode($this->helper->getPrice($data['booking_details']->property_id, $data['booking_details']->start_date, $data['booking_details']->end_date, $data['booking_details']->guest, true, $booking->id));

        $data['symbol'] = $this->helper->getCurrentCurrencySymbol();
        if (isset(Auth::user()->email)) {
            $email_controller = new EmailController;
            $email_controller->booking_user($booking->id, onlyFormat($booking->start_date));
        }
        // dd($data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.booking.requested', $data);
        } else {
            return view('booking.requested', $data);
        }
        return view('booking.requested', $data);
    }

    /**
     * Booking FAILED redirect here
     *
     */
    public function failed(Request $request)
    {
        $data['booking_details'] = Bookings::with('currency')->where('code', $request->code)->first();

        if ($data['booking_details']->status == 'Unpaid' || $data['booking_details']->status == 'Processing' || $data['booking_details']->status == 'Pending') {
            $data['title'] = trans('messages.booking_detail.request_booking');
        }
        $data['price_list'] = json_decode($this->helper->getPrice($data['booking_details']->property_id, $data['booking_details']->start_date, $data['booking_details']->end_date, $data['booking_details']->guest, true));
        $data['symbol'] = $this->helper->getCurrentCurrencySymbol();

        return view('booking.failed', $data);
    }


    /**
     *  Requst to Booking accept
     *
     */
    public function accept(Request $request, EmailController $email)
    {
        $booking = Bookings::find($request->id);
        $penalty = Penalty::where('user_id', Auth::user()->id)->where('remaining_penalty', '!=', 0)->get();
        $penalty_result = $this->helper->host_penalty_check($penalty, $booking->host_payout, $booking->currency_code);

        $booking->status = 'Processing';
        $booking->accepted_at = date('Y-m-d H:i:s');
        $booking->save();

        BookingStatusLog::updateOrCreate(
            ['booking_id' => $booking->id],
            ['status' => $booking->status, 'changed_by' => Auth::id()]
        );

        $payouts = new Payouts;
        $payouts->booking_id = $request->id;
        $payouts->property_id = $booking->property_id;
        $payouts->user_id = $booking->host_id;
        $payouts->user_type = 'host';
        $payouts->amount = $penalty_result['host_amount'];
        $payouts->penalty_amount = $penalty_result['penalty_total'];
        $payouts->currency_code = $booking->currency_code;
        $payouts->status = 'Future';
        $payouts->save();

        $panalty_ids = explode(',', $penalty_result['penalty_ids']);
        $panalty_amounts = explode(',', $penalty_result['panalty_amounts']);

        for ($i = 0; $i < count($panalty_ids); $i++) {
            if ($panalty_ids[$i] != '' && $panalty_amounts[$i] != '') {
                $payout_penalties = new PayoutPenalties;
                $payout_penalties->payout_id = $payouts->id;
                $payout_penalties->penalty_id = $panalty_ids[$i];
                $payout_penalties->amount = $panalty_amounts[$i];
                $payout_penalties->save();
            }
        }


        $status = 'Processing';
        if (isset(Auth::user()->email)) {
            $email->bookingAcceptedOrDeclined($request->id, $status);
        }
        $now = now();
        $chat_head = PropertyChatHead::firstOrCreate(['property_id' => $booking->property_id, 'guest_id' => $booking->user_id, 'host_id' => auth()->id()]);
        $chatData = [
            // [
            //     'message' => Prompt::generatePrompt([':booking' => $booking->code], PropertyChatPromptEnum::BookingRequestHostAccept->value),
            //     'sender_id' => auth()->id(),
            //     'type' => PropertyChatTypeEnum::Prompt->value,
            //     'property_chat_head_id' => $chat_head->id,
            //     'created_at' => $now,
            //     'updated_at' => $now
            // ], [
            //     'message' => Prompt::generatePrompt([':booking' => $booking->code], PropertyChatPromptEnum::BookingRequestHostAcceptResponse->value),
            //     'sender_id' => $booking->user_id,
            //     'type' => PropertyChatTypeEnum::Prompt->value,
            //     'property_chat_head_id' => $chat_head->id,
            //     'created_at' => $now,
            //     'updated_at' => $now
            // ]
        ];
        (User::find($booking->user_id))->notify(new UserNotify(
            'host.accept.booking.request.guest',
            route('guest_reservation_detail', $booking->code),
            data: ['slug' => 'reservation', 'tab' => '0', 'booking_id' => $booking->code]
        ));
        if (!empty($request->message)) {
            $msg = mb_convert_encoding($request->message, 'UTF-8', 'UTF-8');
            $chatData[] = [
                'message' => $msg,
                'sender_id' => auth()->id(),
                'type' => PropertyChatTypeEnum::Text->value,
                'property_chat_head_id' => $chat_head->id,
                'created_at' => $now,
                'updated_at' => $now
            ];
            PropertyChat::insert($chatData);
            (User::find($booking->user_id))->notify(new UserNotify(
                'message.text.received',
                route('properties.chat.view', ['type' => 'guest']),
                [':user' => auth()->user()->first_name, ':msg' => (substr($request->message, 0, 30) . (strlen($request->message) > 30 ? '...' : ''))],
                [
                    'slug' => 'guest/property_inbox',
                    'chat_head_id' => $chat_head->id
                ]
            ));
        }

        $this->helper->one_time_message('success', trans('messages.success.booking_accept_success'));
        return redirect('incoming/booking/request');
    }


    public function acceptBooking(Request $request, EmailController $email)
    {
        try {
            $booking = Bookings::find($request->id);

            $penalty = Penalty::where('user_id', Auth::user()->id)->where('remaining_penalty', '!=', 0)->get();
            $penalty_result = $this->helper->host_penalty_check($penalty, $booking->host_payout, $booking->currency_code);

            // $capturePay = new HyperpaySdkService();
            // $guestPaid = $booking->total_with_discount != 0 ? $booking->total_with_discount : $booking->total;
            // $capturePay->capturePayment($booking->paymentDetails->payment_value, $guestPaid);

            $booking->status = Bookings::PROCESSING;
            $booking->accepted_at = date('Y-m-d H:i:s');
            $booking->save();

            BookingStatusLog::updateOrCreate(
                ['booking_id' => $booking->id],
                ['status' => $booking->status, 'changed_by' => Auth::id()]
            );


            $payouts = new Payouts;
            $payouts->booking_id = $request->id;
            $payouts->property_id = $booking->property_id;
            $payouts->user_id = $booking->host_id;
            $payouts->user_type = 'host';
            $payouts->amount = $penalty_result['host_amount'];
            $payouts->penalty_amount = $penalty_result['penalty_total'];
            $payouts->currency_code = $booking->currency_code;
            $payouts->status = 'Future';
            $payouts->save();

            $panalty_ids = explode(',', $penalty_result['penalty_ids']);
            $panalty_amounts = explode(',', $penalty_result['panalty_amounts']);

            for ($i = 0; $i < count($panalty_ids); $i++) {
                if ($panalty_ids[$i] != '' && $panalty_amounts[$i] != '') {
                    $payout_penalties = new PayoutPenalties;
                    $payout_penalties->payout_id = $payouts->id;
                    $payout_penalties->penalty_id = $panalty_ids[$i];
                    $payout_penalties->amount = $panalty_amounts[$i];
                    $payout_penalties->save();
                }
            }

            if (!empty($request->message)) {
                $messages = new Messages;
                $messages->property_id = $booking->property_id;
                $messages->booking_id = $booking->id;
                $messages->receiver_id = $booking->user_id;
                $messages->sender_id = Auth::user()->id;
                $messages->message = $request->message;
                $messages->type_id = 5;
                $messages->save();
            }

            // $email->bookingAcceptedOrDeclined($request->id, $status);

            $companyName = Settings::getAll()->where('type', 'general')->where('name', 'name')->first()->value;
            $requestBookingConfirm = ($companyName . ': ' . 'Your booking request for' . ' ' . $booking->properties->name . ' ' . 'is Accepted, Please Payment for booking.');
            $requestBookingConfirmArabic = ($companyName . ': ' . 'طلب الحجز الخاص بك لـ ' . ' ' . $booking->properties->name . ' ' . 'مقبول، يرجى الدفع لإكمال الحجز');

            $notifyhost = User::find($booking->user_id);


            $now = now();
            $chat_head = PropertyChatHead::firstOrCreate(['property_id' => $booking->property_id, 'guest_id' => $booking->user_id, 'host_id' => auth()->id()]);
            $chatData = [
                // [
                //     'message' => Prompt::generatePrompt([':booking' => $booking->code], PropertyChatPromptEnum::BookingRequestHostAccept->value),
                //     'sender_id' => auth()->id(),
                //     'type' => PropertyChatTypeEnum::Prompt->value,
                //     'property_chat_head_id' => $chat_head->id,
                //     'created_at' => $now,
                //     'updated_at' => $now
                // ], [
                //     'message' => Prompt::generatePrompt([':booking' => $booking->code], PropertyChatPromptEnum::BookingRequestHostAcceptResponse->value),
                //     'sender_id' => $booking->user_id,
                //     'type' => PropertyChatTypeEnum::Prompt->value,
                //     'property_chat_head_id' => $chat_head->id,
                //     'created_at' => $now,
                //     'updated_at' => $now
                // ]
            ];
            (User::find($booking->user_id))->notify(new UserNotify(
                'host.accept.booking.request.guest',
                route('guest_reservation_detail', ['code' => $booking->code]),
                data: [
                    'slug' => 'reservation',
                    'tab' => '0',
                    'booking_id' => $booking->code,
                    'is_accepted' => $booking->status == 'Accepted' ? true : false
                ]
            ));
            if (!empty($request->message)) {
                $chatData[] = [
                    'message' => $request->message,
                    'sender_id' => auth()->id(),
                    'type' => PropertyChatTypeEnum::Text->value,
                    'property_chat_head_id' => $chat_head->id,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
                $msg = mb_convert_encoding(substr($request->message, 0, 30) . (strlen($request->message) > 30 ? '...' : ''), 'UTF-8', 'UTF-8');
                PropertyChat::insert($chatData);
                (User::find($booking->user_id))->notify(new UserNotify(
                    'message.text.received',
                    route('properties.chat.view', ['type' => 'guest']),
                    [':user' => auth()->user()->first_name, ':msg' => $msg],
                    [
                        'slug' => 'guest/property_inbox',
                        'chat_head_id' => $chat_head->id
                    ]
                ));
            }
            return response()->json(["message" => "Success", "data" => Bookings::with('properties')->find($request->id)], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    /**
     *  Requst to Booking decline
     *
     */
    public function decline(Request $request, EmailController $email)
    {

        $booking = Bookings::find($request->id);
        $booking->status = Bookings::DECLINED;
        // $booking->declined_at     = date('Y-m-d H:i:s');
        $booking->declined_at = Carbon::now()->timezone('Asia/Riyadh')->format('Y-m-d H:i:s');


        // $capturePay = new HyperpaySdkService();
        // $capturePay->reversePayment($booking->paymentDetails->payment_value);

        $booking->cancelled_by = "Host";
        $booking->save();

        $days = $this->helper->get_days($booking->start_date, $booking->end_date);

        for ($j = 0; $j < count($days) - 1; $j++) {
            PropertyDates::where('property_id', $booking->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
        }

        BookingStatusLog::updateOrCreate(
            ['booking_id' => $booking->id],
            ['status' => $booking->status, 'changed_by' => Auth::id()]
        );

        $booking_details = new BookingDetails;
        $booking_details->booking_id = $request->id;
        $booking_details->field = 'decline_reason';
        $booking_details->value = isset($request->decline_reason) ? $request->decline_reason : 'Decline by Host';
        $booking_details->save();

        $payouts = new Payouts;
        $payouts->booking_id = $request->id;
        $payouts->property_id = $booking->property_id;
        $payouts->user_id = $booking->user_id;
        $payouts->user_type = 'guest';
        $payouts->amount = $booking->original_guest_payout;
        $payouts->penalty_amount = 0;
        $payouts->currency_code = $booking->currency_code;
        $payouts->status = 'Future';
        $payouts->save();

        if ($request->block_calendar == 'yes') {
            $days = $this->helper->get_days($booking->start_date, $booking->end_date);

            for ($i = 0; $i < count($days) - 1; $i++) {
                // this data is for PropertyDates
                // $property_date = [
                //     'property_id' => $booking->property_id,
                //     'booking_id' => $booking->id,
                //     'date'        => $days[$i],
                //     'status'      => 'Not available',
                //     'type'         => "calendar",
                // ];


                $property_date = [
                    'property_id' => $booking->property_id,
                    'date' => $days[$i],
                    'status' => 'Not available',
                    'type' => "calendar",
                ];

                // PropertyDates::updateOrCreate(['property_id' => $booking->property_id, 'date' => $days[$i]], $property_date);
                CustomPricing::updateOrCreate(['property_id' => $booking->property_id, 'date' => $days[$i]], $property_date);
            }
        } else {
            $days = $this->helper->get_days($booking->start_date, $booking->end_date);

            for ($i = 0; $i < count($days) - 1; $i++) {
                // This data is for PropertyDates
                // $property_date = [
                //     'property_id' => $booking->property_id,
                //     'booking_id' => $booking->id,
                //     'date'        => $days[$i],
                //     'status'  => 'Available',
                //     'type'         => "calendar",
                // ];

                $property_date = [
                    'property_id' => $booking->property_id,
                    'date' => $days[$i],
                    'status' => 'Available',
                    'type' => "calendar",
                ];

                // PropertyDates::updateOrCreate(['property_id' => $booking->property_id, 'date' => $days[$i]], $property_date);
                CustomPricing::updateOrCreate(['property_id' => $booking->property_id, 'date' => $days[$i]], $property_date);
            }
        }
        $now = now();
        $chat_head = PropertyChatHead::firstOrCreate(['property_id' => $booking->property_id, 'guest_id' => $booking->user_id, 'host_id' => auth()->id()]);
        $chatData = [
            // [
            //     'message' => Prompt::generatePrompt([':booking' => $booking->code], PropertyChatPromptEnum::BookingRequestHostReject->value),
            //     'sender_id' => auth()->id(),
            //     'type' => PropertyChatTypeEnum::Prompt->value,
            //     'property_chat_head_id' => $chat_head->id,
            //     'created_at' => $now,
            //     'updated_at' => $now
            // ], [
            //     'message' => Prompt::generatePrompt([':booking' => $booking->code], PropertyChatPromptEnum::BookingRequestHostRejectResponse->value),
            //     'sender_id' => $booking->user_id,
            //     'type' => PropertyChatTypeEnum::Prompt->value,
            //     'property_chat_head_id' => $chat_head->id,
            //     'created_at' => $now,
            //     'updated_at' => $now
            // ]
        ];
        (User::find($booking->user_id))->notify(new UserNotify(
            'booking.request.declined',
            url('/guest/reservation/'.$booking->code.'?booking=cancelled'),
            [':property' => $booking->properties->name],
            ['slug' => 'reservation', 'tab' => '3', 'booking_id' => $booking->code]
        ));
        if (!empty($request->message)) {
            $chatData[] = [
                'message' => $request->message,
                'sender_id' => auth()->id(),
                'type' => PropertyChatTypeEnum::Text->value,
                'property_chat_head_id' => $chat_head->id,
                'created_at' => $now,
                'updated_at' => $now
            ];
            $msg = mb_convert_encoding(substr($request->message, 0, 30) . (strlen($request->message) > 30 ? '...' : ''), 'UTF-8', 'UTF-8');
            PropertyChat::insert($chatData);
            (User::find($booking->user_id))->notify(new UserNotify(
                'message.text.received',
                route('properties.chat.view', ['type' => 'guest']),
                [':user' => auth()->user()->first_name, ':msg' => $msg],
                [
                    'slug' => 'guest/property_inbox',
                    'chat_head_id' => $chat_head->id
                ]
            ));
        }
        $status = 'Declined';
        if (isset(Auth::user()->email)) {
            $email->bookingAcceptedOrDeclined($request->id, $status);
        }

        //    twilioSendSms($booking->users->formatted_phone, $requestBookingDecline);

        clearCache('.calc.property_price');
        $this->helper->one_time_message('success', trans('messages.success.booking_decline_success'));
        if ($request->route()->getName() == 'declineBooking') {
            return response()->json(["message" => "Success", "data" => Bookings::with('properties')->find($request->id)]);
        }
        return redirect('incoming/booking/request');
    }

    public function expire(Request $request)
    {

        $booking = Bookings::find($request->id);

        $fees = PropertyFees::pluck('value', 'field');

        $host_penalty = $fees['host_penalty'];
        $currency = $fees['currency'];
        $more_then_seven = $fees['more_then_seven'];
        $less_then_seven = $fees['less_then_seven'];
        $cancel_limit = $fees['cancel_limit'];

        if (Session::get('currency')) {
            $code = Session::get('currency');
        } else {
            $code = DB::table('currency')->where('default', 1)->first()->code;
        }

        if ($host_penalty != 0) {
            $penalty = new Penalty;
            $penalty->property_id = $booking->property_id;
            $penalty->user_id = $booking->user_id;
            $penalty->booking_id = $request->id;
            $penalty->currency_code = $booking->currency_code;
            $penalty->amount = $this->helper->convert_currency($penalty_currency, $code, $penalty_before_days);
            $penalty->remain_amount = $penalty->amount;
            $penalty->status = "Pending";
            $penalty->save();
        }

        $to_time = strtotime($booking->created_at);
        $from_time = strtotime(date('Y-m-d H:i:s'));
        $diff_mins = round(abs($to_time - $from_time) / 60, 2);

        if ($diff_mins >= 1440) {
            $booking->status = 'Expired';
            $booking->expired_at = date('Y-m-d H:i:s');
            $booking->save();

            BookingStatusLog::updateOrCreate(
                ['booking_id' => $booking->id],
                ['status' => $booking->status, 'changed_by' => '']
            );

            $days = $this->helper->get_days($booking->start_date, $booking->end_date);
            for ($j = 0; $j < count($days) - 1; $j++) {
                PropertyDates::where('property_id', $booking->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
            }

            $payouts = new Payouts;
            $payouts->booking_id = $request->id;
            $payouts->property_id = $booking->property_id;
            $payouts->user_id = $booking->user_id;
            $payouts->user_type = 'guest';
            $payouts->amount = $booking->original_guest_payout;
            $payouts->penalty_amount = 0;
            $payouts->currency_code = $booking->currency_code;
            $payouts->status = 'Future';
            $payouts->save();

            $messages = new Messages;
            $messages->property_id = $booking->property_id;
            $messages->booking_id = $booking->id;
            $messages->receiver_id = $booking->user_id;
            $messages->sender_id = Auth::user()->id;
            $messages->message = '';
            $messages->type_id = 7;
            $messages->save();

            $this->helper->one_time_message('success', trans('messages.success.booking_expire_success'));
            clearCache('.calc.property_price');
            return redirect('booking/' . $request->id);
        } else {
            $this->helper->one_time_message('error', trans('messages.error.booking_expire_error'));
            return redirect('booking/' . $request->id);
        }
    }

    /**
     * User Booking List
     * User Booking Sort
     *
     */

    public function bookingRequests(Request $request)
    {
        switch ($request->status) {
            case 'Expired':
                $params = [['created_at', '<', Carbon::yesterday()], ['status', '!=', 'Accepted']];
                break;
            case 'Current':
                $params = [['start_date', '<=', date('Y-m-d')], ['end_date', '>=', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Upcoming':
                $params = [['start_date', '>', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Completed':
                $params = [['end_date', '<', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Pending':
                $params = [['created_at', '>', Carbon::yesterday()], ['status', $request->status]];
                break;
            default:
                $params = [];
                break;
        }

        $data['yesterday'] = Carbon::yesterday();
        $data['status'] = $request->status;
        $data['title'] = customTrans('reservation.bookings_requests');
        $data['bookings'] = Bookings::with(['users', 'properties'])
            ->whereHas('users')
            ->where('host_id', Auth::user()->id)
            ->whereIn('status', ['Pending', 'Accepted'])
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            ->orderBy('id', 'desc')
            ->paginate(10);
        if ($this->helper->isRequestFromMobile($request)) {
            return redirect()->route('mobile.hostbookings');
        } else {
            return view('booking.booking-requests', $data);
        }
        // return view('booking.booking-requests', $data);
    }

    public function hostUpcomingBooking(Request $request)
    {
        $data['title'] = "Upcoming Bookings";
        $data['upcomingBooking'] = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->where('status', 'Accepted')
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            ->orderBy('id', 'desc')
            ->paginate(10);

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.booking.host-upcoming-booking', $data);
        } else {
            return view('booking.host-upcoming-booking', $data);
        }
        return view('booking.host-upcoming-booking', $data);
    }

    public function hostOngoingBooking(Request $request)
    {
        $data['title'] = "Ongoing Bookings";
        $data['ongoingBooking'] = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
            ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
            ->where('status', 'Accepted')
            ->orderBy('updated_at', 'desc')
            ->paginate(10);

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.booking.host-ongoing-booking', $data);
        } else {
            return view('booking.host-ongoing-booking', $data);
        }
        return view('booking.host-ongoing-booking', $data);
    }

    public function hostCancelledBooking(Request $request)
    {
        $data['title'] = "Cancelled Bookings";
        $data['cancelledBooking'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where(fn($q) => $q->where('status', '=', 'Cancelled')
                ->orWhere('status', '=', 'Declined'))
            ->orderBy('updated_at', 'desc')
            ->paginate(10);


        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.booking.host-cancelled-booking', $data);
        } else {
            return view('booking.host-cancelled-booking', $data);
        }
        return view('booking.host-cancelled-booking', $data);
    }

    public function hostExpiredBooking(Request $request)
    {
        $data['title'] = "Expired Bookings";
        $data['expiredBooking'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where('status', 'Expired')
            ->orderBy('updated_at', 'desc')
            ->paginate(10);

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.booking.host-expired-booking', $data);
        } else {
            return view('booking.host-expired-booking', $data);
        }
    }

    public function hostHistoryBooking(Request $request)
    {
        $data['title'] = "Host History Bookings";
        $data['historyBooking'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->orderBy('id', 'desc')->paginate(10);

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.booking.host-history-booking', $data);
        } else {
            return view('booking.host-history-booking', $data);
        }
    }

    public function hostCancel(Request $request, EmailController $email)
    {
        $bookings = Bookings::find($request->id);
        $now = new DateTime();
        $booking_end = new DateTime($bookings->end_date);

        if ($now < $booking_end) {

            $properties = Properties::find($bookings->property_id);
            $payount = Payouts::where(['user_id' => $bookings->host_id, 'booking_id' => $request->id])->first();

            if (isset($payount->id)) {
                $payout_penalties = PayoutPenalties::where('payout_id', $payount->id)->get();
                if (!empty($payout_penalties)) {
                    foreach ($payout_penalties as $key => $payout_penalty) {
                        $prv_penalty = Penalty::where('id', $payout_penalty->penalty_id)->first();
                        $update_amount = $prv_penalty->remaining_penalty + $payout_penalty->amount;
                        Penalty::where('id', $payout_penalty->penalty_id)->update(['remaining_penalty' => $update_amount, 'status' => 'Pending']);
                    }
                }
            }

            $payouts = new Payouts;
            $payouts->booking_id = $request->id;
            $payouts->property_id = $bookings->property_id;
            $payouts->user_id = $bookings->user_id;
            $payouts->user_type = 'guest';
            $payouts->amount = $bookings->original_total;
            $payouts->currency_code = $bookings->currency_code;
            $payouts->penalty_amount = 0;
            $payouts->status = 'Future';
            $payouts->save();

            if ($bookings->booking_type != 'instant' || $request->cancel_reason != 'i_am_uncomfortable_with_guest') {
                $start_date = new DateTime($bookings->start_date);
                $panalty_date = new DateTime(date('Y-m-d H:i:s', strtotime('-7 days')));
                $fees = PropertyFees::pluck('value', 'field')->toArray();
                if ($start_date >= $panalty_date) {
                    //more then 7 days
                    $panalty = new Penalty;
                    $panalty->booking_id = $request->id;
                    $panalty->property_id = $bookings->property_id;
                    $panalty->user_id = Auth::user()->id;
                    $panalty->user_type = 'Host';
                    $panalty->currency_code = $bookings->currency_code;
                    $panalty->amount = $fees['more_then_seven'];
                    $panalty->remaining_penalty = $fees['more_then_seven'];
                    $panalty->reason = 'cancelation';
                    $panalty->save();
                } else {
                    //less then 7 days
                    $panalty = new Penalty;
                    $panalty->booking_id = $request->id;
                    $panalty->property_id = $bookings->property_id;
                    $panalty->user_id = Auth::user()->id;
                    $panalty->user_type = 'Host';
                    $panalty->currency_code = $bookings->currency_code;
                    $panalty->amount = $fees['less_then_seven'];
                    $panalty->remaining_penalty = $fees['less_then_seven'];
                    $panalty->reason = 'cancelation';
                    $panalty->save();
                }
            }
            // else {
            $days = $this->helper->get_days($bookings->start_date, $bookings->end_date);

            for ($j = 0; $j < count($days) - 1; $j++) {
                PropertyDates::where('property_id', $bookings->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
            }
            // }

            Payouts::where(['user_id' => $bookings->host_id, 'booking_id' => $request->id])->delete();

            $messages = new Messages;
            $messages->property_id = $bookings->property_id;
            $messages->booking_id = $bookings->id;
            $messages->receiver_id = $bookings->user_id;
            $messages->sender_id = Auth::user()->id;
            $messages->message = $request->cancel_message;
            $messages->type_id = 3;
            $messages->save();

            $cancel = Bookings::find($request->id);
            $cancel->cancelled_by = "Host";
            $cancel->cancelled_at = date('Y-m-d H:i:s');
            $cancel->status = "Cancelled";
            $cancel->save();

            BookingStatusLog::updateOrCreate(
                ['booking_id' => $cancel->id],
                ['status' => $cancel->status, 'changed_by' => Auth::id()]
            );

            $booking_details = new BookingDetails;
            $booking_details->booking_id = $request->id;
            $booking_details->field = 'cancelled_reason';
            $booking_details->value = $request->cancel_reason;
            $booking_details->save();
            if (isset(Auth::user()->email)) {
                $email->bookingCancellation($request->id);
            }
            $this->helper->one_time_message('success', trans('messages.success.resere_cancel_success'));
            clearCache('.calc.property_price');
            return redirect('incoming/booking/request');
        } else {
            $this->helper->one_time_message('danger', trans('messages.error.you_cant_cancel_booking_now'));
            return redirect('incoming/booking/request');
        }
    }

    function CancellationPolicy(Request $request)
    {
        $User = User::find(Auth::id());
        $User->cancel_policy = $request->cancel_policy;
        $User->save();
        return redirect('/cancellation_policy')->with('success', 'Cancellation Policy Changed Successfully');
    }

    function Store_GuestRequirement(Request $request)
    {

        $User = User::find(Auth::id());
        $User->guest_req = ($request->guest_req == null ? "No" : "Yes");
        $User->save();
        return redirect('/guest_requirements')->with('success', 'Guest Requirement Changed Successfully');
    }

    public function moreReservation(Request $request)
    {
        // dd($request->all());
        if ($request->tab == "reserv-history") {
            $data['bookings'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
                ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->orderBy('id', 'desc')->skip(request('offset'))->take(10)->get();

            $data['status'] = "history";
            if ($this->helper->isRequestFromMobile($request)) {
                $view = view('mobile.pagination.reservation', $data)->render();
            } else {
                $view = view('pagination.reservation', $data)->render();
            }
            // $view = view('pagination.reservation',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'history']);
        } elseif ($request->tab == "reserv-ongoing") {
            $data['bookings'] = Bookings::with('users', 'properties')
                ->where('user_id', auth()->id())
                ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted')
                ->orderBy('updated_at', 'desc')
                ->skip(request('offset'))->take(10)->get();
            $data['status'] = "ongoing";
            if ($this->helper->isRequestFromMobile($request)) {
                $view = view('mobile.pagination.reservation', $data)->render();
            } else {
                $view = view('pagination.reservation', $data)->render();
            }

            // $view = view('pagination.reservation',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'ongoing']);
        } elseif ($request->tab == "reserv-cancelled") {
            $data['bookings'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
                ->where(fn($q) => $q->where('status', '=', 'Cancelled')->orWhere('status', '=', 'Declined'))->orderBy('updated_at', 'desc')->skip(request('offset'))->take(10)->get();
            $data['status'] = "cancelled";
            if ($this->helper->isRequestFromMobile($request)) {
                $view = view('mobile.pagination.reservation', $data)->render();
            } else {
                $view = view('pagination.reservation', $data)->render();
            }
            // $view = view('pagination.reservation',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'cancelled']);
        } elseif ($request->tab == "reserv-expired") {
            $data['bookings'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
                ->where(fn($q) => $q->where('status', '=', 'Expired'))->orderBy('updated_at', 'desc')->skip(request('offset'))->take(10)->get();
            $data['status'] = "expired";
            if ($this->helper->isRequestFromMobile($request)) {
                $view = view('mobile.pagination.reservation', $data)->render();
            } else {
                $view = view('pagination.reservation', $data)->render();
            }
            // $view = view('pagination.reservation',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'expired']);
        } elseif ($request->tab == "reserv-upcoming") {
            $data['bookings'] = Bookings::with('users', 'properties')
                ->where('user_id', auth()->id())
                ->whereIn('status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
                ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                ->orderBy('id', 'desc')
                ->skip(request('offset'))->take(10)->get();
            $data['status'] = "upcoming";
            if ($this->helper->isRequestFromMobile($request)) {
                $view = view('mobile.pagination.upcomingReservation', $data)->render();
            } else {
                $view = view('pagination.upcomingReservation', $data)->render();
            }

            // $view = view('pagination.upcomingReservtion',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'upcoming']);
        }
    }


    public function deleteHoldBookingDates(Request $request)
    {
        $bookingOnHoldDates = PropertyDates::where('booking_id', $request->bookingId)->delete();
        return response()->json(["message" => "Success"]);
    }

    public function hostBookings(Request $request)
    {
        $data['upcomingBooking'] = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->where('status', 'Accepted')
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            ->orderBy('id', 'desc')
            ->paginate(10);

        $data['cancelledBooking'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where(fn($q) => $q->where('status', '=', 'Cancelled')
                ->orWhere('status', '=', 'Declined'))
            ->orderBy('updated_at', 'desc')
            ->paginate(10);

        $data['ongoingBooking'] = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
            ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
            ->where('status', 'Accepted')
            ->orderBy('updated_at', 'desc')
            ->paginate(10);


        $data['historyBooking'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->orderBy('id', 'desc')->paginate(10);

        $data['expiredBooking'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where('status', 'Expired')
            ->orderBy('updated_at', 'desc')
            ->paginate(10);

        $data['yesterday'] = Carbon::yesterday();
        $data['status'] = $request->status;
        $data['title'] = customTrans('reservation.bookings_requests');
        $data['bookings'] = Bookings::with(['users', 'properties'])
            ->whereHas('users')
            ->where('host_id', Auth::user()->id)
            ->whereIn('status', ['Pending', 'Accepted'])
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            ->orderBy('id', 'desc')
            ->paginate(10);

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.booking.host_bookings', $data);
        }
    }

    public function hostMoreReservation(Request $request)
    {
        if ($request->tab == "historyBookings") {
            $data['bookings'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
                ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->orderBy('id', 'desc')->skip(request('offset'))->take(10)->get();

            $data['status'] = "history";

            $view = view('mobile.pagination.host_reservation', $data)->render();
            // $view = view('pagination.reservation',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'history']);
        } else if ($request->tab == "cancelledBookings") {
            $data['bookings'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
                ->where(fn($q) => $q->where('status', '=', 'Cancelled')
                    ->orWhere('status', '=', 'Declined'))
                ->orderBy('updated_at', 'desc')
                ->skip(request('offset'))->take(10)->get();
            $data['status'] = "cancelled";

            $view = view('mobile.pagination.host_reservation', $data)->render();

            // $view = view('pagination.upcomingReservtion',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'cancelled']);
        } else if ($request->tab == "expiredBookings") {
            $data['bookings'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
                ->where('status', 'Expired')
                ->orderBy('updated_at', 'desc')
                ->skip(request('offset'))->take(10)->get();
            $data['status'] = "expired";

            $view = view('mobile.pagination.host_reservation', $data)->render();

            // $view = view('pagination.upcomingReservtion',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'expired']);
        } else if ($request->tab == "ongoingBookings") {

            $data['bookings'] = Bookings::with('users', 'properties')
                ->where('host_id', Auth::user()->id)
                ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted')
                ->orderBy('updated_at', 'desc')
                ->skip(request('offset'))->take(10)->get();
            $data['status'] = "ongoing";
            // dd($data['bookings'] );
            $view = view('mobile.pagination.host_reservation', $data)->render();

            // $view = view('pagination.upcomingReservtion',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'ongoing']);
        } else if ($request->tab == "requestBookings") {
            $data['bookings'] = Bookings::with(['users', 'properties'])
                ->whereHas('users')
                ->where('host_id', Auth::user()->id)
                ->whereIn('status', ['Pending', 'Accepted'])
                ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                ->orderBy('id', 'desc')
                ->skip(request('offset'))->take(10)->get();
            $data['status'] = "request";

            $view = view('mobile.pagination.host_request_booking', $data)->render();
            // $view = view('pagination.reservation',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'request']);
        } else if ($request->tab == "upcomingsBookings") {
            $data['bookings'] = Bookings::with('users', 'properties')
                ->where('host_id', Auth::user()->id)
                ->whereIn('status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
                ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                ->orderBy('id', 'desc')
                ->skip(request('offset'))->take(10)->get();
            $data['status'] = "upcoming";

            $view = view('mobile.pagination.host_reservation', $data)->render();

            // $view = view('pagination.upcomingReservtion',$data)->render();
            return response()->json(['bookings' => $view, 'tab' => 'upcoming']);
        }
    }

    function removePromo(Request $request, $booking_id)
    {
        $booking = Bookings::select('bookings.*')->leftJoin('promo_code_usage AS pcus', fn($q) => $q->on('pcus.booking_id', 'bookings.id')->where('is_used', 0))
            ->where(function ($q) {
                $q->where('bookings.status', '=', 'Processing')
                    ->orWhere('bookings.status', '=', 'Unpaid');
            })
            ->where('bookings.user_id', auth()->id())
            ->where('bookings.id', $booking_id)
            ->orderBy('pcus.id', 'DESC')
            ->firstOrFail();

        $booking->update([
            'total_with_discount' => 0,
            'base_price_with_discount' => 0,
            'total_discount' => 0
        ]);
        PromoCodeUsage::where('booking_id', $booking_id)->update([
            'is_removed' => 1,
            'remove_time' => now(),
            'deleted_at' => now()
        ]);
        $data = ['message' => 'Promo removed successfully', 'status' => true];
        if ($request->is('api/*')) {
            $data = ['data' => $data, 'success' => true];
        }
        return response()->json($data);
    }

    public function getBooking($id)
    {
        return $id;
    }
}
