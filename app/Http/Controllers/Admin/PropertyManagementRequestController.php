<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\{PropertyManagementRequest,City};
use App\DataTables\PropertyManagementDataTable;
use Carbon\Carbon;
use App\Http\Helpers\Common;
use Illuminate\Support\Facades\Validator;


class PropertyManagementRequestController extends Controller
{
    protected $base_route;
    protected $helper;

    public function __construct()
    {
        $this->base_route = 'admin.management_requests.';
        $this->helper = new Common;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(PropertyManagementDataTable $dataTable)
    {
        $data['total_requests'] = PropertyManagementRequest::count();
        $data['total_requests_today'] = PropertyManagementRequest::whereDate('created_at',Carbon::today())->count();
        $data['from'] = isset(request()->from) ?  request()->from: null;
        $data['to'] = isset(request()->to) ? request()->to : null;
        return $dataTable->render($this->base_route . 'view',$data);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try{
            $messages = array(
                'required'                => ':attribute is required.',
            );
            $validator = Validator::make($request->all(), [
                'owner_name'     => 'required|string|max:255',
                'email'          => 'required|email|max:255',
                'property_name'  => 'required|string|max:255',
                'units'          => 'required|integer|min:0',
                'address_line_1' => 'required|string|max:255',
                'city_id'        => 'required|exists:cities,id',
                'contact_no'     => 'required|string|max:20',
            ],$messages);
        
            if ($validator->fails()) {
                return ['code' => 500, 'status' => false, 'message'=> 'Error submitting request'];
            }
            $request->merge([
                'location' => $request->address_line_1,
            ]);
            $property = PropertyManagementRequest::create($request->all());
            return ['code' => 200, 'status' => true, 'message'=> 'Request submitted successfully'];

        } catch(\Exception $e) {
            return ['code' => 500, 'status' => false, 'message'=> 'Error submitting request'];

        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data['cities'] = City::all();
        $data['management'] = PropertyManagementRequest::findOrFail($id);

        return view($this->base_route . 'edit',$data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try{
            $validator = Validator::make($request->all(), [
                'owner_name'     => 'required|string|max:255',
                'email'          => 'required|email|max:255',
                'property_name'  => 'required|string|max:255',
                'units'          => 'required|integer|min:0',
                'address_line_1' => 'nullable|string|max:255',
                'city_id'        => 'required|exists:cities,id',
                'contact_no'     => 'required|string|max:20',
            ]);
        
            if ($validator->fails()) {
                return redirect()
                    ->back()
                    ->withErrors($validator)
                    ->withInput();
            }
            $property = PropertyManagementRequest::findOrFail($id);
            $request->merge([
                'location' => $request->address_line_1,
            ]);
            $property->update($request->all());
            $this->helper->one_time_message('success', 'Request updated successfully');
            return redirect()->route('admin.property-management-requests.index');
        } catch(\Exception $e) {
            dd($e);
            $this->helper->one_time_message('error', 'Unable to update request. Please try again later.');
            return back();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try{
            $prop = PropertyManagementRequest::findOrFail($id);
            $prop->delete();
            $this->helper->one_time_message('success', 'Request deleted successfully');
            return back();
        } catch(\Exception $e) {
            $this->helper->one_time_message('error', 'Unable to delete request. Please try again later.');
            return back();
        }
    }
}
