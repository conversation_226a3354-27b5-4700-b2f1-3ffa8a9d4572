<?php

/**
 * Reviews Controller
 *
 * Reviews Controller manages Reviews by admin.
 *
 * @category   Reviews
 * @package    darent
 * <AUTHOR> Dev Team
 * @copyright Darent 
 * @license
 * @version    2.7
 * @link       http://darent.com
 * @email      <EMAIL>
 * @since      Version 1.3
 * 
 */

namespace App\Http\Controllers\Admin;

use App\DataTables\ReviewsDataTable;
use App\Http\Controllers\Controller;
use App\Http\Helpers\Common;
use App\Models\Properties;
use App\Models\User;
use App\Models\Reviews;
use App\Models\Settings;
use Carbon\Carbon;
use Modules\Reviews\Services\ReviewsService;
use PDF;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\Request;
use Validator;
use App\Notifications\UserNotify;


class ReviewsController extends Controller
{
    protected $helper;
    private ReviewsService $_review_srv;
    public function __construct(ReviewsService $reviewsService)
    {
        $this->helper = new Common;
        $this->_review_srv =$reviewsService;
    }

    public function index(ReviewsDataTable $dataTable)
    {
        if (isset($_GET['reset_btn'])) {
            $data['from']          = null;
            $data['to']            = null;
            $data['allreviewer']   = '';
            $data['allproperties'] = '';
            return redirect('/admin/reviews');
            // return $dataTable->render('admin.reviews.view', $data);
        }
        $data['from'] = isset($_GET['from']) ? $_GET['from'] : null;
        $data['to']   = isset($_GET['to']) ? $_GET['to'] : null;
        $data['property'] = Properties::get();

        if (isset($_GET['property'])) {
            $data['properties'] = $properties = Properties::where('properties.id', $_GET['property'])->select('id', 'name')->get();
        } else {
            $data['properties'] = null;
        }

        isset($_GET['property']) ? $data['allproperties'] = $allproperties = $_GET['property'] : $data['allproperties'] = $allproperties = '';
        isset($_GET['reviewer']) ? $data['allreviewer'] = $allreviewer = $_GET['reviewer'] : $data['allreviewer'] = $allreviewer = '';
        return $dataTable->render('admin.reviews.view', $data);
    }

    public function fakeReview(Request $request)
    {
        if ($request->isMethod('post')) {

            // dd($request->all());

            $validator = Validator::make(
                $request->all(),
                [
                    'message' => 'required|string',
                    'guest_id' => 'required',
                    'property_id' => 'required',
                    'accuracy' => 'required',
                    'communication' => 'required',
                    'cleanliness' => 'required',
                    'location' => 'required',
                    'darent_service' => 'nullable',
                    'darent_recom' => 'nullable',
                ],
                [
                    'message.required' => 'Review Message Required.',
                    'guest_id.required' => 'Guest is Required.',
                    'property_id.required' => 'Property is Required.',
                    'accuracy.required' => 'Accuracy is Required.',
                    'communication.required' => 'Communication is Required.',
                    'cleanliness.required' => 'Cleanliness is Required.',
                    'location.required' => 'Location is Required.',
                    'darent_service.required' => 'Darent Service is Required.',
                    'darent_recom.required' => 'Darent Recommentation is Required.',
                ]
            );

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            } else {

                $prop = Properties::find($request->property_id);
                $UserById = User::find($request->guest_id);

                $review                 = new Reviews;
                // $review->rating          = $request->rating;
                $review->reviewer        = 'guest';
                $review->property_id     = $request->property_id;

                $review->sender_id      = $request->guest_id;

                $review->sender_id      = isset($request->guest_id) ? $request->guest_id : 0;
                $review->guest_name      = $UserById->first_name .' '.$UserById->last_name;



            $review->receiver_id    = $prop->host_id;

            $review->accuracy        = $request->accuracy;
            $review->location        = $request->location ?? 0;
            $review->communication   = $request->communication;
            $review->checkin         = $request->checkin ?? 0;
            $review->cleanliness     = $request->cleanliness;
            $review->darent_service     = $request->darent_service ?? 0;
            $review->darent_recomended  = $request->darent_recom ?? 0;
            $review->value           = $request->value ?? 0;
            $review->ispublic        = $request->publish;
            $review->message         = $request->message;
            $review->review_date     = isset($request->created_at) ? Carbon::parse($request->created_at)->format('Y-m-d H:i:s') : Carbon::now()->format('Y-m-d H:i:s');

            $review->rating = number_format(($request->communication + $request->accuracy + $review->cleanliness + $review->location ),2) / 4 ;
            // dd($request->all());
            $review->save();
            $this->helper->getLogs($review, 'admin');


                $this->helper->one_time_message('success', 'Insert Successfully');
                return redirect('admin/reviews');
            }

        }else{

            $data['properties'] = Properties::where(['deleted_at' => null, ])->get();
            $data['users'] = User::where('status', 'Active')->orderBy('id', 'desc')->get();

            return view('admin.reviews.addFake', $data);
        }
    }

    public function edit(Request $request)
    {
        $data['result'] = Reviews::join('properties', function ($join) {
            $join->on('properties.id', '=', 'reviews.property_id');
        })
            ->join('users', function ($join) {
                $join->on('users.id', '=', 'reviews.sender_id');
            })
            ->join('users as receiver', function ($join) {
                $join->on('receiver.id', '=', 'reviews.receiver_id');
            })
            ->where('reviews.id', $request->id)
            ->select(['reviews.id as id', 'booking_id', 'properties.name as property_name', 'reviews.sender_id',
                'users.first_name as sender', 'receiver.first_name as receiver', 'reviewer', 'rating', 'accuracy', 'reviews.location', 'communication', 'checkin', 'cleanliness', 'value', 'message', 'reviews.created_at','reviews.darent_service as darent_service','reviews.darent_recomended as darent_recomended', 'properties.property_code', 'ispublic'])->first();

        abort_if(!$data['result'], 404);

        // $data['result']->accuracy = $data['result']->accuracy / 2;
        // $data['result']->location = $data['result']->location / 2;
        // $data['result']->communication = $data['result']->communication / 2;
        // $data['result']->checkin = $data['result']->checkin / 2;
        // $data['result']->cleanliness = $data['result']->cleanliness / 2;
        // $data['result']->value = $data['result']->value / 2;
        // $data['result']->darent_service = $data['result']->darent_service / 2;
        // $data['result']->darent_recomended = $data['result']->darent_recomended / 2;

        $data['users'] = User::where('status', 'Active')->orderBy('id', 'desc')->get();
        return view('admin.reviews.edit', $data);
    }
    public function update(Request $request)
    {
        
        // $rules = array(
        // 'message' => 'required|string',
        //         'guest_id' => 'required',
        //         'property_id' => 'required',
        //         'accuracy' => 'required',
        //         'communication' => 'required',
        //         'cleanliness' => 'required',
        //         'darent_service' => 'required',
        //         'darent_recom' => 'required',
        // );

        // $niceNames = array(
        //     'message' => 'Message'
        // );

        // $validator = Validator::make($request->all(), $rules);
        // $validator->setAttributeNames($niceNames);

        $validator = Validator::make(
            $request->all(),
            [
                'message' => 'required|string',
                'guest_id' => 'required',
                'accuracy' => 'required',
                'communication' => 'required',
                'cleanliness' => 'required',
                'location' => 'required',
                // 'darent_service' => 'required',
                // 'darent_recomended' => 'required',
            ],
            [
                'message.required' => 'Review Message Required.',
                'guest_id.required' => 'Guest is Required.',
                'accuracy.required' => 'Accuracy is Required.',
                'communication.required' => 'Communication is Required.',
                'cleanliness.required' => 'Cleanliness is Required.',
                'location.required' => 'Location is Required.',
                'darent_service.required' => 'Darent Service is Required.',
                'darent_recomended.required' => 'Darent Recommentation is Required.',
            ]
        );

        if ($validator->fails())  return back()->withErrors($validator)->withInput();
        $reviewById = Reviews::find($request->id);

        
        $data=[
            "sender_id"=>$request->guest_id??0,
            "message"=>$request->message,
            "accuracy"=>$request->accuracy,
            "location"=>$request->location ?? 0,
            "communication"=>$request->communication,
            "checkin"=>$request->checkin ?? 0,
            "cleanliness"=>$request->cleanliness,
            "darent_service"=>$request->darent_service ?? 0,
            "darent_recomended"=>$request->darent_recomended ?? 0,
            "value"=>$request->value ?? 0,
            "ispublic"=>$request->publish,
            "review_date"=>Carbon::parse($request->created_at)->format('Y-m-d H:i:s'),
        ];
        // dd($data);
        $review = $this->_review_srv->update($request->id,$data);
        
        // Log::debug('check', ["{$type}.review.booking.{$other}"]);
        (User::find($reviewById->receiver_id))->notify(new UserNotify("guest.review.booking.host", route('userreview'), data: [
            'slug' => "guest_review"
        ]));
        

        $this->helper->one_time_message('success', 'Updated Successfully');
        return redirect()->route('admin.review_list');
    }
    public function searchReview(Request $request)
    {

        $str = $request->term;
        if ($str == null) {

            $myresult = Reviews::with(['properties' => function ($query) {
                $query->select('id', 'name as text');
            }])->distinct()->get(['property_id']);
        } else {
            $myresult = Reviews::with(['properties' => function ($query) use ($str) {
                $query->where('name', 'LIKE', '%' . $str . '%')
                    ->select('id', 'name as text');
            }])->get();
        }
        $arr2 = array(
            "id" => "",
            "text" => "All"
        );

        $myArr[] = ($arr2);
        foreach ($myresult as $result) {
            if ($result->properties != null) {
                $arr = array(
                    "id" => $result->properties->id,
                    "text" => $result->properties->text
                );
                $myArr[] = ($arr);
            }
        }

        return $myArr;
    }

    public function reviewCsv()
    {
        $to                 = $_GET['to'];
        $from               = $_GET['from'];
        $reviewer = isset($_GET['reviewer']) ? $_GET['reviewer'] : 'null';
        $property = isset($_GET['property']) ? $_GET['property'] : 'null';
        $reviews           = $this->getAllReviews();
        if ($from) {
            $reviews->whereDate('reviews.created_at', '>=', $from);
        }
        if ($to) {
            $reviews->whereDate('reviews.created_at', '<=', $to);
        }
        if ($property) {
            $reviews->where('properties.id', '=', $property);
        }
        if ($reviewer) {
            $reviews->where('reviews.reviewer', '=', $reviewer);
        }
        $reviewList = $reviews->get();
        if ($reviewList->count()) {
            foreach ($reviewList as $key => $value) {
                $data[$key]['Property Name'] = $value->property_name;
                $data[$key]['Sender']        = $value->sender;
                $data[$key]['Receiver']      = $value->receiver;
                $data[$key]['Reviewer']      = $value->reviewer;
                $data[$key]['Message']       = $value->message;
                $data[$key]['Date']          = dateFormat($value->created_at);
            }
        } else {
            $data = null;
        }
        return Excel::create('review_sheet' . time() . '', function ($excel) use ($data) {
            $excel->sheet('mySheet', function ($sheet) use ($data) {
                $sheet->fromArray($data);
            });
        })->download('xls');
    }

    public function reviewPdf()
    {
        $to                 = $_GET['to'];
        $from               = $_GET['from'];
        $data['companyLogo']  = $logo  = Settings::where('name', 'logo')->select('value')->first();
        if ($logo->value == null) {
            $data['logo_flag'] = 0;
        } elseif (!file_exists("public/images/logos/$logo->value")) {
            $data['logo_flag'] = 0;
        }
        $data['reviewer']     = $reviewer = isset($_GET['reviewer']) ? $_GET['reviewer'] : 'null';
        $data['property']     = $property = isset($_GET['property']) ? $_GET['property'] : 'null';
        $reviews              = $this->getAllReviews();
        if ($from) {
            $reviews->whereDate('reviews.created_at', '>=', $from);
        }
        if ($to) {
            $reviews->whereDate('reviews.created_at', '<=', $to);
        }
        if ($property) {
            $reviews->where('properties.id', '=', $property);
        }
        if ($reviewer) {
            $reviews->where('reviews.reviewer', '=', $reviewer);
        }
        if ($from && $to) {
            $data['date_range'] = $from . ' To ' . $to;
        }

        $data['reviewList'] = $reviews->get();
        $pdf = PDF::loadView('admin.reviews.list_pdf', $data, [], [
            'format' => 'A3', [750, 1060]
        ]);
        return $pdf->download('review_list_' . time() . '.pdf', array("Attachment" => 0));
    }

    public function getAllReviews()
    {
        $reviews = Reviews::join('properties', function ($join) {
            $join->on('properties.id', '=', 'reviews.property_id');
        })
            ->join('users', function ($join) {
                $join->on('users.id', '=', 'reviews.sender_id');
            })
            ->join('users as receiver', function ($join) {
                $join->on('receiver.id', '=', 'reviews.receiver_id');
            })
            ->select(['reviews.id as id', 'booking_id', 'properties.name as property_name', 'properties.id as property_id', 'users.first_name as sender', 'receiver.first_name as receiver', 'reviewer', 'message', 'reviews.created_at as created_at', 'reviews.updated_at as updated_at'])
            ->orderBy('reviews.id', 'desc');
        return $reviews;
    }
}
