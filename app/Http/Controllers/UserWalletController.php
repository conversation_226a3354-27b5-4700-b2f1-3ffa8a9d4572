<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Helpers\Common;

use App\Models\{
    NewUserWallet,
    User,
    UserCards,
    Transactions,
    UserWallet
};

class UserWalletController extends Controller
{
    protected $helper;

    public function __construct()
    {
        $this->helper = new Common;
    }
    public function wallet(Request $request)
    {


        // return $request;
        // GET USER CARDS
        $data['title'] = customTrans('wallet.my_wallet');
        $data['user_cards'] = UserCards::where(['user_id' => auth()->id(), 'is_verified' => 1])->get();
        $data['verified_card'] = UserCards::where(['user_id' => auth()->id(), 'is_verified' => 1, 'is_default' => 1])->first();

        //GET USER TRANSACTIONS
        $data['user_transactions'] = Transactions::where('user_id', auth()->id())
        ->with('transaction_types', 'transaction_categories')
        ->orderBy('id', 'desc')
        ->get(['id', 'amount', 'transaction_category_id', 'transaction_type_id', 'created_at']);
        //GET USER WALLET (IF NOT EXIST, CREATE)
        // $user_wallet = UserWallet::where('user_id', auth()->id())->first();
        $new_user_wallet = NewUserWallet::where('user_id', auth()->id())->firstOrCreate(
            array(
                'user_id' => auth()->id()
            ),
            array(
                'balance' => 0,
                'is_active' => 1
            )
        );

        // $data['user_wallet'] = $user_wallet;
        $data['new_user_wallet'] = $new_user_wallet;
        $userAgent = $request->header('User-Agent');
        $data['showPayOpts'] = strpos($userAgent, 'Safari') == true && strpos($userAgent, 'Chrome') == false && (strpos($userAgent, 'Macintosh') == true || strpos($userAgent, 'iPhone') == true);

        if ($request->isMethod('post')) {
            return $request->post();
        } else {

            if ($this->helper->isRequestFromMobile($request)) {
                return view('mobile.payment.user_wallet', $data);
            } else {
                return view('payment.user_wallet', $data);
            }
        }
    }

    public function makeDefualtCard(Request $request)
    {

        //first disable previous
        $getcard = UserCards::where(['user_id' => auth()->id(), 'is_default' => 1])->first();
        $getcard ?  $getcard->update(['is_default' => 0]) : '';

        //make default newone
        $a = UserCards::where(['user_id' => auth()->id(), 'id' => $request->cardid])->first();
        $a->update(['is_default' => 1]);

        if (auth()->user()->fcm_token) {
            $this->helper->sendPushNotification(auth()->user()->fcm_token, "Your " . $a->CardNumberHidden . " card has been set as default", null, "card_default");
        }
        return 1;
    }

    public function deleteCard(Request $request)
    {

        //Delete Card if its default then make defualt previous
        $IsdefaultCardExist = UserCards::where(['user_id' => auth()->id(), 'is_default' => 1, 'id' => $request->cardid])->first();

        if ($IsdefaultCardExist) {
            $IsdefaultCardExist->delete();
            $othercard = UserCards::where(['user_id' => auth()->id(), 'is_verified' => 1])->latest()->first();

            // if other cards exist then pick one latest card and make it default.
            if ($othercard) {
                $othercard->update(['is_default' => 1]);
            }

            return 1;
        } else {

            //Delete Card
            UserCards::where(['user_id' => auth()->id(), 'id' => $request->cardid])->first()->delete();
            return 1;
        }
    }
    public function all_transaction(Request $request)
    {
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        $data['title'] = 'All Transaction';

        $data['title'] = customTrans('wallet.my_wallet');
        $data['user_cards'] = UserCards::where(['user_id' => auth()->id(), 'is_verified' => 1])->get();
        $data['verified_card'] = UserCards::where(['user_id' => auth()->id(), 'is_verified' => 1, 'is_default' => 1])->first();

        //GET USER TRANSACTIONS
        $data['user_transactions'] = Transactions::where('user_id', auth()->id())
        ->with('transaction_types', 'transaction_categories')
        ->when(app()->environment('local'), function ($query) {
            return $query->where('status', 'success');
        })
        ->orderBy('id', 'desc')->paginate(5);

        //GET USER WALLET (IF NOT EXIST, CREATE)
        $user_wallet = UserWallet::where('user_id', auth()->id())->first();
        $new_user_wallet = NewUserWallet::where('user_id', auth()->id())->first();

        if ($user_wallet == null) {
            $wallet_data = [
                'user_id'   => auth()->id(),
                'balance'   => 0,
                'is_active' => 1,
            ];
            $user_wallet = UserWallet::create($wallet_data);
        }

        $data['user_wallet'] = $user_wallet;
        $data['new_user_wallet'] = $new_user_wallet;
        $userAgent = $request->header('User-Agent');
        $data['showPayOpts'] = strpos($userAgent, 'Safari') == true && strpos($userAgent, 'Chrome') == false && (strpos($userAgent, 'Macintosh') == true || strpos($userAgent, 'iPhone') == true);

        if ($request->isMethod('post')) {
            return $request->post();
        } else {

            if ($this->helper->isRequestFromMobile($request)) {
                return view('mobile.payment.all_transaction', $data);
            } else {
                return view('payment.all_transaction', $data);
            }
        }

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.payment.all_transaction', $data);
        } else {
            return view('payment.all_transaction', $data);
        }
    }

    public function loadMoreTransactions(Request $request)
    {
        $transactions = Transactions::where('user_id', auth()->id())
            ->with('transaction_types', 'transaction_categories')
            ->when(app()->environment('local'), function ($query) {
                return $query->where('status', 'success');
            })
            ->orderBy('id', 'desc')
            ->paginate(5, ['*'], 'page', $request->page);

        if ($request->ajax()) {
            if ($this->helper->isRequestFromMobile($request)) {
                return response()->json([
                    'html' => view('mobile.payment.partials.transaction_items', ['user_transactions' => $transactions])->render(),
                    'last_page' => $transactions->lastPage()
                ]);
            }
            return response()->json([
                'html' => view('payment.partials.transaction_items', ['transactions' => $transactions])->render(),
                'last_page' => $transactions->lastPage()
            ]);
        }

        return response()->json(['html' => '']);
    }
}
