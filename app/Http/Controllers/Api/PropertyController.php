<?php

namespace App\Http\Controllers\Api;

use App\Enums\PlatformTypeEnum;
use App\Enums\PropertyInquiryStatusTypeEnum;
use App\Helpers\ImageHelper;
use App\Http\Controllers\Controller;
use App\Http\Services\Mabaat\MabaatService;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use App\Http\Helpers\Common;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Notifications\UserNotify;
use App\Notifications\UserNotifySms;
use App\Http\Controllers\CalendarController;
use App\Http\Resources\BookmarkCollection;
use App\Http\Resources\UserPropertiesResource;
use App\Http\Resources\UserPropertiesCollection;
use App\Http\Resources\CompletePropertiesCollection;
use App\Http\Resources\IncompletePropertiesCollection;
use App\Http\Resources\HostReservationCollection;
use App\Http\Resources\AllReservationCollection;
use App\Http\Resources\CalendarPriceResource;
use App\Http\Resources\CalendarReservationResource;
use App\Http\Resources\HostPropertiesResource;
use App\Http\Resources\ReviewResource;
use App\Http\Resources\WebhookPropertiesResource;
use App\Http\Resources\YourReservationResource;
use Validator;
use Session;
use Image;

use App\Models\{
    Admin,
    Favourite,
    Properties,
    PropertyDetails,
    PropertyAddress,
    PropertyPhotos,
    PropertyPrice,
    PropertyType,
    PropertyDates,
    PropertyDescription,
    Currency,
    Settings,
    Bookings,
    SpaceType,
    BedType,
    PropertySteps,
    Country,
    Amenities,
    AmenityType,
    City,
    CustomPricing,
    DailyDiscount,
    NewUserWallet,
    User,
    Reviews,
    PropertiesTemp,
    PhotosTemp,
    PropertyDiscount,
    PropertyView,
    ScoringSystem
};
use App\Models\PropertyCacheResult;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class PropertyController extends Controller
{
    private $helper;
    public function __construct()
    {
        $this->helper = new Common;
    }
    public function AddPlace(Request $request)
    {
        try {
            if ($request->isMethod('post')) {
                // dd($request);
                $rules = array(
                    'property_type_id'  => 'required',
                );

                $validator = Validator::make($request->all(), $rules);

                if ($validator->fails()) {
                    return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);

                    // return back()->withErrors($validator)->withInput();
                } else {
                    $property                  = new Properties;
                    $property->host_id         =  Auth::id();     //Auth::id();
                    // $property->name            = SpaceType::getAll()->find($request->space_type)->name.' in '.$request->city;
                    $property->property_type   = $request->property_type_id;
                    $property->status = "Unlisted";
                    $property->space_type      = isset($request->space_type) ? $request->space_type : 0;
                    $property->accommodates    = isset($request->accommodates) ? $request->accommodates : 0;
                    $property->save();

                    $property_address                 = new PropertyAddress;
                    $property_address->property_id    = $property->id;
                    $property_address->save();

                    $property_price                 = new PropertyPrice;
                    $property_price->property_id    = $property->id;
                    $property_price->currency_code  = 'SAR';
                    $property_price->save();

                    $property_steps                   = new PropertySteps;
                    $property_steps->property_id      = $property->id;
                    $property_steps->save();

                    $property_description              = new PropertyDescription;
                    $property_description->property_id = $property->id;
                    $property_description->save();
                    return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property->id, 'property_code' => $property->property_code]], 200);

                    // return redirect('listing/'.$property->id.'/spacetype');
                }
            }
            $data['property_type'] = PropertyType::where('status', 'Active')->get();
            // $data['space_type']    = SpaceType::getAll()->where('status', 'Active')->pluck('name', 'id');
            return response()->json(['message' => 'Success', 'data' => $data], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
        // return view('listing.addplace', $data);
    }

    public function editPropertyWithAdminApprovalRequired(Request $request)
    {
        try {

            $step            = $request->step;
            $property_id     = $request->id;
            $data['step']    = $step;
            $data['result'] = $property  = isHostOrCohostQuery(Properties::query(), auth()->id(), conId: 'id')->findOrFail($property_id);
            $data['details'] = PropertyDetails::pluck('value', 'field');
            $data['missed']  = PropertySteps::where('property_id', $request->id)->first();


            if ($step == 'description') {
                if ($request->isMethod('post')) {

                    Log::debug("description", [$request->all()]);
                    $validate = Validator::make($request->all(), [
                        'summary'     => ['required','max:1000','min:20',   'regex:/^[a-zA-Z0-9.,\/\-\s!?;:\'\"\(\)“”’]+$/u'],
                        'summary_ar'  => ['required','max:1000','min:20',  'regex:/^[\p{Arabic}0-9\s,\/\-.!?;:\'\"\(\)“”’]+$/u'],
                        'edit'        => 'required',
                    ], [
                        'summary.required' => 'Description is Required',
                        'summary_ar.required' => 'Arabic Descrtiption is Required',
                        'edit.required' => 'Edit param required in binary (1 is request for edit property & 0 for add property)',
                    ]);

                    if ($validate->fails()) {

                        return response()->json(['message' => 'failure', 'error' => $validate->errors()], 422);

                        // return apiResponse(['error' => $validate->errors()], 'failure', 422);
                    } else {
                        if ($request->edit) {
                            if ($property->status == 'Unlisted') {
                                $property_description              = PropertyDescription::where('property_id', $property_id)->first();
                                $property_description->summary     = $request->summary;
                                $property_description->summary_ar  = $request->summary_ar;
                                $property_description->save();
                            } else {
                                $newSummary_ar = $property->property_description->summary_ar != $request->summary_ar ?  $request->summary_ar : null;
                                $newSummary = $property->property_description->summary != $request->summary ? $request->summary : null;



                                if ($newSummary_ar || $newSummary) {

                                    $temp = PropertiesTemp::firstOrNew(array('property_id' => $property_id));
                                    $temp->fill([
                                        'summary_ar' => $newSummary_ar,
                                        'summary' => $newSummary,
                                    ]);

                                    $temp->save();
                                }
                            }
                        } else {
                            $property_description              = PropertyDescription::where('property_id', $property_id)->first();
                            $property_description->summary     = $request->summary;
                            $property_description->summary_ar  = $request->summary_ar;
                            $property_description->save();
                        }
                        $property                     = Properties::find($property_id);
                        $property->accommodates       = '1';
                        $property->adult_guest        = '1';
                        $property->children_guest     = '0';
                        $this->helper->getLogs($property, 'api');
                        $property->save();
                        Log::info('Property accommodates Updated', ['property_id' => $property_id, 'summary' => $request->summary]);
                        $this->propertyStep($property_id, 'basics');


                        $this->propertyStep($property_id, 'description');
                        // $this->property_step($property_id, ['description']);


                        return apiResponse(['propertyid' => $property_id], 'Success', 200);
                    }
                }
            } elseif ($step == "title") {
                if ($request->isMethod('post')) {
                    $validate = Validator::make($request->all(), [
                        'edit'        => 'required',
                        'title' => ['required', 'string', 'regex:/^[a-zA-Z0-9.,\/\-\s]+$/'],
                        'title_ar' => ['required', 'string', 'regex:/^[\p{Arabic}0-9\s,\/\-.]+$/u'],
                    ], [
                        'edit.required' => 'Edit param required in binary (1 is request for edit property & 0 for add property)',
                        'title.required' => __('validation.title.required'),
                        'title.regex' => __('validation.title.regex'),
                        'title_ar.required' => __('validation.name_ar.required'),
                        'title_ar.regex' => __('validation.name_ar.regex'),
                    ]);

                    if ($validate->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validate->errors()], 422);
                    } else {
                        if ($request->edit) {
                            if ($property->status == 'Unlisted') {

                                $property = Properties::find($property_id);
                                $property->name = $request->title;
                                $property->name_ar = $request->title_ar;
                                if (empty($property->slug)) {
                                    $property->slug = $this->helper->pretty_url($property->name, $property->property_code);
                                }
                                $property->save();
                            } else {
                                $newTitle = $property->name != $request->title ? $request->title : null;
                                if ($newTitle) {
                                    $temp = PropertiesTemp::firstOrNew(array('property_id' => $property_id));
                                    $temp->fill([
                                        'name' => $newTitle,
                                        // 'slug' => $property->name != $request->title ? $this->helper->pretty_url($request->title) : null,
                                    ]);
                                    $temp->save();
                                }
                            }
                        } else {

                            $property = Properties::find($property_id);
                            $property->name = $request->title;
                            $property->name_ar = $request->title_ar;
                            if (empty($property->slug)) {
                                $property->slug = $this->helper->pretty_url($property->name, $property->property_code);
                            }
                            // $property->slug = $this->helper->pretty_url($request->title);
                            $property->save();
                        }

                        $this->propertyStep($property_id, 'title');
                        // $this->property_step($property_id, ['title']);

                        return apiResponse(['propertyid' => $property_id], 'Success', 200);
                    }
                }
            } elseif ($step == "photos") {
                // dd($request->all());
                if ($request->isMethod('post')) {
                    $parmanentRequest = $request->permanentDeleteFile ?? [];
                    $commaSeparatedString = reset($parmanentRequest);
                    $permanentIdsArray = explode(',', $commaSeparatedString);

                    $temporaryRequest = $request->tempDeleteFile ?? [];
                    $tempcommaSeparatedString = reset($temporaryRequest);
                    $temporaryIdsArray = explode(',', $tempcommaSeparatedString);

                    $tempArray = array_filter($temporaryIdsArray);
                    $parmanentArray = array_filter($permanentIdsArray);

                    $tempCount = count($tempArray);
                    $permanentCount = count($parmanentArray);

                    $rule = [];
                    if ($tempCount == 0 && $permanentCount == 0) {
                        $rule = [
                            'file'  => 'required|max:2999', //3MB
                            'edit'  => 'required'
                        ];
                    }

                    $validate = Validator::make($request->all(), $rule, [
                        'file.required' => 'Please select Image(s) to upload.',
                        'file.max' => 'Image(s) may not be greater than 3MB.',
                        'edit.required' => 'Edit param required in binary (1 is request for edit photos & 0 for add photos)',
                    ]);

                    if ($validate->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validate->errors()], 422);
                    }

                    $uploadedPhotos = PropertyPhotos::where('property_id', $property_id)->orderBy('serial', 'asc')->get();

                    $totalPhotos = count($uploadedPhotos); //Uploaded and Approved Photos
                    $reqPhotos = count($request->file('file') ?? []);
                    $photos_temp = PhotosTemp::where('property_id', $property_id)->count();


                    // $allphotos = ($totalPhotos + $reqPhotos + $photos_temp) - $tempCount;
                    $allphotos = ($totalPhotos-$permanentCount)+$reqPhotos; //($totalPhotos + $reqPhotos + $photos_temp) - ($tempCount + $permanentCount);
                    $parmanentPhoto = $totalPhotos - $permanentCount;



                    if ($request->edit) {
                        if ($allphotos < 5 || $allphotos > 20) {
                            return response()->json(['message' => 'failure', 'error' => 'Please upload between 5 and 20 images.'], 422);
                        }
                    } else {
                        if ($allphotos < 5 ||  $allphotos > 20) {
                            return response()->json(['message' => 'failure', 'error' => 'Please upload between 5 and 20 images.'], 422);
                        }
                    }


                    $photosId = [];

                    if ($request->hasFile('file')) {

                        $exist = PropertyPhotos::orderBy('serial', 'desc')
                            ->select('serial')
                            ->where('property_id', $property_id)
                            ->take(1)->first();
                        for ($i = 0; $i < count($request->file('file')); $i++) {
                            $name = str_replace(' ', '_', $_FILES["file"]["name"][$i]);
                            $ext = pathinfo($name, PATHINFO_EXTENSION);

                            if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'JPG') {
                                $imgFile = Image::make($_FILES["file"]["tmp_name"][$i])->orientate();
                                $imgFile->resize(null, 400, function ($constraint) {
                                    $constraint->aspectRatio();
                                    $constraint->upsize();
                                });
                                // Check and rotate image based on Exif orientation
                                //  $orientation = $this->getImageOrientation($_FILES["file"]["tmp_name"][$i]);
                                //  if ($orientation !== 1) { // 1 means no rotation is needed
                                //      $imgFile->rotate($this->getRotationAngle($orientation));
                                //  }
                                $image_url=ImageHelper::upload($imgFile,'property/' . $property_id);

                                if ($request->edit) {
                                    if ($property->status == 'Unlisted') {
                                        // IF ADD NEW PHOTOS (NEW PROPERTY BUT NOT LISTED)
                                        $photos = new PropertyPhotos;
                                        $photos->property_id = $property_id;
                                        $photos->photo = $image_url;

                                        $exist = PropertyPhotos::orderBy('serial', 'desc')
                                            ->select('serial')
                                            ->where('property_id', $property_id)
                                            ->take(1)->first();

                                        if ($exist) {
                                            // IF PHOTO EXISTS SERIAL PLUS ONE
                                            $photos->serial = $exist->serial + 1;
                                            $photos->cover_photo = 0;
                                        } else {
                                            //IF NO PHOTOS EXISTS
                                            $photos->serial = 1;
                                            $photos->cover_photo = 1;
                                        }

                                        $photos->save();
                                        array_push($photosId, $photos->id);
                                    } else {

                                        // IF EDIT PHOTOS
                                        $photos = new PhotosTemp;
                                        $photos->property_id = $property_id;
                                        $photos->photo = $image_url;
                                        $photos->cover_photo = !empty($exist->serial) == true ?  0 : 1;

                                        $photos->save();
                                        array_push($photosId, $photos->id);
                                    }
                                } else {

                                    // IF ADD NEW PHOTOS (NEW PROPERTY)
                                    $photos = new PropertyPhotos;
                                    $photos->property_id = $property_id;
                                    $photos->photo = $image_url;
                                    $photos->serial = 1;
                                    $photos->cover_photo = 1;

                                    $exist = PropertyPhotos::orderBy('serial', 'desc')
                                        ->select('serial')
                                        ->where('property_id', $property_id)
                                        ->take(1)->first();

                                    if (!empty($exist->serial)) {
                                        $photos->serial = $exist->serial + 1;
                                        $photos->cover_photo = 0;
                                    }
                                    $photos->save();
                                    array_push($photosId, $photos->id);
                                }
                            } else {
                                return apiResponse(['error' => ['file.mimes' => 'Only jpeg, jpg, and png images are allowed.']], 'failure', 422);
                            }
                        }
                    }
                    if (!empty($tempArray)) {
                        foreach ($tempArray as $tempFile) {
                            $tempImageIdsArray = explode(',', $tempFile);
                            foreach ($tempImageIdsArray as $imploded) {
                                $photos = PhotosTemp::find($imploded);

//                                if ($photos->photo != "images/default-image-not-exist.png") {
//                                    unlink('images/property/' . $property_id . '/' . $photos->photo);
//                                }
                                $photos->delete();
                            }

                            // return response()->json(['status' => 'Success', 'message' => 'Photo removed successfully.']);
                        }
                    }
                    if (!empty($parmanentArray)) {
                        foreach ($parmanentArray as $permanentFile) {
                            $imageIdsArray = explode(',', $permanentFile);

                            foreach ($imageIdsArray as $imploded) {
                                $photobyid = PropertyPhotos::find($imploded);

                                if ($photobyid) {  // Check if the record was found

                                    $property = Properties::find($property_id);

                                    if ($property && $property->host_id == Auth::id()) {

                                        if (request()->route()->getPrefix() == "api/v3") {
                                            if ($property->status == "Unlisted") {
                                                $this->helper->photos_delete_adjust_serial($photobyid, $property);
                                            } else {
                                                $photos = new PhotosTemp;
                                                $photos->property_id = $property_id;
                                                $photos->photo = $photobyid->photo;
                                                $photos->cover_photo = $photobyid->cover_photo;   //!empty($exist->serial) == true ?  0 : 1;
                                                $photos->serial = $photobyid->serial;
                                                $photos->remove = 1;
                                                $photos->remove_photo_id = $photobyid->id;

                                                $photos->save();
                                            }
                                        } else {
                                            $deletedSerial = $photobyid->serial;

                                            // Update the serial numbers of remaining photos with higher serial numbers
                                            PropertyPhotos::where('property_id', $property->id)
                                                ->where('serial', '>', $deletedSerial)
                                                ->decrement('serial', 1);

                                            if ($photobyid->cover_photo) {
                                                $makeCoverPhoto = PropertyPhotos::where('property_id', $property->id)
                                                    ->where('cover_photo', 0)
                                                    ->orderBy('serial', 'desc')
                                                    ->take(1)
                                                    ->update(['cover_photo' => 1]);
                                            }

                                            //unlink($photobyid->photo);
                                            $photobyid->delete();
                                        }
                                    } else {
                                        return apiResponse(['error' => 'Unauthorized.'], 'failure', 422);
                                    }
                                } else {
                                    return response()->json(['message' => 'failure', 'error' => 'Photo Does Not Exist.'], 422);
                                }
                            }
                        }
                    }


                    $cover_photo = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 1)->first();

                    $this->propertyStep($property_id, 'photos');
                    $this->propertyStep($property_id, columns: 'setCover');

                    return response()->json(["message" => "Success", "data" => ["property_id" => $property_id, 'photosid' => $photosId, 'cover_photo' => $cover_photo->id]], 200);
                }
                $data['cover_photo'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 1)->get();
                $data['photos'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 0)->orderBy('serial', 'asc')->get();
                $data['photosForApproval'] = PhotosTemp::where('property_id', $property_id)->where('cover_photo', 0)->orderBy('serial', 'asc')->get();
            }

            return apiResponse($data, 'Success', 200);
        } catch (\Throwable $th) {
            return apiResponse(['error' => $th->getMessage()], 'failure', 500);
        }
    }

    public function AddPropertyWithSteps(Request $request)
    {
        try {
            $custom_amenities = $request->input('custom_amenities');
            $step            = $request->step;
            $property_id     = $request->id;
            // dd($step);
            $data['step']    = $step;
            // Auth::id()
            $data['result'] = $property  = isHostOrCohostQuery(Properties::query(), auth()->id(), conId: 'id')->findOrFail($property_id);
            // dd($data['step']);

            // dd($data['result']->property_address);

            $data['details'] = PropertyDetails::pluck('value', 'field');

            $data['missed']  = PropertySteps::where('property_id', $request->id)->first();
            $data['special_days'] = Country::orderBy('id')->first()->special_days;
            if ($step == "spacetype") {

                if ($request->isMethod('post')) {

                    $rules = array(
                        'no_of_appartment'  => 'required',
                    );
                    if (isset($request->space_type)) {
                        $rules['space_type'] = 'required';
                    } else {
                        $rules['space_type'] = '';
                    }

                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $property = Properties::find($property_id);
                        $property->space_type = $request->space_type;
                        $property->no_of_appartment = $request->no_of_appartment;
                        $this->helper->getLogs($property, 'api');
                        $property->save();

                        $this->propertyStep($property_id, 'spacetype');

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
                $data['space_type']    = SpaceType::where('status', 'Active')->get();
            }
            elseif ($step == "location") {
                if ($request->isMethod('Post')) {
                    // dd($request);

                    $rules = array(
                        'address_line_1'    => 'required|max:250',
                        'address_line_2'    => 'max:250',
                        'country'           => 'required',
                        'city'              => 'required',
                        'state'             => 'required',
                        'latitude'          => 'required|not_in:0',
                        'postal_code'       => 'max:10',

                    );

                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        // $property = Properties::find($property_id);
                        // if ($property->property_type == 27) {
                        //     $property->name = 'Room in ' . $request->city;
                        // } else {
                        //     $property->name = SpaceType::getAll()->find($property->space_type)->name . ' in ' . $request->city;
                        // }
                        // $this->helper->getLogs($property, 'api');
                        // $property->save();

                        $property_address                 = PropertyAddress::where('property_id', $property_id)->first();

                        // $minLat =  $request->latitude - $property_address::LAT_LONG_INCREMENT;
                        // $maxLat =  $request->latitude + $property_address::LAT_LONG_INCREMENT;
                        // $minLong = $request->longitude - $property_address::LAT_LONG_INCREMENT;
                        // $maxLong = $request->longitude + $property_address::LAT_LONG_INCREMENT;
                        // $cityID = City::whereRaw('latitude between ' . $minLat . ' and ' . $maxLat . ' and longitude between ' . $minLong . ' and ' . $maxLong)->first();

                        $property_address->address_line_1 = $request->address_line_1;
                        $property_address->address_line_2 = isset($request->address_line_2) ? $request->address_line_2 : NULL;
                        $property_address->district       = $request->district;
                        $property_address->latitude       = $request->latitude;
                        $property_address->longitude      = $request->longitude;
                        $property_address->city           = $request->city;
                        // $property_address->city_id           = $cityID->id;
                        $property_address->state          = $request->state;
                        $property_address->country        = $request->country;
                        $property_address->postal_code    = $request->postal_code;
                        $this->helper->getLogs($property_address, 'api');
                        $property_address->save();

                        $this->propertyStep($property_id, 'location');
                        $this->propertyStep($property_id, 'confirmLocation');

                        $normalizedCity = trim(preg_replace('/\s+/u', '', $property_address->city));
                        $license_mandatory = ($property->property_type == 22 || in_array($normalizedCity, ["AlUla", "العلا"])) ? false : true;

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id, 'title' => $property->name,'license_mandatory' => $license_mandatory]], 200);
                    }
                }
            }
            elseif ($step == "numberofRoom") {
                if ($request->isMethod('Post')) {
                    $rules = array(
                        'bedroom'    => 'required|numeric|min:0',
                        'beds'       => 'required_if:bedroom,>0|numeric',
                        'bathroom'   => 'required',
                    );

                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return back()->withErrors($validator)->withInput();
                    } else {
                        if ($request->bedroom > 0 && $request->beds < 1) {
                            return response()->json(['message' => 'failure', 'error' => 'Select at least 1 bed when the bedroom is greater than 0'], 422);
                        }

                        $propertyamenities                  = Properties::find($request->id);
                        $propertyamenities->bedrooms        = $request->bedroom;

                        $propertyamenities->single_beds     = $request->bedroom ? $request->single_beds : 0;
                        $propertyamenities->double_beds     = $request->bedroom ? $request->double_beds : 0;

                        $propertyamenities->beds            = $request->bedroom ? $request->beds : 0;
                        $propertyamenities->bathrooms       = $request->bathroom;
                        $this->helper->getLogs($propertyamenities, 'api');
                        $propertyamenities->save();


                        $this->propertyStep($property_id, 'numberofRoom');


                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "basic") {

                if ($request->isMethod('Post')) {
                    $property                     = Properties::find($property_id);
                    $property->accommodates       = $request->adult + $request->children;
                    $property->adult_guest        = $request->adult;
                    $property->children_guest     = $request->children;
                    $this->helper->getLogs($property, 'api');
                    $property->save();

                    $this->propertyStep($property_id, 'basics');
                    return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                }
            }
            elseif ($step == "amenities") {
                if ($request->isMethod('Post') && is_array($request->amenities)) {
                    $rules = array(
                        'amenities'  => 'required',
                    );

                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        if ($custom_amenities) {
                            Properties::storeCustomAmenitiesApi($request->id, $custom_amenities);
                        }
                        $propertyamenities            = Properties::find($request->id);
                        $propertyamenities->amenities = implode(',', $request->amenities);
                        $this->helper->getLogs($propertyamenities, 'api');
                        $propertyamenities->save();

                        $this->propertyStep($property_id, 'amenities');
                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }

                $data['property_amenities'] = explode(',', $data['result']->amenities);
                $data['common_amenities']   = Amenities::where('status', 'Active')->where('type_id', 1)->get();
                $data['safety_amenities']   = Amenities::where('status', 'Active')->where('type_id', 2)->get();
                $data['house_rules']        = Amenities::where('status', 'Active')->where('type_id', 3)->get();
            }
            elseif ($step == "photos") {
                if ($request->isMethod('post')) {
                    $validate = Validator::make($request->all(), [
                        'file' => 'required',
                        'file.*' => 'mimes:jpg,jpeg,bmp,png,gif,JPG',
                    ]);
                    if ($validate->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validate->errors()], 422);
                    }
                    $photosId = [];
                    if ($request->hasFile('file')) {
                        for ($i = 0; $i < count($request->file('file')); $i++) {
                            $name = str_replace(' ', '_', $_FILES["file"]["name"][$i]);
                            $ext = pathinfo($name, PATHINFO_EXTENSION);

                            if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'gif' || $ext == 'JPG') {
                                $imgFile = Image::make($_FILES["file"]["tmp_name"][$i])->orientate();
                                $imgFile->resize(null, 400, function ($constraint) {
                                    $constraint->aspectRatio();
                                    $constraint->upsize();
                                });

                                // Check and rotate image based on Exif orientation
                                // $orientation = $this->getImageOrientation($_FILES["file"]["tmp_name"][$i]);
                                // if ($orientation !== 1) { // 1 means no rotation is needed
                                //     $imgFile->rotate($this->getRotationAngle($orientation));
                                // }

                                // Save the corrected image

                                $image_url=ImageHelper::upload($imgFile,'property/'.$property->id);
                                // ->save($path . "/" . $image);
                                $photos = new PropertyPhotos;
                                $photos->property_id = $property_id;
                                $photos->photo = $image_url;
                                $photos->serial = 1;
                                $photos->cover_photo = 1;

                                $exist = PropertyPhotos::orderBy('serial', 'desc')
                                    ->select('serial')
                                    ->where('property_id', $property_id)
                                    ->take(1)->first();

                                if (!empty($exist->serial)) {
                                    $photos->serial = $exist->serial + 1;
                                    $photos->cover_photo = 0;
                                }
                                $photos->save();
                                $this->helper->getLogs($photos, 'api');

                                array_push($photosId, $photos->id);
                                $this->propertyStep($property_id, 'photos');
                                $this->propertyStep($property_id, 'setCover');
                            }
                        }
                    }

                    return response()->json(["message" => "Success", "data" => ["property_id" => $property_id, 'photosid' => $photosId]], 200);
                }
                $data['cover_photo'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 1)->get();
                $data['photos'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 0)->orderBy('serial', 'asc')->get();
            }
            elseif ($step == "title") {
                if ($request->isMethod('post')) {
                    $validator = Validator::make($request->all(), [
                        'title' => ['required', 'string', 'regex:/^[a-zA-Z\s]+$/'],
                        'title_ar' => ['required', 'string', 'regex:/^[\p{Arabic}\s]+$/u'],
                    ], [
                        'title.required' => __('validation.title.required'),
                        'title.regex' => __('validation.title.regex'),
                        'title_ar.required' => __('validation.name_ar.required'),
                        'title_ar.regex' => __('validation.name_ar.regex'),
                    ]);
                    // $validate = Validator::make($request->all(), [
                    //     'title' => 'required',
                    //     'title_ar' => 'required',
                    // ]);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $property = Properties::find($property_id);
                        $property->name = $request->title;
                        $property->name_ar = $request->title_ar ?? null;

                        if (empty($property->slug)) {
                            $property->slug = $this->helper->pretty_url($property->name, $property->property_code);
                        }
                        // $property->slug = $this->helper->pretty_url($request->title);
                        $this->helper->getLogs($property, 'api');
                        $property->save();

                        $this->propertyStep($property_id, 'title');
                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "description") {
                if ($request->isMethod('post')) {

                    $rules = array(

                        'summary'  => 'required|max:1000',
                        'summary_ar'  => 'required|max:1000'
                    );
                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {

                        $property_description              = PropertyDescription::where('property_id', $property_id)->first();
                        $property_description->summary     = $request->summary;
                        $property_description->summary_ar  = $request->summary_ar;
                        $this->helper->getLogs($property_description, 'api');
                        $property_description->save();
                        $this->propertyStep($property_id, 'description');
                        Log::info('Property Description Updated', ['property_id' => $property_id, 'summary' => $request->summary]);
                        $property                     = Properties::find($property_id);
                        $property->accommodates       = '1';
                        $property->adult_guest        = '1';
                        $property->children_guest     = '0';
                        $this->helper->getLogs($property, 'api');
                        $property->save();
                        Log::info('Property accommodates Updated', ['property_id' => $property_id, 'summary' => $request->summary]);
                        $this->propertyStep($property_id, 'basics');
                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "price") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'price' => 'required|numeric|min:50',
                        'weekly_discount' => 'nullable|numeric|max:99|min:0',
                        'monthly_discount' => 'nullable|numeric|max:99|min:0'
                    );
                    foreach ($data['special_days'] as $special_day) {
                        $rules["price_{$special_day}"] = 'required|numeric|min:50';
                    }
                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $special_days_price = [];
                        foreach ($data['special_days'] as $special_day) {
                            $special_days_price[$special_day] = $request->{"price_{$special_day}"};
                        }

                        $property_price                    = PropertyPrice::where('property_id', $property_id)->first();
                        $property_price->price             = $request->price;
                        $property_price->special_days_price = $special_days_price;
                        $property_price->weekly_discount   = isset($request->weekly_discount) ? $request->weekly_discount : 0;
                        $property_price->monthly_discount  = isset($request->monthly_discount) ? $request->monthly_discount : 0;
                        $property_price->currency_code     = isset($request->currency_code) ? $request->currency_code : 'SAR';
                        $property_price->cleaning_fee      = isset($request->cleaning_fee) ? $request->cleaning_fee : 0;
                        $property_price->guest_fee         = isset($request->guest_fee) ? $request->guest_fee : 0;
                        $property_price->guest_after       = isset($request->guest_after) ? $request->guest_after : 0;
                        $property_price->security_fee      = isset($request->security_fee) ? $request->security_fee : 0;
                        $property_price->weekend_price     = isset($request->weekend_price) ? $request->weekend_price : 0;

                        $this->helper->getLogs($property_price, 'api');
                        $property_price->save();
                        $this->propertyStep($property_id, 'pricing');

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "nightsandtime") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'min_nights' => 'required|numeric|min:1',
                        'max_nights' => 'required|numeric|gte:min_nights',
                        'checkinTime' => 'required',
                        'checkoutTime' => 'required',
                    );

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $property = Properties::find($property_id);
                        $property->max_nights = $request->max_nights;
                        $property->min_nights = $request->min_nights;
                        $property->checkinTime = $request->checkinTime;
                        $property->checkoutTime = $request->checkoutTime;

                        $this->helper->getLogs($property, 'api');
                        $property->save();
                        $this->propertyStep($property_id, 'nightsandtime');


                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "question") {
                if ($request->isMethod('post')) {
                    $property           = Properties::find($property_id);
                    $property->booking_type = $request->booking_type;
                    $property->status = $property->status;
                    $property->visibility = $property->visibility;

                    $this->helper->getLogs($property, 'api');
                    $property->save();

                    $this->helper->duplicateEntry($property->no_of_appartment, $property->id, 'api');
                    $this->propertyStep($property_id, 'booking');



                    return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                }
            }
            elseif ($step == "communicationMethod") {

                if ($request->isMethod('post')) {
                    $rules = array();

                    if (isset(Auth::guard('api')->user()->communication_preference)) {
                        $rules['comm_method'] = 'nullable';
                    } else {
                        $rules['comm_method'] = 'required';
                    }

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $user  =  User::find(Auth::guard('api')->user()->id);
                        $user->communication_preference = implode(',', $request->comm_method);
                        $user->save();

                        // return redirect('/managehost/host_listings');
                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "reviewListing") {
                $request->validate([
                    'ref_code' => 'nullable|string|size:10|exists:admin,emp_code'
                ]);
                if (!!$request->ref_code) {
                    $admin = Admin::where('emp_code', $request->ref_code)->first()->id;
                    DB::table('admin_property')->insert([
                        'admin_id' => $admin,
                        'property_id' => $property_id,
                        'type' => 1,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                $this->propertyStep($property_id, 'reviewListing');
            }

            return response()->json(['message' => 'Success', 'data' => $data], 200);

            // return view("listing.$step",$data);

        } catch (Exception $e) {
            $arr = [
                'message' => 'Error',
                'status' => false
            ];
            $code = 500;
            if ($e instanceof ValidationException) {
                $code = 422;
                $arr['message'] = array_merge(...array_values($e->errors()))[0];
            } else if ($e instanceof ModelNotFoundException) {
                $code = 404;
                $arr['message'] = 'Property not found.';
            } else {
                $arr['message'] = $e->getMessage();
                $arr['trace'] = $e->getTrace();
            }
            return response()->json($arr, $code);
        }
    }

    public function DeletePhoto(Request $request)
    {


        try {
            $photobyid = PropertyPhotos::find($request->photoid);

            if (!$photobyid) {
                return response()->json(['message' => 'Failure', 'error' => 'Invalid photo ID'], 400);
            }

            $property = Properties::find($photobyid->property_id);

            if (!$property) {
                return response()->json(['message' => 'Failure', 'error' => 'Property not found'], 404);
            }

            if ($property->host_id != Auth::guard('api')->user()->id) {
                return response()->json(['message' => 'Failure', 'error' => 'Unauthorized'], 403);
            }

            // unlink('images/property/'.$photobyid->property_id.'/'.$photobyid->photo);
            $photoPath = $photobyid->photo;
            if (file_exists($photoPath)) {
                if (!unlink($photoPath)) {
                    return response()->json(['message' => 'Failure', 'error' => 'Failed to delete photo'], 500);
                }
            }

            $deletedSerial = $photobyid->serial; // Store the serial number of the deleted photo
            // Update the serial numbers of remaining photos with higher serial numbers
            PropertyPhotos::where('property_id', $property->id)
                ->where('serial', '>', $deletedSerial)
                ->decrement('serial', 1);

            if ($photobyid->cover_photo) {
                $makeCoverPhoto = PropertyPhotos::where('property_id', $property->id)
                    ->where('cover_photo', 0)
                    ->orderBy('serial', 'desc')
                    ->take(1)
                    ->update(['cover_photo' => 1]);
            }

            $photobyid->delete();
            $this->helper->getLogs($photobyid, 'api');

            return response()->json(['message' => 'Success', 'data' => "Photo Deleted"], 200);
        } catch (\Throwable $th) {
            return response()->json(['message' => 'Failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function checklisting(Request $request)
    {
        try {
            $propertyforchangelisting = Properties::find($request->propertyid);
            if ($propertyforchangelisting->status == "Listed") {
                if ($propertyforchangelisting->host_id == Auth::id()) {
                    if ($request->checkbox_value == true) {
                        $propertyforchangelisting->visibility = 1;
                        $propertyforchangelisting->save();
                        $this->helper->getLogs($propertyforchangelisting, 'api');
                    }
                    if ($request->checkbox_value == false) {
                        $propertyforchangelisting->visibility = 0;
                        $propertyforchangelisting->save();
                        $this->helper->getLogs($propertyforchangelisting, 'api');
                    }
                    return response()->json(['message' => 'Success', 'data' => 'Status Changed'], 200);
                } else {
                    return response()->json(['message' => 'Failure', 'data' => 'You are not host'], 422);
                }
            } else {
                return response()->json(['message' => "Waiting for admin's approval", ''], 422);
            }

            // dd($request->propertyid,$request->checkbox_value);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }


    public function create()
    {
        $data['property_type'] = PropertyType::getAll()->where('status', 'Active')->pluck('name', 'id');
        $data['space_type']    = SpaceType::getAll()->where('status', 'Active')->pluck('name', 'id');
        return response()->json(['message' => 'Success', 'data' => $data], 200);
    }
    public function store(Request $request)
    {
        try {
            $rules = array(
                'property_type_id'  => 'required',
                'space_type'        => 'required',
                'accommodates'      => 'required',
                'map_address'       => 'required',
            );

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            } else {
                $property                  = new Properties;
                $property->host_id         = Auth::id();
                $property->name            = SpaceType::getAll()->find($request->space_type)->name . ' in ' . $request->city;
                $property->property_type   = $request->property_type_id;
                $property->space_type      = $request->space_type;
                $property->accommodates    = $request->accommodates;
                $property->save();

                $property_address                 = new PropertyAddress;
                $property_address->property_id    = $property->id;
                $property_address->address_line_1 = $request->route;
                $property_address->city           = $request->city;
                $property_address->state          = $request->state;
                $property_address->country        = $request->country;
                $property_address->postal_code    = $request->postal_code;
                $property_address->latitude       = $request->latitude;
                $property_address->longitude      = $request->longitude;
                $property_address->save();

                $property_price                 = new PropertyPrice;
                $property_price->property_id    = $property->id;
                $property_price->currency_code  = 'SAR';
                $property_price->save();

                $property_steps                   = new PropertySteps;
                $property_steps->property_id      = $property->id;
                $property_steps->save();

                $property_description              = new PropertyDescription;
                $property_description->property_id = $property->id;
                $property_description->save();

                return response()->json(['message' => 'Success', 'propertyid' => $property->id], 200);
                // return redirect('listing/'.$property->id.'/basics');
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function listing(Request $request)
    {

        $step            = $request->step;
        $property_id     = $request->id;
        $data['step']    = $step;
        $data['result']  = isHostOrCohostQuery(Properties::query(), auth()->id(), conId: 'id')->findOrFail($property_id);
        $data['details'] = PropertyDetails::pluck('value', 'field');
        $data['missed']  = PropertySteps::where('property_id', $request->id)->first();
        // dd($request->id);
        if ($step == 'basics') {
            if ($request->isMethod('post')) {
                $property                     = Properties::find($property_id);
                $property->bedrooms           = $request->bedrooms;
                $property->beds               = $request->beds;
                $property->bathrooms          = $request->bathrooms;
                $property->bed_type           = $request->bed_type;
                $property->property_type      = $request->property_type;
                $property->space_type         = $request->space_type;
                $property->accommodates       = $request->accommodates;
                $property->save();

                $property_steps         = PropertySteps::where('property_id', $property_id)->first();
                $property_steps->basics = 1;
                $property_steps->save();
                return response()->json(['message' => 'Success', 'propertyid' => $property_id], 200);
                // return redirect('listing/'.$property_id.'/description');
            }

            $data['bed_type']       = BedType::getAll()->pluck('name', 'id');
            // dd($data['bed_type']);
            $data['property_type']  = PropertyType::getAll()->where('status', 'Active')->pluck('name', 'id');
            $data['space_type']     = SpaceType::getAll()->pluck('name', 'id');
        } elseif ($step == 'description') {
            if ($request->isMethod('post')) {

                $rules = array(
                    'name'     => 'required|max:50',
                    'summary'  => 'required|max:1000'
                );

                $fieldNames = array(
                    'name'     => 'Name',
                    'summary'  => 'Summary',
                );

                $validator = Validator::make($request->all(), $rules);
                $validator->setAttributeNames($fieldNames);

                if ($validator->fails()) {
                    return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);

                    // return back()->withErrors($validator)->withInput();
                } else {
                    $property           = Properties::find($property_id);
                    $property->name     = $request->name;
                    if (empty($property->slug)) {
                        $property->slug = $this->helper->pretty_url($property->name, $property->property_code);
                    }
                    // $property->slug     = $this->helper->pretty_url($request->name);
                    $property->save();

                    $property_description              = PropertyDescription::where('property_id', $property_id)->first();
                    $property_description->summary     = $request->summary;
                    $property_description->save();

                    $property_steps              = PropertySteps::where('property_id', $property_id)->first();
                    $property_steps->description = 1;
                    $property_steps->save();
                    return response()->json(['message' => 'Success', 'propertyid' => $property_id], 200);

                    // return redirect('listing/'.$property_id.'/location');
                }
            }
            $data['description']       = PropertyDescription::where('property_id', $property_id)->first();
        } elseif ($step == 'details') {
            if ($request->isMethod('post')) {
                $property_description                       = PropertyDescription::where('property_id', $property_id)->first();
                $property_description->about_place          = $request->about_place;
                $property_description->place_is_great_for   = $request->place_is_great_for;
                $property_description->guest_can_access     = $request->guest_can_access;
                $property_description->interaction_guests   = $request->interaction_guests;
                $property_description->other                = $request->other;
                $property_description->about_neighborhood   = $request->about_neighborhood;
                $property_description->get_around           = $request->get_around;
                $property_description->save();
                return response()->json(['message' => 'Success', 'propertyid' => $property_id], 200);

                // return redirect('listing/'.$property_id.'/description');
            }
        } elseif ($step == 'location') {
            if ($request->isMethod('post')) {
                $rules = array(
                    'address_line_1'    => 'required|max:250',
                    'address_line_2'    => 'max:250',
                    'country'           => 'required',
                    'city'              => 'required',
                    'state'             => 'required',
                    'latitude'          => 'required|not_in:0',
                );

                $fieldNames = array(
                    'address_line_1' => 'Address Line 1',
                    'country'        => 'Country',
                    'city'           => 'City',
                    'state'          => 'State',
                    'latitude'       => 'Map',
                );

                $messages = [
                    'not_in' => 'Please set :attribute pointer',
                ];

                $validator = Validator::make($request->all(), $rules, $messages);
                $validator->setAttributeNames($fieldNames);

                if ($validator->fails()) {
                    return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);

                    // return back()->withErrors($validator)->withInput();
                } else {
                    $property_address                 = PropertyAddress::where('property_id', $property_id)->first();
                    $property_address->address_line_1 = $request->address_line_1;
                    $property_address->address_line_2 = $request->address_line_2;
                    $property_address->latitude       = $request->latitude;
                    $property_address->longitude      = $request->longitude;
                    $property_address->city           = $request->city;
                    $property_address->state          = $request->state;
                    $property_address->country        = $request->country;
                    $property_address->postal_code    = $request->postal_code;
                    $property_address->save();

                    $property_steps           = PropertySteps::where('property_id', $property_id)->first();
                    $property_steps->location = 1;
                    $property_steps->save();
                    return response()->json(['message' => 'Success', 'propertyid' => $property_id], 200);

                    // return redirect('listing/'.$property_id.'/amenities');
                }
            }
            $data['country']   = Country::pluck('name', 'short_name');
        } elseif ($step == 'amenities') {
            if ($request->isMethod('post') && is_array($request->amenities)) {
                // dd($request->amenities);

                $rooms            = Properties::find($request->id);
                $rooms->amenities = implode(',', $request->amenities);
                $rooms->save();
                return response()->json(['message' => 'Success', 'propertyid' => $property_id], 200);

                // return redirect('listing/'.$property_id.'/photos');
            }
            $data['property_amenities'] = explode(',', $data['result']->amenities);
            $data['amenities']          = Amenities::where('status', 'Active')->get();
            $data['amenities_type']     = AmenityType::get();
        } elseif ($step == 'photos') {
            if ($request->isMethod('post')) {

                $validate = Validator::make($request->all(), [
                    'file' => 'required',
                    // file|dimensions:min_width=640,min_height=360|mimes:jpg,jpeg,bmp,png,gif,JPG',
                    // 'file' => 'dimensions:min_width=640,min_height=360'
                ]);
                if ($validate->fails()) {
                    return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                }
                // $path = public_path('images/property/'.$property_id.'/');

                // if (!file_exists($path)) {
                //     mkdir($path, 0777, true);
                // }

                if (isset($_FILES["file"]["name"])) {
                    $name = str_replace(' ', '_', $_FILES["file"]["name"]);
                    $ext = pathinfo($name, PATHINFO_EXTENSION);
                    // dd($tmp_name, $path . "/" . $image);
                    if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'gif' || $ext == 'JPG') {
                        // $uploaded = move_uploaded_file($tmp_name, $path . "/" . $image);
                        $imgFile = Image::make($request->file->getRealPath());
                        $imgFile->resize(null, 400, function ($constraint) {
                            $constraint->aspectRatio();
                            $constraint->upsize();
                        });
                        // $imgFile->orientate();
                        // Check and rotate image based on Exif orientation
                        //  $orientation = $this->getImageOrientation($_FILES["file"]["tmp_name"]);
                        //  if ($orientation !== 1) { // 1 means no rotation is needed
                        //      $imgFile->rotate($this->getRotationAngle($orientation));
                        //  }
                        $image_url=ImageHelper::upload($imgFile,'property/'.$property_id);

                        $photos = new PropertyPhotos;
                        $photos->property_id = $property_id;
                        $photos->photo = $image_url;
                        $photos->serial = 1;
                        $photos->cover_photo = 1;

                        $exist = PropertyPhotos::orderBy('serial', 'desc')
                            ->select('serial')
                            ->where('property_id', $property_id)
                            ->take(1)->first();

                        if (!empty($exist->serial)) {
                            $photos->serial = $exist->serial++;
                            $photos->cover_photo = 0;
                        }
                        $photos->save();
                        $property_steps = PropertySteps::where('property_id', $property_id)->first();
                        $property_steps->photos = 1;
                        $property_steps->save();
                    }
                }
                return response()->json(["message" => "Success", "property_id" => $property_id], 200);
            }
            $data['cover_photo'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 1)->get();
            $data['photos'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 0)
                ->orderBy('serial', 'asc')
                ->get();
        } elseif ($step == 'pricing') {
            if ($request->isMethod('post')) {
                $bookings = Bookings::where('property_id', $property_id)->where('currency_code', '!=', $request->currency_code)->first();
                if ($bookings) {
                    return response()->json(['message' => 'failure', "error" => "Booking has been made using the current currency. It cannot be changed now"], 422);

                    // return back()->withErrors(['currency' => trans('messages.error.currency_change')]);
                }
                $rules = array(
                    'price' => 'required|numeric|min:50',
                    'weekly_discount' => 'nullable|numeric|max:99|min:0',
                    'monthly_discount' => 'nullable|numeric|max:99|min:0'
                );
                $validator = Validator::make($request->all(), $rules);

                if ($validator->fails()) {
                    return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                } else {
                    $property_price                    = PropertyPrice::where('property_id', $property_id)->first();
                    $property_price->price             = $request->price;
                    $property_price->weekly_discount   = $request->weekly_discount;
                    $property_price->monthly_discount  = $request->monthly_discount;
                    $property_price->currency_code     = $request->currency_code;
                    $property_price->cleaning_fee      = $request->cleaning_fee;
                    $property_price->guest_fee         = $request->guest_fee;
                    $property_price->guest_after       = isset($request->guest_after) ? $request->guest_after : 0;
                    $property_price->security_fee      = $request->security_fee;
                    $property_price->weekend_price     = $request->weekend_price;
                    $property_price->save();

                    $property_steps = PropertySteps::where('property_id', $property_id)->first();
                    $property_steps->pricing = 1;
                    $property_steps->save();
                    return response()->json(['message' => 'Success', 'propertyid' => $property_id], 200);

                    // return redirect('listing/'.$property_id.'/booking');
                }
            }
        } elseif ($step == 'booking') {
            if ($request->isMethod('post')) {

                $property_steps          = PropertySteps::where('property_id', $property_id)->first();
                $property_steps->booking = 1;
                $property_steps->save();

                $properties               = Properties::find($property_id);
                $properties->booking_type = $request->booking_type;
                // $properties->status       = ($properties->steps_completed == 0) ?  'Listed' : 'Unlisted';
                $properties->status       = 'Unlisted';
                $properties->save();

                return response()->json(['message' => 'Success', 'propertyid' => $property_id], 200);

                // return redirect('listing/'.$property_id.'/calendar');
            }
        }

        return response()->json(['message' => 'Success', 'data' => $data], 200);

        // return view("listing.$step", $data);
    }

    public function edit_listing(Request $request)
    {
        $data['step'] = $step  = $request->step;
        $data['property_id'] = $property_id    = $request->id;
        $data['result'] = $property = isHostOrCohostQuery(Properties::query(), auth()->id(), conId: 'id')->findOrFail($property_id);
    }

    public function updateCoverPhoto(Request $request)
    {

        $validate = Validator::make($request->all(), [
            'property_id' => 'required',
            'photo_id' => 'required'
        ]);

        if ($validate->fails()) {
            return response(['message' => 'failure', 'error' => $validate->errors()], 422);
        }

        try {
            PropertyPhotos::where('property_id', '=', $request->property_id)->update(['cover_photo' => 0]);

            $photos = PropertyPhotos::find($request->photo_id);
            $photos->cover_photo = 1;
            $photos->save();
            $this->helper->getLogs($photos, 'api');

            return response(['message' => 'Success'], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }



    public function single(Request $request)
    {
        $authId = auth()->guard('api')->id();
        $data['property_slug'] = $request->slug;
        $result = Properties::selectRaw(
            'properties.*' . (!!$authId ? ', (pis.id IS NOT NULL || bs.id IS NOT NULL) AS chat_redirect, pchs.id AS chat_head_id' : '')
        )
            ->when(!!$authId, fn ($q) =>
            $q->leftJoin(
                'property_inquiries as pis',
                fn ($q1) => $q1->on('pis.property_id', 'properties.id')
                    ->where('pis.guest_id', $authId)
                    ->whereDate('pis.end_date', '>', now()->format('Y-m-d H:i:s'))
                    ->whereIn('pis.status', [PropertyInquiryStatusTypeEnum::Pending->value, PropertyInquiryStatusTypeEnum::Accepted->value])
            )
                ->leftJoin(
                    'bookings as bs',
                    fn ($q1) => $q1->on('bs.property_id', 'properties.id')
                        ->where('bs.user_id', $authId)
                        ->whereDate('bs.end_date', '>', now()->format('Y-m-d H:i:s'))
                        ->whereIn('bs.status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
                )
                ->leftJoin(
                    'property_chat_heads as pchs',
                    fn ($q1) => $q1->on('pchs.property_id', 'properties.id')
                        ->where('pchs.guest_id', $authId)
                ))
            ->with(['property_description', 'propertyType'])
            ->where('slug', $request->slug)
            ->where('properties.status', 'Listed')->where('visibility', 1)
            ->first();
        if (!isset($result)) {
            return response()->json(['message' => 'failure', 'error' => "no property found"], 422);
        }

        // $formattedNumber = number_format($result->rating_avg, 2, '.', ',');
        $result->rating_avg = $result->average_rating;

        $result['review_average'] = Reviews::avgs('guest', $result['id']);

        // $result['review_average']['avg_total'] = ($result['review_average']->cleanliness + $result['review_average']->darent_service + $result['review_average']->darent_recomended + $result['review_average']->accuracy + $result['review_average']->communication) / 5 ; //$result->rating_avg;
        $result['review_average']['avg_total'] = $result->average_rating;

        $result['chat_head_id'] = $result['chat_redirect'] ? $result['chat_head_id'] : null;
        unset($result['chat_redirect']);
        $data['result'] = $result;

        if (isset($property_summary->property_description->summary)) {
            $result = $result->property_description->summary;
        } else {
            $result->summary = null;
        }


        if (empty($result)) {
            return response()->json(['message' => 'failure', 'error' => 'property does not exist'], 422);
        }

        $data['property_id'] = $id = $result->id;
        $data['license_no'] = $result->license_no;
        $this->helper->trackProductView($data['property_id'],MOBILE_APP_KEY_NAME,$request->bearerToken());
        $data['property_photos']     = PropertyPhotos::where('property_id', $id)->orderBy('serial', 'asc')
            ->get();
        $data['property_price']     = PropertyPrice::where('property_id', $id)->get();
        $data['amenities']        = Amenities::normal($id);
        $data['safety_amenities'] = Amenities::security($id);
        $data['houserule_amenities'] = Amenities::house_rule($id);
        $available_dates = PropertyDates::where('property_id', $id)->whereDate('date', '>=', date('Y-m-d'))->where('status', 'Not available')->get();
        $available_dates_custom = CustomPricing::where('property_id', $id)->whereDate('date', '>=', date('Y-m-d'))->where('status', 'Not available')->get();
        $data['available_dates'] = (request()->route()?->getPrefix() === "api/v2")
            ? $available_dates->merge($available_dates_custom)->pluck('date')->unique()->values()
            : $available_dates->merge($available_dates_custom);
        $property_address         = $data['result']->property_address;

        $latitude                 = $property_address->latitude;

        $longitude                = $property_address->longitude;
        $data['result']->property_address->nearbyCoordinates = $this->helper->calculateNearbyCoordinates($latitude, $longitude, Properties::DISTANCE_IN_KM, Properties::BEARING_IN_DEGREES);

        // $data['checkin']          = (isset($request->checkin) && $request->checkin != '') ? $request->checkin : '';
        // $data['checkout']         = (isset($request->checkinTime) && $request->checkout != '') ? $request->checkout : '';

        $data['checkin']          = $result->checkinTime ?? null;
        $data['checkout']          = $result->checkoutTime ?? null;

        $data['guests']           = (isset($request->guests) && $request->guests != '') ? $request->guests : '';

        $data['user_wallet']      = NewUserWallet::where('user_id', auth()->id())->first();

        if (auth()->guard('api')->check()) {
            $userLang = auth()->guard('api')->user()->lang;
        } elseif (isset($request->lang)) {
            $userLang = $request->lang;
        } else {
            $userLang = "en";
        }

        $data['result']['property_title']    =   propertyTitle($id, $result->propertyType, $request->lang ?? " ");

        $data['result']['property_type_name'] = propertyTypeName($result->propertyType,  $request->lang ?? " ");
        $data['result']['space_type_lang'] = spaceTypeName($result->space_type,  $request->lang ?? " ");


        $data['result']['discount'] = $result->property_discount ? $result->property_discount->discount  : null;
        $data['result']['discounted_amount'] = $result->discountedAmount ??  null;
        $data['result']['host_profile'] = $result->users->getProfileSrcAttribute() ??  null;

        $data['symbol'] = $this->helper->getCurrentCurrencySymbol();

        $data['shareLink'] = url('/') . '/' . 'properties/' . $data['property_id'];


        $data['date_format'] = Settings::getAll()->firstWhere('name', 'date_format_type')->value;
        $data['result']->makeHidden(['can_edit']);

        $reviews = Reviews::reviewsList('guest', $data['property_id'])->orderByDesc('reviews.id')->cursorPaginate(10);

        $data['reviews'] = $reviews->getCollection()->map(function ($review) {
            $temp['reviewer'] = array_combine(
                ['id', 'name', 'profile_image'],
                array_map(function ($v) use ($review) {
                    $temp = $review->{"reviewer_$v"};
                    return $v == 'profile_image' && !$temp ? 'icons/user.svg' : $temp;
                }, ['id', 'name', 'profile_image'])
            );
            return [
                'id' => $review->id,
                'message' => $review->message,
                // 'rating' => $review->rating,
                'rating' => $this->helper->customRound($review->rating),
                'cleanliness' => $review->cleanliness,
                'location' => $review->location,
                'accuracy' => $review->accuracy,
                'communication' => $review->communication,
                'darent_service' => $review->darent_service,
                'darent_recomended' => $review->darent_recomended,
                'created_at' => $review->created_at,
                'review_date' => $review->review_date
            ] + $temp;
        });

        $data['score'] = ScoringSystem::where('property_id', $data['result']->id)->first();
        $data['review_counts'] = Reviews::selectRaw('COUNT(*) as total_reviews')
            ->selectRaw('SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star')
            ->selectRaw('SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star')
            ->selectRaw('SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star')
            ->selectRaw('SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star')
            ->selectRaw('SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star')
            ->where('property_id', $data['result']->id)
            ->first();


        return response()->json(['message' => 'success', 'data' => $data], 200);
        // return view('property.single', $data);
    }

    public function singleWebhook(Request $request)
    {
        $authId = auth()->guard('api')->id();
        $propertyData = Properties::where('property_code', $request->code)->first();
        $data['property_slug'] = $propertyData->slug;
        $result = Properties::selectRaw(
            'properties.*' . (!!$authId ? ', (pis.id IS NOT NULL || bs.id IS NOT NULL) AS chat_redirect, pchs.id AS chat_head_id' : '')
        )
            ->when(!!$authId, fn ($q) =>
            $q->leftJoin(
                'property_inquiries as pis',
                fn ($q1) => $q1->on('pis.property_id', 'properties.id')
                    ->where('pis.guest_id', $authId)
                    ->whereDate('pis.end_date', '>', now()->format('Y-m-d H:i:s'))
                    ->whereIn('pis.status', [PropertyInquiryStatusTypeEnum::Pending->value, PropertyInquiryStatusTypeEnum::Accepted->value])
            )
                ->leftJoin(
                    'bookings as bs',
                    fn ($q1) => $q1->on('bs.property_id', 'properties.id')
                        ->where('bs.user_id', $authId)
                        ->whereDate('bs.end_date', '>', now()->format('Y-m-d H:i:s'))
                        ->whereIn('bs.status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
                )
                ->leftJoin(
                    'property_chat_heads as pchs',
                    fn ($q1) => $q1->on('pchs.property_id', 'properties.id')
                        ->where('pchs.guest_id', $authId)
                ))
            ->with(['property_description', 'propertyType'])
            ->where('slug', $propertyData->slug)
            ->where('properties.status', 'Listed')->where('visibility', 1)
            ->first();
        if (!isset($result)) {
            return response()->json(['message' => 'failure', 'error' => "no property found"], 422);
        }

        // $formattedNumber = number_format($result->rating_avg, 2, '.', ',');
        // $result->rating_avg = number_format($result->rating_avg);
        // $result['review_average'] = Reviews::avgs('guest', $result['id']);
        // $result['review_average']['avg_total'] = $result->rating_avg;
        // $result['chat_head_id'] = $result['chat_redirect'] ? $result['chat_head_id'] : null;
        // unset($result['chat_redirect']);
        $data['result'] = $result;

        if (empty($result)) {
            return response()->json(['message' => 'failure', 'error' => 'property does not exist'], 422);
        }

        $data['property_id'] = $id = $result->id;
        $this->helper->trackProductView($data['property_id'],MOBILE_APP_KEY_NAME,$request->bearerToken());
        $data['property_photos']     = PropertyPhotos::where('property_id', $id)->orderBy('serial', 'asc')
            ->get();
        $data['property_price']     = PropertyPrice::where('property_id', $id)->get();
        // $data['amenities']        = Amenities::normal($id);
        // $data['safety_amenities'] = Amenities::security($id);
        // $data['houserule_amenities'] = Amenities::house_rule($id);
        $available_dates = PropertyDates::where('property_id', $id)->whereDate('date', '>=', date('Y-m-d'))->where('status', 'Not available')->get();
        $available_dates_custom = CustomPricing::where('property_id', $id)->whereDate('date', '>=', date('Y-m-d'))->where('status', 'Not available')->get();
        $data['available_dates'] = (request()->route()?->getPrefix() === "api/v2")
            ? $available_dates->merge($available_dates_custom)->pluck('date')->unique()->values()
            : $available_dates->merge($available_dates_custom);
        $property_address         = $data['result']->property_address;

        $latitude                 = $property_address->latitude;

        $longitude                = $property_address->longitude;
        $data['result']->property_address->nearbyCoordinates = $this->helper->calculateNearbyCoordinates($latitude, $longitude, Properties::DISTANCE_IN_KM, Properties::BEARING_IN_DEGREES);

        // $data['checkin']          = (isset($request->checkin) && $request->checkin != '') ? $request->checkin : '';
        // $data['checkout']         = (isset($request->checkinTime) && $request->checkout != '') ? $request->checkout : '';

        $data['checkin']          = $result->checkinTime ?? null;
        $data['checkout']          = $result->checkoutTime ?? null;

        $data['guests']           = (isset($request->guests) && $request->guests != '') ? $request->guests : '';

        if (auth()->guard('api')->check()) {
            $userLang = auth()->guard('api')->user()->lang;
        } elseif (isset($request->lang)) {
            $userLang = $request->lang;
        } else {
            $userLang = "en";
        }

        $price_list = json_decode($this->helper->getPrice($propertyData->id, $request->checkin, $request->checkout, $request->guest, booking_id: null));
        $data['result']['available_status'] = 'Available';
        if (isset($price_list->status)) {
            $data['result']['available_status'] = 'Not Available';
        };

        $data['result']['property_title']    =   propertyTitle($id, $result->propertyType, $request->lang ?? " ");

        $data['result']['property_type_name'] = propertyTypeName($result->propertyType,  $request->lang ?? " ");
        $data['result']['space_type_lang'] = spaceTypeName($result->space_type,  $request->lang ?? " ");


        return response()->json(['message' => 'success', 'data' => new WebhookPropertiesResource($data['result'])], 200);

        // return response()->json(['message' => 'success', 'data' => $data], 200);
        // return view('property.single', $data);
    }

    public function userBookmark(Request $request)
    {
        try {
            if (request()->route()->getPrefix() == "api/v2") {
                $request->validate([
                    'size' => 'required|integer|min:1,max:10000',
                    'page' => 'required|integer:min:1'
                ]);
                $size = $request->size;

                $data = Favourite::whereHas('properties', function ($q) {
                    $q->where('status', 'Listed')->where('visibility', 1);
                })->where(['user_id' => Auth::id(), 'status' => 'Active'])->orderBy('id', 'desc')
                    ->paginate($size);
                return new BookmarkCollection($data);
            }
            $data['bookings'] = Favourite::whereHas('properties', function ($q) {
                $q->with('property_price', 'property_address', 'property_description');
            })->where(['user_id' => Auth::id(), 'status' => 'Active'])->orderBy('id', 'desc')
                ->paginate(Settings::getAll()->where('name', 'row_per_page')->first()->value);
            return response()->json(['message' => 'success', 'data' => $data], 200);
        } catch (\Throwable $th) {
            return response(['status' => 'failure', 'errors' => $th->getMessage()], 500);
        }
    }

    public function addEditBookMark()
    {
        try {
            $property_id = request('id');
            $user_id = Auth::id();

            $favourite = Favourite::where('property_id', $property_id)->where('user_id', $user_id)->first();

            if (empty($favourite)) {
                $favourite = Favourite::create([
                    'property_id' => $property_id,
                    'user_id' => $user_id,
                    'status' => 'Active',
                ]);
                $favourite->bookmark = TRUE;
            } else {
                $favourite->status = ($favourite->status == 'Active') ? 'Inactive' : 'Active';
                $favourite->save();
                $favourite->status == 'Active' ? $favourite->bookmark = TRUE : $favourite->bookmark = FALSE;
            }

            return response()->json(['message' => 'Success', 'data' => $favourite], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function userProperties(Request $request)
    {
        try {

            $properties = isHostOrCohostQuery(Properties::with('property_price', 'property_address', 'property_photos', 'property_description'), auth()->id(), conId: 'id')
                ->withCount(['reviews' => function ($q) {
                    $q->where('reviewer', 'guest');
                }])
                ->orderBy('properties.id', 'desc')
                ->get();

            $data['currentCurrency'] =  $this->helper->getCurrentCurrency();
            if (request()->route()->getPrefix() == "api/v2") {
                $request->validate([
                    'size' => 'required|integer|min:1,max:10000',
                    'page' => 'required|integer:min:1'
                ]);
                $size = $request->size;

                $data['complete_property'] = collect($properties)->where('steps_completed', 0)->values();
                $data['incomplete_property'] = collect($properties)->where('steps_completed', '!=', 0)->values();
                $complete_properties_paginated = new LengthAwarePaginator(
                    $data['complete_property']->forPage($request->page, $size),
                    $data['complete_property']->count(),
                    $size,
                    $request->page
                );
                $incomplete_properties_paginated = new LengthAwarePaginator(
                    $data['incomplete_property']->forPage($request->page, $size),
                    $data['incomplete_property']->count(),
                    $size,
                    $request->page
                );
                $data['complete_property'] = $complete_properties_paginated;
                $data['incomplete_property'] = $incomplete_properties_paginated;

                return ApiResponse($data, 'Success', 200);
            }

            $data['complete_property'] = collect($properties)->where('steps_completed', 0)->values()->toArray();
            $data['incomplete_property'] = collect($properties)->where('steps_completed', '!=', 0)->values()->toArray();

            return response()->json(['message' => 'Success', 'data' => $data], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
            // return ApiResponse('', $th->getMessage(), 500);
        }
    }

    public function completeProperties(Request $request)
    {
        try {
            $request->validate([
                'size' => 'required|integer|min:1,max:10000',
                'page' => 'required|integer:min:1'
            ]);
            $special_days = Country::orderBy('id')->first()->special_days;
            $size = $request->size;
            $properties = isHostOrCohostQuery(Properties::with(
                'property_price',
                'property_address',
                'property_photos',
                'property_description',
                'property_steps'
            ), auth()->id(), conId: 'id')
                ->whereHas('property_steps', function ($query) {
                    $query->where('total_steps', '=', 0);
                })->withCount(['reviews' => function ($q) {
                    $q->where('reviewer', 'guest');
                }])
                ->orderBy('id', 'desc')
                ->paginate($size);
            $properties->getCollection()->transform(function ($property) use ($special_days) {
                $property['special_days_price'] = array_combine($special_days, array_map(fn ($day) => property_exists($property->property_price->special_days_price, $day) ? $property->property_price->special_days_price->{$day} : $property->property_price->price, $special_days));
                return $property;
            });

            // $data = collect($properties)->where('steps_completed','=',0)->values();
            // $complete_properties_paginated = new LengthAwarePaginator(
            //     $data->forPage($request->page, $size),
            //     $data->count(),
            //     $size,
            //     $request->page
            // );
            return new CompletePropertiesCollection($properties);
            // return response()->json(['message' => 'Success', 'data' => $properties], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
            // return ApiResponse('', $th->getMessage(), 500);
        }
    }

    public function incompleteProperties(Request $request)
    {
        try {
            $request->validate([
                'size' => 'required|integer|min:1,max:10000',
                'page' => 'required|integer:min:1'
            ]);
            $special_days = Country::orderBy('id')->first()->special_days;
            $size = $request->size;
            $properties = isHostOrCohostQuery(Properties::with(
                'property_price',
                'property_address',
                'property_photos',
                'property_description',
                'property_steps'
            ), auth()->id(), conId: 'id')
                ->whereHas('property_steps', function ($query) {
                    $query->where('total_steps', '!=', 0);
                })->withCount(['reviews' => function ($q) {
                    $q->where('reviewer', 'guest');
                }])
                ->orderBy('id', 'desc')
                ->paginate($size);
            $properties->getCollection()->transform(function ($property) use ($special_days) {
                $property['special_days_price'] = !$property->property_price ? (object) [] : array_combine($special_days, array_map(fn ($day) => property_exists($property->property_price->special_days_price, $day) ? $property->property_price->special_days_price->{$day} : $property->property_price->price, $special_days));
                return $property;
            });
            return new IncompletePropertiesCollection($properties);

            // return response()->json(['message' => 'Success', 'data' => $properties], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
            // return ApiResponse('', $th->getMessage(), 500);
        }
    }


    public function getPrice(Request $request)
    {
        $checkin = Carbon::parse($request->input('checkin'));
        $checkout = Carbon::parse($request->input('checkout'));
        // Ensure checkout is not before checkin
        if ($checkout->lt($checkin)) {
            return response()->json(['error' => 'Checkout date cannot be before check-in date'], 400);
        }
        // Calculate number of nights
        $number_of_nights = $checkin->diffInDays($checkout);
        // If same day, set to 1 night
        if ($number_of_nights == 0) {
            $number_of_nights = 1;
            $checkout = $checkin->copy()->addDay();
        }
        $dateRange = CarbonPeriod::create($checkin, $checkout->subDay());
        // $number_of_days = $checkin->diff($checkout)->days > 1 ? $checkin->diff($checkout)->days + 1 : 1;
        // $dateRange = CarbonPeriod::create($checkin, $checkout);
        $property_custom_pricing = CustomPricing::query()
            ->where('property_id', $request->property_id)
            ->whereIn('date', iterator_to_array($dateRange->map(fn($date) => $date->format('Y-m-d'))))
            ->where('type', 'calendar')
            ->where('status', 'Available')
            ->get()
            ->mapWithKeys(fn($c) => [ $c->date => $c->only([ 'price', 'discount' ]) ]);

        return $this->helper->getPriceDetails(
            Properties::with('property_price')->findOrFail($request->property_id),
            $dateRange,
            $number_of_nights,
            $property_custom_pricing,
            (int)$request->guest_count
        );
    }

    public function getPriceV2(Request $request)
    {
        $checkin = Carbon::parse($request->input('checkin'));
        $checkout = Carbon::parse($request->input('checkout'));
        // Ensure checkout is not before checkin
        if ($checkout->lt($checkin)) {
            return response()->json(['error' => 'Checkout date cannot be before check-in date'], 400);
        }
        // Calculate number of nights
        $number_of_nights = $checkin->diffInDays($checkout);
        // If same day, set to 1 night
        if ($number_of_nights == 0) {
            $number_of_nights = 1;
            $checkout = $checkin->copy()->addDay();
        }
        $dateRange = CarbonPeriod::create($checkin, $checkout->subDay());
        // $number_of_days = $checkin->diff($checkout)->days > 1 ? $checkin->diff($checkout)->days + 1 : 1;
        // $dateRange = CarbonPeriod::create($checkin, $checkout);

            $cacheKey = 'property_getPriceV2'.md5(json_encode([
            'checkin' => $checkin ?? '',
            'propertyId' => $request->property_id ?? '',
            'checkout' => $checkout ?? '',
            'reviewer' => $reviewer ?? '',
            'perPage' => $perPage ?? '',
        ]));
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        $property_custom_pricing = CustomPricing::query()
            ->where('property_id', $request->property_id)
            ->whereIn('date', iterator_to_array($dateRange->map(fn($date) => $date->format('Y-m-d'))))
            ->where('type', 'calendar')
            ->where('status', 'Available')
            ->get()
            ->mapWithKeys(fn($c) => [ $c->date => $c->only([ 'price', 'discount' ]) ]);

           $MabbatProperties = Properties::where(['id'=>$request->property_id,'platform_id'=> PlatformTypeEnum::MABBAT])
            ->whereNotNull('thirdparty_listing')
            ->first();

            if($MabbatProperties){
                 $CheckAvailability = (new MabaatService())->Availability($MabbatProperties->thirdparty_listing, $checkin, $checkout);
                 if($CheckAvailability['status'] == "Not available"){
                    return $CheckAvailability;

                 }
            }


        $data = $this->helper->getPriceDetails(
            Properties::with('property_price')->findOrFail($request->property_id),
            $dateRange,
            $number_of_nights,
            $property_custom_pricing,
            (int) $request->guest_count
        );
        PropertyCacheResult::insert([
            'property_id' => $request->property_id,
            'cache_key' => $cacheKey,
            'created_at' => now()->toDateTimeString(),
        ]);

        Cache::put($cacheKey, $data, now()->addMinutes(120));

        return   $data;
    }

    public function getPropertyPrice(Request $request)
    {
        $data = $this->helper->getPrice($request->property_id, $request->checkin, $request->checkout, $request->guest_count);
        $response = json_decode($data, true);
        if (isset($response['date_with_price']) && !empty($response['date_with_price'])) {
            foreach ($response['date_with_price'] as &$datePrice) {
                $dateString = $datePrice['date'];

                // Convert the date string to a formatted date using Carbon
                $date = Carbon::parse($dateString)->format('d-m-Y');

                // Update the 'date' value in the same array
                $datePrice['date'] = $date;
            }
        }
    if(isset($response['total_night_price_before_discount'])){
        $response['total_night_price'] = $response['total_night_price_before_discount'];
        unset($response['total_night_price_before_discount']);
    }
    if(isset($response['property_price_before_discount'])){
        $response['property_price'] = $response['property_price_before_discount'];
        unset($response['property_price_before_discount']);
    }
        // return $response;
        return response(['message' => 'Success', 'data' => $response], 200);
    }

    public function RatingByGuest(Request $request)
    {
        try {
            $rules = array(
                'rating'  => 'required',
                'message'  => 'required',
                'booking_id' => 'required',
            );

            $validator = Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            } else {
                $booking = Bookings::find($request->booking_id);
                $propertyhost = User::find($booking->host_id);
                $review = new Reviews();
                $review->sender_id = Auth::id();
                $review->receiver_id = $booking->host_id;
                $review->booking_id = $request->booking_id;
                $review->property_id = $booking->property_id;
                $review->rating = $request->rating;
                $review->reviewer = "guest";
                $review->message = $request->message;
                $review->save();
                if (isset($_FILES["file"]["name"])) {
                    $tmp_name = $_FILES["file"]["tmp_name"];
                    $name = str_replace(' ', '_', $_FILES["file"]["name"]);

                    $ext = pathinfo($name, PATHINFO_EXTENSION);

                    $image = time() . '_' . $name;


                    // $path = 'public/images/property/' . $property_id;
                    $path = 'images/reviews_by_guest/' . Auth::id() . '/';
                    // dd($image);
                    if (!file_exists($path)) {
                        mkdir($path, 0777, true);
                    }
                    if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'gif' || $ext == 'JPG') {
                        $uploaded = move_uploaded_file($tmp_name, $path . "/" . $image);
                    }
                }
                $msg_en = customTrans('notifications.rating.received', [], 'en');
                $msg_ar = customTrans('notifications.rating.received', [], 'ar');
                $msg_en = $msg_ar;

                $msg = $propertyhost->lang == "ar" ? $msg_ar : $msg_en;

                if ($propertyhost->fcm_token) {
                    $this->helper->sendPushNotification($propertyhost->fcm_token, $msg, null, "host_review");
                }
                return response()->json(['message' => 'Success', 'data' => $review], 200);
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }
    public function RatingByHost(Request $request)
    {
        try {
            $rules = array(
                'rating'  => 'required',
                'message'  => 'required',
                'reviewid'  => 'required',
            );

            $validator = Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            } else {
                $UserReview = Reviews::find($request->reviewid);
                $property = Properties::find($UserReview->property_id);

                $ReviewedUser = User::find($UserReview->sender_id);
                $review = new Reviews();
                $review->sender_id = Auth::id();
                $review->receiver_id = $UserReview->sender_id;
                $review->booking_id = $UserReview->booking_id;
                $review->property_id = $UserReview->property_id;
                $review->rating = $request->rating;
                $review->reviewer = "host";
                $review->message = $request->message;
                $review->save();


                $UserReview->ispublic = 1;
                $UserReview->save();
                if (isset($_FILES["file"]["name"])) {
                    $tmp_name = $_FILES["file"]["tmp_name"];
                    $name = str_replace(' ', '_', $_FILES["file"]["name"]);

                    $ext = pathinfo($name, PATHINFO_EXTENSION);

                    $image = time() . '_' . $name;


                    // $path = 'public/images/property/' . $property_id;
                    $path = 'images/reviews_by_host/' . Auth::id() . '/';
                    // dd($image);
                    if (!file_exists($path)) {
                        mkdir($path, 0777, true);
                    }
                    if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'gif' || $ext == 'JPG') {
                        $uploaded = move_uploaded_file($tmp_name, $path . "/" . $image);
                    }
                }

                $msg_en = customTrans('notifications.rating.posted', [], 'en');
                $msg_ar = customTrans('notifications.rating.posted', [], 'ar');
                $msg = $ReviewedUser->lang == "ar" ? $msg_ar : $msg_en;

                if ($ReviewedUser->fcm_token) {
                    $this->helper->sendPushNotification($ReviewedUser->fcm_token, $msg, null, "guest_review ");
                }

                return response()->json(['message' => 'Success', 'data' => $review], 200);
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }





    public function searchReviews(Request $request)
    {
        try {
            $searchText = $request->searchText;
            $propertyId = $request->propertyId;
            $filterBy = $request->filterBy;
            $perPage = $request->perPage ?? 5;
            $offset = $request->offset ?? 0;
            $reviewer = $request->reviewer == 'guest' ? 'guest' :  'host';

    $cacheKey = 'property_searchReviews'.md5(json_encode([
                'searchText' => $searchText ?? '',
                'propertyId' => $propertyId ?? '',
                'filterBy' => $filterBy ?? '',
                'reviewer' => $reviewer ?? '',
                'perPage' => $perPage ?? '',
            ]));
            $cachedResult = Cache::get($cacheKey);
            // if ($cachedResult) {
            //     return response()->json(['message' => 'Success', 'data' => $cachedResult], 200);                 //commenting because the pagination is not working.
            // }

            $orderByFilter = '';
            switch ($filterBy) {
                case 'rated-desc':
                    $orderByFilter = 'reviews.rating DESC';
                    break;
                case 'rated-asc':
                    $orderByFilter = 'reviews.rating ASC';
                    break;
                default:
                    $orderByFilter = 'reviews.created_at DESC';
                    break;
            }

            $data['reviews'] = Reviews::where('reviewer', 'guest')
                ->join('users', 'reviews.sender_id', '=', 'users.id')
                ->where('reviews.reviewer', $reviewer)
                ->where('reviews.property_id', $propertyId)
                ->where(function ($query) use ($searchText) {
                    // Search by users.first_name
                    $query->where('users.first_name', 'like', '%' . $searchText . '%')
                        // Search by concatenated full name (first_name + ' ' + last_name)
                        ->orWhereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ['%' . $searchText . '%']);
                })
                ->where('reviews.ispublic', 1)
                ->orderByRaw($orderByFilter)
                ->select('reviews.*', 'users.first_name', 'users.last_name', 'users.profile_image')
                ->skip($offset)->take($perPage)->paginate($perPage);


            foreach ($data['reviews'] as $review) {
                $review['rating'] = $this->helper->customRound($review->rating);
                $review['review_date'] = Carbon::parse($review['review_date'])->format('m/d/Y');

                if ($review['profile_image'] == '') {
                    $review['profile_image'] = asset('icons/user.svg');
                } else {
                    $src1 = 'images/profile/' . $review['id'] . '/' . $review['profile_image'];
                    $src2 = $review['profile_image'];
                    if (file_exists($src1)) {
                        $review['profile_image'] = asset($src1);
                    } else if (file_exists($src2)) {
                        $review['profile_image'] = asset($src2);
                    } else {
                        $review['profile_image'] =  asset('icons/user.svg');
                    }
                }
            }

            $data['reviews']->withPath(str_replace('http://', 'https://', $data['reviews']->path()));
            $data['reviews']->nextPageUrl = str_contains(($data['reviews']->nextPageUrl() ?? ''), 'https')
            ? $data['reviews']->nextPageUrl()
            : str_replace('http://', 'https://', ($data['reviews']->nextPageUrl() ?? ''));


                PropertyCacheResult::insert([
                'property_id' => $propertyId,
                'cache_key' => $cacheKey,
                'created_at' => now()->toDateTimeString(),
            ]);

            Cache::put($cacheKey, $data, now()->addMinutes(120));

            return response()->json(['message' => 'Success', 'data' => $data], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function propertyStep($prop_id, $columns)
    {
        $count = PropertySteps::where('property_id', $prop_id)->where($columns, 0)->count();
        if ($count) {
            return PropertySteps::where('property_id', $prop_id)->update([$columns => 1]);
        }
        return true;
    }

    function getPricesByProperty(Request $request, $property_id)
    {
        try {
            $isMob = $request->is('api/*');
            $request->validate([
                'year' => 'required_without:combos|date_format:Y',
                'combos' => 'required_without:year|nullable|array|min:1',
                'combos.*' => 'required|date_format:Y-m|distinct'
            ]);
            $year = $request->year;
            $combos = $request->combos;
            if (is_array($combos) && !!Count(array_filter($combos, function ($combo, $index) use ($combos) {
                $c = Carbon::parse($combo)->subMonth()->startOfMonth();
                if ($index > 0 && Carbon::parse($combos[$index - 1])->startOfMonth()->notEqualTo($c)) {
                    return true;
                }
                return false;
            }, ARRAY_FILTER_USE_BOTH))) {
                throw ValidationException::withMessages([
                    'combos' => ['Incorrect combos sequence.']
                ]);
            }
            if (!$isMob && is_array($combos) && count($combos) == 1) {
                $date = Carbon::parse($combos[0] . '-01');
                $start = $date->copy()->subDays(7)->format('Y-m-d');
                $end = $date->copy()->addMonth()->addDays(11)->format('Y-m-d');
            }
            $property_price = isHostOrCohostQuery(PropertyPrice::query(), auth()->id())
                ->where('property_price.property_id', $property_id)->firstOrFail();
            $price_obj = ['per_night' => $property_price->price, 'special_prices' => $property_price->special_days_price];

            $price_obj['custom_prices'] = CustomPricing::selectRaw("IF(custom_pricing.price = 0, IFNULL(JSON_UNQUOTE(JSON_EXTRACT(pps.special_days_price, CONCAT('$.', LOWER(DATE_FORMAT(custom_pricing.date, '%W'))))), pps.price), custom_pricing.price) as price, (custom_pricing.price != 0) AS is_price_custom, date, discount, status='Available' As available")
                ->join('property_price AS pps', 'pps.property_id', 'custom_pricing.property_id')
                ->where('custom_pricing.property_id', $property_id)
                ->when(
                    !!$year && !is_array($combos),
                    fn ($q) => $q->whereYear('date', $year)
                )->when(
                    is_array($combos),
                    fn ($q) => $q->when(
                        !isset($start),
                        fn ($q1) => $q1->whereIn(DB::raw('DATE_FORMAT(date, "%Y-%m")'), $combos),
                        fn ($q1) => $q1->whereBetween('date', [$start, $end])
                    )
                )
                ->orderBy('date')->where('type', 'calendar')->get()
                ->unique('date')->reduce(
                    function ($iniVal, $cprice) {
                        $date = $cprice->date;
                        unset($cprice->date);
                        $cprice->available = !!$cprice->available;
                        $cprice->is_price_custom = !!$cprice->is_price_custom;
                        $iniVal[$date] = $cprice;
                        return $iniVal;
                    },
                    collect()
                );
                $price_obj['daily_discounts'] = DailyDiscount::selectRaw("date, discount, status='Available' As available")
                ->where('property_id', $property_id)
                ->when(
                    !!$year && !is_array($combos),
                    fn ($q) => $q->whereYear('date', $year)
                )->when(
                    is_array($combos),
                    fn ($q) => $q->when(
                        !isset($start),
                        fn ($q1) => $q1->whereIn(DB::raw('DATE_FORMAT(date, "%Y-%m")'), $combos),
                        fn ($q1) => $q1->whereBetween('date', [$start, $end])
                    )
                )
                ->orderBy('date')->get()
                ->unique('date')->reduce(
                    function ($iniVal, $cprice) {
                        $date = $cprice->date;
                        unset($cprice->date);
                        $cprice->available = !!$cprice->available;
                        $iniVal[$date] = $cprice;
                        return $iniVal;
                    },
                    collect()
                );

                $merged_prices = [];
                foreach ($price_obj['custom_prices'] as $date => $custom_price) {

                    $merged_prices[$date] = array_merge($custom_price->toArray(), ['is_price_custom' => true]);
                }
                foreach ($price_obj['daily_discounts'] as $date => $daily_discount) {
                    if (!isset($merged_prices[$date])) {
                        $merged_prices[$date] = [
                            'price' => "0",
                            'is_price_custom' => false,
                            'discount' => (float)($daily_discount['discount']),
                            'available' => $daily_discount['available']
                        ];
                    } else {
                        $merged_prices[$date]['price'] = "0";
                        $merged_prices[$date]['is_price_custom'] = false;
                        $merged_prices[$date]['discount'] = (float)($daily_discount['discount']);
                        $merged_prices[$date]['available'] = $daily_discount['available'];
                    }
                }
                $price_obj['custom_prices'] = $merged_prices;

                $reservations = Bookings::selectRaw("bookings.id, code, first_name, last_name, profile_image, guest, start_date, end_date, total")
                ->join('users AS us', 'us.id', 'user_id')->when(!!$year && !is_array($combos), fn ($q) => $q->whereYear('start_date', $year))
                ->when(is_array($combos), fn ($q) => $q->whereIn(DB::raw('DATE_FORMAT(start_date, "%Y-%m")'), $combos))
                ->where('property_id', $property_id)->where('bookings.status', 'Accepted')->orderBy('start_date')->get();
            $data = ['data' => [
                'price_obj' => new CalendarPriceResource((object) $price_obj),
                'reservations' => CalendarReservationResource::collection($reservations)
            ], 'status' => true];
            if ($isMob) {
                $data = [...$data, 'success' => true];
            }
            return response()->json($data);
        } catch (\Exception $e) {
            $arr = [
                'message' => 'Error',
                'status' => false
            ];
            $code = 500;
            if ($e instanceof ValidationException) {
                $code = 422;
                $arr['errors'] = $isMob ? array_merge(...array_values($e->errors()))[0] : $e->errors();
            } else if ($e instanceof ModelNotFoundException) {
                $code = 404;
                $arr['message'] = 'Property not found.';
            } else {
                $arr['message'] = $e->getMessage();
                $arr['trace'] = $e->getTrace();
            }
            return response()->json($arr, $code);
        }
    }

    function setCalendarProp(Request $request, $property_id)
    {
        try {
            $isMob = $request->is('api/*');
            //adding for previous data compatibility
            $request->merge([
                'type' => $request->type ?? 'calendar'
            ]);
            $validated = $request->validate([
                'type' => 'required|in:calendar,daily_discount',
                'price' => 'required_if:type,calendar|numeric|min:0',
                'discount' => 'required_if:type,daily_discount|numeric|min:0|max:99.99',
                'available' => 'required|integer|between:0,1',
                'dates' => 'required|array|min:1',
                'dates.*' => 'required|distinct|date|date_format:Y-m-d'
            ]);
            $data = ['message' => 'invalid type', 'status' => false];

            if ($validated['type'] == 'calendar') {
                $validated['discount'] = !is_null($validated['discount']) ? $validated['discount'] : 0;
                $property_price = isHostOrCohostQuery(PropertyPrice::query(), auth()->id())
                    ->where('property_price.property_id', $property_id)->firstOrFail();
                $dates = $validated['dates'];
                $availablity = $validated['available'] ? 'Available' : 'Not available';
                CustomPricing::whereIn('date', $dates)->where('property_id', $property_id)->where('type', 'calendar')->get()
                    ->each(function ($cus) use ($validated, &$dates, $availablity, $property_price) {
                        $day = strtolower(Carbon::parse($cus->date)->format('l'));
                        $price = property_exists($property_price->special_days_price, $day) ? $property_price->special_days_price->{$day} : $property_price->price;
                        if (($price == $validated['price'] || $validated['price'] == 0) && $validated['discount'] == 0) {
                            if ($validated['available']) {
                                $cus->delete();
                            } else {
                                $cus->status = 'Not available';
                                $cus->save();
                            }
                        } else {
                            $cus->price = $price == $validated['price'] ? 0 : $validated['price'];
                            $cus->discount = $validated['discount'];
                            $cus->status = $availablity;
                            $cus->save();
                        }
                        unset($dates[array_search($cus->date, $dates)]);
                    });
                if (!$validated['available'] || $validated['price'] != 0 || $validated['discount'] != 0) {
                    $newData = [];
                    foreach ($dates as $date) {
                        $day = strtolower(Carbon::parse($date)->format('l'));
                        $price = property_exists($property_price->special_days_price, $day) ? $property_price->special_days_price->{$day} : $property_price->price;
                        $newData[] = ['date' => $date, 'property_id' => $property_id, 'status' => $availablity, 'price' => $price == $validated['price'] ? 0 : $validated['price'], 'discount' => $request->discount, 'type' => 'calendar', 'created_at' => now(), 'updated_at' => now()];
                    }
                    if (count($newData) > 0) {
                        CustomPricing::insert($newData);
                    }
                }
             $data = ['message' => 'Calendar dates updated successfully.', 'status' => true];

            }
            elseif($validated['type'] == 'daily_discount'){
                $property_price = isHostOrCohostQuery(PropertyPrice::query(), auth()->id())
                    ->where('property_price.property_id', $property_id)->firstOrFail();
                $dates = $validated['dates'];
                $availablity = $validated['available'] ? 'Available' : 'Not available';
                DailyDiscount::whereIn('date', $dates)->where('property_id', $property_id)->get()
                    ->each(function ($cus) use ($validated, &$dates, $availablity) {
                        if (($cus->discount == $validated['discount'] || $validated['discount'] == 0) && $validated['discount'] == 0) {
                            if ($validated['available']) {
                                $cus->delete();
                            } else {
                                $cus->status = 'Not available';
                                $cus->save();
                            }
                        } else {
                            $cus->discount = $validated['discount'];
                            $cus->status = $availablity;
                            $cus->save();
                        }
                        unset($dates[array_search($cus->date, $dates)]);
                    });
                if (!$validated['available'] || $validated['discount'] != 0) {
                    $newData = [];
                    foreach ($dates as $date) {
                        $newData[] = [
                            'date' => $date,
                            'property_id' => $property_id,
                            'status' => $availablity,
                            'discount' => $validated['discount'],
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                    }
                    if (count($newData) > 0) {
                        DailyDiscount::insert($newData);
                    }
                }
             $data = ['message' => 'Daily discount dates updated successfully.', 'status' => true];

            }

            if ($isMob) {
                $data = [...$data, 'success' => true];
            }
            return response()->json($data);
        } catch (\Exception $e) {
            $arr = [
                'message' => 'Error',
                'status' => false
            ];
            $code = 500;
            if ($e instanceof ValidationException) {
                $code = 422;
                $arr['errors'] = $isMob ? array_merge(...array_values($e->errors()))[0] : $e->errors();
            } else if ($e instanceof ModelNotFoundException) {
                $code = 404;
                $arr['message'] = 'Property not found.';
            } else {
                $arr['message'] = $e->getMessage();
                $arr['trace'] = $e->getTrace();
            }
            return response()->json($arr, $code);
        }
    }

    function getCalendarBookingInfo($property_id, $booking_id)
    {
        $booking = Bookings::where('property_id', $property_id)->findOrFail($booking_id);
        return response()->json(['data' => ['reservation' => new YourReservationResource($booking)], 'status' => true]);
    }

    function changePhotoOrder(Request $request)
    {
        try {
            $rules = array(
                'sort_images'  => 'required',
            );

            $validator = Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            } else {
                $sortImages = $request->input('sort_images');

                foreach ($sortImages as $index => $sortImage) {
                    $record = PropertyPhotos::where('id', $sortImage)
                        ->where('property_id', $request->id)
                        ->first();

                    if ($record) {
                        $record->update(['serial' => $index + 1]);
                    }
                    $this->helper->getLogs($record, 'api');
                }
                return response()->json(['message' => 'Success', 'data' => $sortImages], 200);
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    private function getImageOrientation($path)
    {
        $exif = exif_read_data(Storage::path($path));
        return $exif['Orientation'] ?? 1; // Default to 1 if orientation data is not found
    }

    private function getRotationAngle($orientation)
    {
        // Define rotation angle based on Exif orientation
        switch ($orientation) {
            case 3:
                return 180;
            case 6:
                return -90;
            case 8:
                return 90;
            default:
                return 0;
        }
    }
}
