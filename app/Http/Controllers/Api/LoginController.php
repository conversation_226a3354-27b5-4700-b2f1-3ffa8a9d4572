<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Otp;
use App\Http\Helpers\Common;
use App\Jobs\SendOtpEmailJob;
use App\Mail\OtpMail;
use App\Models\PhoneVerification;
use App\Models\EmailVerification;
use App\Models\PermissionUser;
use App\Models\User;
use App\Models\UserDocument;
use App\Models\UsersVerification;
use App\Models\Properties;
use App\Models\AccountDeleteRequest;
use App\Notifications\UserNotify;
use App\Notifications\UserNotifySms;
use Auth;
use Illuminate\Support\Str;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Cookie;
use App\Models\Settings;
use App\Models\SocialLogin;
use Exception;
use App\Models\ElmDocument;
use App\Models\Bank;
use App\Models\CoHostRequest;
use App\Models\OtpSetting;
use App\Models\PropertiesCohost;
use App\Models\UserDevice;
use Illuminate\Support\Facades\Log;
use App\Rules\ReCaptchaV3;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;
use Jenssegers\Agent\Agent;

class LoginController extends Controller
{
    private $helper;

    public function __construct()
    {
        $this->helper = new Common;
    }

    public function register(Request $request, UserController $user_controller)
    {

        $validation = Validator::make($request->all(), [
            'first_name' => 'required|max:255',
            'last_name' => 'required|max:255',
            'email' => 'required|email|max:255|unique:users',
            'phone' => 'required|string|regex:/\d+/|min:9|max:12',
            'password' => 'required|min:6|confirmed',
            'password_confirmation' => 'required'
        ]);

        if ($validation->fails()) {
            return response()->json(['message' => 'failure', 'error' => $validation->errors()], 422);
        }

        $insertUser = new User;
        $insertUser->first_name = $request->first_name;
        $insertUser->last_name = $request->last_name;
        $insertUser->email = $request->email;
        $insertUser->phone = $request->phone;
        $insertUser->formatted_phone = isset($request->code) ? $request->code . $request->phone : '+966' . $request->phone;
        $insertUser->password = bcrypt($request->password);
        $insertUser->save();

        $user = User::where('email', $request->email)->first();

        $user_controller->wallet($user->id);
        if (isset($request->noToken)) {
            Auth::login($user);
            return response()->json(['success' => true], 200);
        }
        $token = $user->createToken('API Token')->accessToken;
        return response()->json(['message' => 'Success', 'user' => $user, 'token' => $token], 200);
    }

    public function login(Request $request)
    {
        try {
            //retrieve the session data
            $checkin = Session::get('header_checkin');
            $checkout = Session::get('header_checkout');
            $guest = Session::get('adult_guest_session');

            $remember = ($request->remember_me) ? true : false;

            $validator = Validator::make($request->all(), [
                'email' => 'required|email|max:200',
                'password' => 'required',
            ]);


            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            } else {

                $users = User::where('email', $request->email)->first();

                if (!empty($users)) {
                    if ($users->status != 'Inactive') {
                        $socialUser = socialLogin::where('user_id', $users->id)->first();
                        if (isset($socialUser)) {
                            return response()->json(['message' => 'failure', 'error' => ['email' => ['This email is already associated with a google account']]], 422);
                        }
                        if (Auth::attempt(['email' => $request->email, 'password' => $request->password], $remember)) {
                            // put the session back
                            Session::put('header_checkin', $checkin);
                            Session::put('header_checkout', $checkout);
                            Session::put('adult_guest_session', $guest);

                            $user = Auth::user();
                            // $user->dateofbirth = isset(Auth::user()->user_details->where('field', 'date_of_birth')->first()->value) ? Auth::user()->user_details->where('field', 'date_of_birth')->first()->value : null;


                            if (!isset($request->noToken)) {
                                $token = auth()->user()->createToken('API Token')->accessToken;
                                $user->documents = UserDocument::where('user_id', auth()->id())->get();

                                $data = [
                                    'user' => $user,
                                    'token' => $token
                                ];
                                return response()->json(['message' => 'Success', 'data' => $data], 200);
                            } else {
                                $properties = isHostOrCohostQuery(Properties::query(), auth()->id(), conId: 'id')->get();

                                $data = ['success' => true];
                                if (count($properties) > 0) {
                                    $data['data'] = true;
                                }
                                return response()->json($data, 200);
                            }
                        } else {
                            if (!isset($request->noToken)) {
                                return response()->json(['message' => 'failure', 'error' => 'Invalid Credentials'], 422);
                            } else {
                                return response()->json(['message' => 'failure', 'error' => ['email' => ['Invalid Credentials']]], 422);
                            }
                        }
                    } else {
                        if (!isset($request->noToken)) {
                            return response()->json(['message' => 'failure', "error" => "User is inactive. Please try again!"], 422);
                        } else {
                            return response()->json(['message' => 'failure', 'error' => ['email' => ['User is inactive. Please try again!']]], 422);
                        }
                    }
                } else {
                    if (!isset($request->noToken)) {
                        return response()->json(['message' => 'failure', "error" => "There isn't an account associated with this email address."], 500);
                    } else {
                        return response()->json(['message' => 'failure', 'error' => ['email' => ['Invalid Credentials']]], 422);
                    }
                }
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function loginWebhook(Request $request)
    {
        try {
            $remember = ($request->remember_me) ? true : false;

            $validator = Validator::make($request->all(), [
                'username' => 'required',
                'password' => 'required',
            ]);


            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            } else {
                if ($request->username == env('DARENT_WEBHOOK_USERNAME') && $request->password == env('DARENT_WEBHOOK_PASSWORD')) {
                    $data['token'] = env('DARENT_WEBHOOK_TOKEN');
                    return response()->json(['message' => 'success', 'data' => $data], 200);
                } else {
                    return response()->json(['message' => 'failure', 'error' => ['email' => ['Invalid Credentials']]], 422);
                }
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function mobileLogin(Request $request)
    {
        try {
            //retrieve the session data
            $checkin = Session::get('header_checkin');
            $checkout = Session::get('header_checkout');
            $guest = Session::get('adult_guest_session');

            $validator = Validator::make($request->all(), [
                'email' => 'required|email|max:200',
                'password' => 'required',
            ]);
            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            }

            $remember = ($request->remember_me) ? true : false;
            $user = User::where('email', $request->email)->first();


            if (!empty($user)) {
                if ($user->status != 'Inactive') {
                    if (Auth::attempt(['email' => $request->email, 'password' => $request->password], $remember)) {
                        // put the session back
                        Session::put('header_checkin', $checkin);
                        Session::put('header_checkout', $checkout);
                        Session::put('adult_guest_session', $guest);

                        $user = Auth::user();

                        $token = auth()->user()->createToken('API Token')->accessToken;
                        $user->documents = UserDocument::where('user_id', auth()->id())->get();
                        $user->user_varification = UsersVerification::where('user_id', auth()->id())->first();
                        $user->permissions = PermissionUser::with('permission')->where('user_id', auth()->id())->get();

                        $data = [
                            'user' => $user,
                            'token' => $token,
                        ];
                        return response()->json(['message' => 'Success', 'data' => $data], 200);
                    } else {
                        return response(['message' => 'failure', 'error' => "Invalid Credentials"], 422);
                    }
                } else {
                    return response()->json(['message' => 'failure', "error" => "User is inactive. Please try again!"], 422);
                }
            } else {
                return response(['message' => 'failure', 'error' => "There isn't an account associated with this email address."], 422);
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function socialLogin(Request $request, UserController $user_controller)
    {
        try {
            if (request()->route()->getPrefix() == "api/v2") {
                $user = User::where('email', $request->email)->first();
                if ($user) {
                    if ($user->status != 'Inactive') {
                        $userSocial = SocialLogin::where('social_id', $request->social_id)->first();
                        if (!$userSocial) {
                            $data = [
                                'user_id' => $user->id,
                                'social_platform' => $request->social_platform,
                                'social_id' => $request->social_id,
                            ];
                            $socialLogin = SocialLogin::create($data);
                        }
                        $token = $user->createToken('API Token')->accessToken;
                    } else {
                        return response()->json(['message' => 'failure', "error" => "User is inactive. Please try again!"], 422);
                    }
                } else {
                    $userData = [
                        'first_name' => $request->first_name,
                        'last_name' => $request->last_name,
                        'phone' => $request->phone,
                        'email' => $request->email,
                        'social_platform' => $request->social_platform,
                        'social_id' => $request->social_id,
                        'profile_image' => ''
                    ];
                    $user = User::create($userData);
                    $user_controller->wallet($user->id);

                    // $this->uploadGoogleAvatar($google->avatar,$user->first_name, $user->id);
                    // $userSocial = SocialLogin::where('social_id',$request->social_id)->first();
                    // if(!$userSocial){
                    $userSocialLogin = [
                        'user_id' => $user->id,
                        'social_platform' => $request->social_platform,
                        'social_id' => $request->social_id,
                    ];
                    $socialLogin = SocialLogin::create($userSocialLogin);
                    // }


                }
                $token = $user->createToken('API Token')->accessToken;
                $user->documents = UserDocument::where('user_id', auth()->id())->get();

                $data = [
                    'user' => $user,
                    'token' => $token
                ];
                return response()->json(['message' => 'Success', 'data' => $data], 200);
            }

            $user = User::where(['social_platform' => $request->social_platform, 'social_id' => $request->social_id])->first();

            if ($user) {
                if ($user->status != 'Inactive') {
                    $token = $user->createToken('API Token')->accessToken;
                } else {
                    return response()->json(['message' => 'failure', "error" => "User is inactive. Please try again!"], 422);
                }
            } else {
                $data = [
                    'first_name' => $request->first_name,
                    'last_name' => $request->last_name,
                    'phone' => $request->phone,
                    'email' => $request->email,
                    'social_platform' => $request->social_platform,
                    'social_id' => $request->social_id,
                    'profile_image' => ''
                ];
                $user = User::create($data);
            }

            $token = $user->createToken('API Token')->accessToken;
            $user->documents = UserDocument::where('user_id', auth()->id())->get();

            $data = [
                'user' => $user,
                'token' => $token
            ];
            return response()->json(['message' => 'Success', 'data' => $data], 200);
        } catch (Exception $e) {
            return response()->json(['message' => 'failure', 'error' => $e->getMessage()], 500);
        }
    }

    public function mobileLogout(Request $request)
    {
        try {
            $user = User::find(Auth::user()->id);
            $update_token = $user->update(['fcm_token' => null]);


            if ($update_token) {
                $data['fcm_token'] = 'Successfully Removed';
                return response()->json(["message" => "Success", "data" => $data], 200);
            } else {
                return response()->json(["message" => "failure", "error" => "Something went wrong while logout"], 401);
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function logout(Request $request)
    {
        try {
            if (isset($request->noToken)) {
                Auth::logout();
                $request->session()->invalidate();
                Cookie::forget('laravel_google_oauth');
                Session::forget('user_mode');
                $request->session()->regenerateToken();
                return redirect('/');
            }

            $user = Auth::user()->token();
            $user->revoke();
            return response()->json(['message' => 'Log Out'], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function createOtp(Request $request, $again = false)
    {
        try {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string|regex:/\d+/|min:9|max:12|exists:users,phone',
            ], [
                'phone.required' => 'Mobile number is required.',
                'phone.exists' => 'Account does not exists with this number.'
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            }
            $user = User::where('phone', $request->phone)->first();
            if (!$user) {
                return response()->json(['message' => 'failure', "error" => "There isn't an account associated with this phone number."], 422);
            }
            if ($user->status == 'Inactive') {
                return response()->json(['message' => 'failure', "error" => "User is inactive. Please try again!"], 422);
            }
            $code = $user->generateOtp()->code;
            $user->notify(
                new UserNotify(
                    'system.send.opt.user',
                    route('home'),
                    [':code' => $code]
                )
            );

            return response()->json(['message' => ($again ? 'New otp' : 'Otp') . ' code has been send successfully.', 'success' => true, 'data' => ['code' => $code]], 200);
        } catch (\Throwable $th) {
            if ($th->getCode() == 483) {
                return response()->json(['message' => 'failure', 'error' => ['phone' => ['Unable to send message try another login method.']]], 422);
            }
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function verifyOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                // 'code' => 'required|string|min:4|max:4|regex:/\d+/|exists:otps,code,is_used,0,is_closed,0'
            ]);
            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            }

            $otp = Otp::where('code', $request->code)->latest()->first();
            $otp->update(['is_used' => true]);
            $user = $otp->user;

            $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
            $user_verification->phone = 'yes';
            $user_verification->save();
            $user->documents = UserDocument::where('user_id', $user->id)->get();

            if (!isset($request->noToken)) {
                return response()->json([
                    'message' => 'Success',
                    'data' => [
                        'user' => $user,
                        'token' => $user->createToken('API Token')->accessToken,
                    ]
                ], 200);
            } else {
                Auth::login($user);
                return response()->json(['success' => true], 200);
            }
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function googleRedirect()
    {
        // return Socialite::driver('google')->redirect();
        return Socialite::driver('google')->with(["prompt" => "select_account"])->redirect();
    }

    public function handleGoogleCallback(Request $request, UserController $user_controller)
    {
        $google = Socialite::driver('google')->user();
        $user = User::where('email', $google->email)->first();
        $names = explode(' ', $google->name);

        if (!$user) {
            $user = User::create([
                'first_name' => $names[0],
                'last_name' => $names[1],
                'email' => $google->email,
                'social_platform' => "Google",
                'google_id' => $google->id,
                'status' => 'Active'
            ]);
            $user_controller->wallet($user->id);

            $userSocialLogin = [
                'user_id' => $user->id,
                'social_platform' => "Google",
                'social_id' => $google->id,
            ];

            $socialLogin = SocialLogin::create($userSocialLogin);
            $this->uploadGoogleAvatar($google->avatar, $names[0], $user->id);
        } else if (!$user->google_id) {
            $user->update(['google_id' => $google->id]);
        } else if (!$user->profile_image) {
            $this->uploadGoogleAvatar($google->avatar, $names[0], $user->id);
        } else if ($user->status == "Inactive") {
            return redirect('/?inactive');

            // return response()->json(['message' => 'failure', "error" => "User is inactive. Please try again!"], 422);

        }
        $userSocial = SocialLogin::where('social_id', $google->id)->first();
        if (!$userSocial) {
            $data = [
                'user_id' => $user->id,
                'social_platform' => "Google",
                'social_id' => $google->id,
            ];
            $socialLogin = SocialLogin::create($data);
        }

        if ($request->wantsJson()) {
            $token = $user->createToken('API Token')->accessToken;
            return response()->json(['message' => 'Success', 'user' => $user, 'token' => $token], 200);
        }
        Auth::login($user);
        return redirect()->route('home');
    }


    private function uploadGoogleAvatar($avatr, $name, $user_id)
    {
        $googleProfileUrl = $avatr;
        $file_name = 'storage/images/profile/profile_' . time() . '.' . $name;

        if (!file_exists('storage/images/profile')) {
            mkdir('storage/images/profile', 0777, true);
        }

        $getProfile = file_put_contents($file_name, file_get_contents($googleProfileUrl));

        if ($getProfile) {
            $user = User::find($user_id);
            $user->profile_image = $file_name;
            $user->save();
        }
    }

    public function translation()
    {
        // $mapKey = Settings::where('type','googleMap')->first()->value;

        // $enFilePath = resource_path('lang/en/messages.php');
        // $arFilePath = resource_path('lang/ar/messages.php');

        // $enFileContents = include $enFilePath;
        // $arFileContents = include $arFilePath;

        // $messages = [
        //     'en' => $enFileContents,
        //     'ar' => $arFileContents,
        //     "mapKey" => $mapKey
        // ];
        $messages = Settings::getSettingAll();
        // dd(Settings::getSettingAll());

        return apiResponse($messages, '', 200);
        // return response()->json($messages);
    }

    public function check(Request $request)
    {
        if ($request->get('email')) {
            $email = $request->get('email');
            $data = DB::table("users")
                ->where('email', $email)
                ->count();
            if ($data > 0) {
                echo 'not_unique';
            } else {
                echo 'unique';
            }
        }
    }

    // public function createPhoneOtp(Request $request, $again = false)
    // {
    //     try {
    //         if (strtolower(env('APP_ENV')) == 'local') {
    //             $validator = Validator::make($request->all(), [
    //                 'phone' => 'required|string|regex:/\d+/|min:10|max:13',
    //             ], [
    //                 'phone.required' => 'Mobile number is required.',
    //                 // 'phone.exists' => 'Account does not exists with this number.'
    //             ]);
    //         } else {
    //             $validator = Validator::make($request->all(), [
    //                 'phone' => 'required|string|regex:/\d+/|min:9|max:14',
    //             ], [
    //                 'phone.required' => 'Mobile number is required.',
    //                 // 'phone.exists' => 'Account does not exists with this number.'
    //             ]);
    //         }


    //         if ($validator->fails()) {
    //             return apiResponse('Failure', $validator->errors(), 422);
    //         }

    //         $otp = PhoneVerification::generateCode();
    //         if ($request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************') {
    //             $otp = 8888;
    //         }
    //         $data = [
    //             'phone' => $request->phone,
    //             'otp' => $otp,
    //             'unique_id' => NULL,
    //             'is_used' => '0',
    //             'ip' => $request->header('cf-connecting-ip'),
    //         ];

    //         $createdOtp = PhoneVerification::updateOrCreate(['phone' => $request->phone], $data);
    //         $code = $createdOtp->otp;
    //         $createdOtp->formatted_phone = $createdOtp->phone;
    //         // $createdOtp->notify(new UserNotify(
    //         //     'system.send.opt.user',
    //         //     route('home'),
    //         //     [':code' => $code]
    //         // ));
    //         // $routePrefix = ;


    //         // $res = Http::post('https:staging2.darent.com/sendOtpNew',[
    //         //     "phone" => $request->phone,
    //         //     "code" => $code
    //         // ]);

    //         // Log::debug('lol',$res);

    //         if ($request->route()->getPrefix() == "api/v1") {
    //             return apiResponse($createdOtp, 'Success', 200);
    //         }
    //         return apiResponse(null, 'Success', 200);
    //     } catch (\Throwable $th) {
    //         if ($th->getCode() == 483) {
    //             return apiResponse('Failure', ['phone' => ['Unable to send message try another login method.']], 422);
    //         }
    //         return apiResponse('Failure', $th->getMessage(), 500);
    //     }
    // }

    public function createPhoneOtp(Request $request, $again = false)
    {
        try {
            // Validation rules
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string|regex:/\d+/|min:9|max:14',
            ], [
                'phone.required' => 'Mobile number is required.',
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }

            $phone = $request->phone;
            $ip = $request->ip();

            // Check if the phone is blocked
            $blockedOtp = PhoneVerification::where('phone', $phone)
                ->where('is_blocked', 1)
                ->where('blocked_until', '>', now())
                ->latest('created_at') // Ensure it retrieves the latest record by created_at
                ->first(); // Fetch the actual record

            if ($blockedOtp) {
                return apiResponse('Failure', ['phone' => ['This number is temporarily blocked. Please try again later.']], 403);
            }

            // Count OTP creation attempts in the last hour
            $attempts = PhoneVerification::where('phone', $phone)
                ->where('ip', $ip)
                ->where('created_at', '>', now()->subHour())
                ->count();

            if ($attempts >= 3) {
                PhoneVerification::where('phone', $phone)
                    ->where('ip', $ip)
                    ->update([
                        'is_blocked' => 1,
                        'blocked_until' => now()->addHours(1),
                    ]);

                return apiResponse('Failure', ['phone' => ['Too many attempts. This number is temporarily blocked.']], 403);
            }

            // Generate OTP
            $otp = PhoneVerification::generateCode();
            if (in_array($phone, ['+************', '+************', '+************', '+************', '+************'])) {
                $otp = 8888;
            }

            $data = [
                'phone' => $phone,
                'otp' => $otp,
                'unique_id' => null,
                'is_used' => '0',
                'ip' => $ip,
                'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
            ];

            // Create a new OTP record without updating existing ones
            PhoneVerification::create($data);

            // Return response
            return apiResponse(['otp' => $otp], 'OTP sent successfully', 200);
        } catch (\Throwable $th) {
            if ($th->getCode() == 483) {
                return apiResponse('Failure', ['phone' => ['Unable to send message. Try another login method.']], 422);
            }
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }


    public function createPhoneOtpV2(Request $request, $again = false)
    {
        try {
            if (strtolower(env('APP_ENV')) == 'local') {
                $validator = Validator::make($request->all(), [
                    'phone' => 'required|string|regex:/\d+/|min:10|max:13',
                    'g-recaptcha-response' => ['required', new ReCaptchaV3('sendOtpCaptcha', config('services.recaptcha_v3.score'))]
                ], [
                    'phone.required' => 'Mobile number is required.',
                    // 'phone.exists' => 'Account does not exists with this number.'
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'phone' => 'required|string|regex:/\d+/|min:9|max:14',
                    'g-recaptcha-response' => ['required', new ReCaptchaV3('sendOtpCaptcha', config('services.recaptcha_v3.score'))]
                ], [
                    'phone.required' => 'Mobile number is required.',
                    // 'phone.exists' => 'Account does not exists with this number.'
                ]);
            }

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }

            $phone = $request->phone;
            $ip = $request->ip();

            $attempts = PhoneVerification::select('phone', 'ip')
                ->selectRaw('COUNT(*) as attempt_count')
                ->where(function ($query) use ($phone, $ip) {
                    $query->where('phone', $phone)
                        ->orWhere('ip', $ip);
                })
                ->where('updated_at', '>=', now()->subHour())
                ->groupBy('phone', 'ip')
                ->havingRaw('COUNT(*) >= ?', [3])
                ->get();

            // Check if there are any matching attempts
            if ($attempts->isNotEmpty()) {
                return apiResponse('Failure', ['phone' => ['Too many attempts. This number is temporarily blocked.']], 422);
            }

            $otp = PhoneVerification::generateCode();

            $data = [
                'phone' => $request->phone,
                'otp' => $otp,
                'unique_id' => NULL,
                'is_used' => '0',
                'ip' => $request->header('cf-connecting-ip'),
                'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
            ];

            if ($request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************') {
                $data['otp'] = 8888;

                PhoneVerification::create($data);

                return apiResponse(null, 'Success', 200);
            }

            $createdOtp = PhoneVerification::create($data);
            $createdOtp->formatted_phone = $createdOtp->phone;
            $sendOtpResponse = $this->helper->sendOtp($createdOtp->phone, $otp);

            if (!$sendOtpResponse) {
                return apiResponse(null, 'Invalid Mobile Number', 400);
            }
//            $createdOtp->notify(
//                new UserNotify(
//                    'system.send.opt.user',
//                    route('home'),
//                    [':code' => $code]
//                )
//            );

            return apiResponse(null, 'Success', 200);
        } catch (\Throwable $th) {
            if ($th->getCode() == 483) {
                return apiResponse('Failure', ['phone' => ['Unable to send message try another login method.']], 422);
            }
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function createPhoneOtpV3(Request $request, $again = false)
    {
        try {
            // Fetch block duration from OTP settings
                $settings = OtpSetting::first();
                $blockDuration = $settings->block_duration ?? 60; // Default to 60 minutes if not set
            if (strtolower(env('APP_ENV')) == 'local') {
                $validator = Validator::make($request->all(), [
                    'phone' => 'required|string|regex:/^\+966\d{8,9}$/|min:10|max:13',    //  /\d+/
                    // 'g-recaptcha-response' => ['required', new ReCaptchaV3('sendOtpCaptcha', config('services.recaptcha_v3.score'))]
                ], [
                    'phone.required' => 'Mobile number is required.',
                    // 'phone.exists' => 'Account does not exists with this number.'
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'phone' => 'required|string|regex:/^\+966\d{8,9}$/|min:9|max:14',   //  /\d+/
                    'g-recaptcha-response' => ['required', new ReCaptchaV3('sendOtpCaptcha', config('services.recaptcha_v3.score'))]
                ], [
                    'phone.required' => 'Mobile number is required.',
                    // 'phone.exists' => 'Account does not exists with this number.'
                ]);
            }


            $ip = $request->header('cf-connecting-ip');
            $visitor_id = $request->header('X-Fingerprint-Visitor-Id');
            if ($validator->fails()) {
                $this->helper->logAttempt(
                    $request->phone,
                    $ip,
                    $visitor_id,
                    'validation_failed',
                    $validator->errors()->toArray()
                );
                return apiResponse('Failure', $validator->errors(), 422);
            }

            if (str_starts_with($visitor_id, '94PQLBeh')) {
                $this->helper->logAttempt(
                    $request->phone,
                    $ip,
                    $visitor_id,
                    'Same Visitor Id',
                    ['message' => "Too many attempts within $blockDuration minutes"]
                );
                return apiResponse('Failure', ['phone' => ["Try again after $blockDuration minutes."]], 406);
            }
            // $attempts = DB::table('phone_verification')
            //     ->selectRaw('COUNT(*) as attempt_count')
            //     ->where('visitor_id', $visitor_id)
            //     ->whereRaw("(updated_at >= (NOW() + INTERVAL 3 HOUR) - INTERVAL 60 MINUTE)")
            //     ->havingRaw('COUNT(*) >= ?', [3])
            //     ->get();

            // if (strtolower(env('APP_ENV')) == 'prod') {
                $attempts = DB::table('phone_verification')
                ->selectRaw('COUNT(*) as attempt_count')
                ->where('visitor_id', $visitor_id)
                ->whereRaw("updated_at >= NOW() - INTERVAL ? MINUTE", [$blockDuration]) // Uses admin-defined duration
                ->havingRaw('COUNT(*) >= ?', [$settings->max_attempts]) // Uses max attempts from settings
                ->get();


            // Check if there are any matching attempts
            if ($attempts->isNotEmpty()) {
                $this->helper->logAttempt(
                    $request->phone,
                    $ip,
                    $visitor_id,
                    'rate_limited',
                    ['message' => "Too many attempts within $blockDuration minutes."]
                );
                return apiResponse('Failure', ['phone' => ["Too many attempts. This number is temporarily blocked for $blockDuration minutes."]], 406);
            }

            // }


            $otp = PhoneVerification::generateCode();
            $data = [
                'phone' => $request->phone,
                'otp' => $otp,
                'unique_id' => NULL,
                'is_used' => '0',
                'ip' => $request->header('cf-connecting-ip'),
                'visitor_id' => $visitor_id,
                'device' => MOBILE_APP_KEY_NAME,
                'browser' => $request->user_agent ?? null,
                'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null,
            ];
            if ($request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************') {
                $data['otp'] = 8888;

                PhoneVerification::create($data);

                return apiResponse(null, 'Success', 200);
            }
            $data['guest_uuid'] = request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null;
            $createdOtp = PhoneVerification::create($data);
            $createdOtp->formatted_phone = $createdOtp->phone;
            if (Str::startsWith($request->phone, '+966')) {
                $sendOtpResponse = $this->helper->sendOtp($createdOtp->phone, $otp);
                // dd($sendOtpResponse);
                if (!$sendOtpResponse) {
                    $this->helper->logAttempt(
                        $request->phone,
                        $ip,
                        $visitor_id,
                        'send_failed',
                        ['message' => 'Invalid Mobile Number']
                    );
                    return apiResponse(null, 'Invalid Mobile Number', 400);
                }
            }

//             $createdOtp->notify(
//                 new UserNotify(
//                     'system.send.opt.user',
//                     route('home'),
//                     [':code' => $otp]
//                 )
//             );

            return apiResponse(null, 'Success', 200);
        } catch (\Throwable $th) {
            $errorType = 'system_error';
            if ($th->getCode() == 483) {
                $errorType = 'messaging_service_error';
            }

            $this->helper->logAttempt(
                $request->phone ?? null,
                $ip ?? null,
                $visitor_id ?? null,
                $errorType,
                ['message' => $th->getMessage()]
            );
            if ($th->getCode() == 483) {
                return apiResponse('Failure', ['phone' => ['Unable to send message try another login method.']], 422);
            }
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function verifyPhoneOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string|regex:/\d+/|min:9|max:14|exists:phone_verification,phone',
                'otp' => 'required',
            ], [
                'phone.required' => 'Mobile number is required.',
                'otp.required' => 'Otp is required.',
                'phone.exists' => 'Mobile Number does not exists.'
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }
            $checkOtp = PhoneVerification::where('phone', $request->phone)
                ->where('otp', $request->otp)
              #  ->where('ip', $request->header('cf-connecting-ip'))
                ->latest('updated_at') // Retrieve the latest record based on created_at
                ->first();


            if (isset($checkOtp)) {
                $checkOtp->unique_id = Str::random(10);
                $checkOtp->is_used = '1';
                $checkOtp->ip = $request->header('cf-connecting-ip');
                $checkOtp->save();

                $userPhone = $checkOtp->phone;
                $user = User::with('users_verification')
                    ->where('formatted_phone', $userPhone)
                    ->get()
                    ->transform(function ($user) {
                        if ($user->profile_image != null) $user->profile_image = $user->profile_src;
                        return $user;
                    })
                    ->first();

                if ($user) {
                    $accountDeleteCheck = AccountDeleteRequest::where('user_id', $user->id)->where('status', '!=', AccountDeleteRequest::PENDING)->exists();
                    if ($accountDeleteCheck) {
                        $data = [
                            'user' => null,
                            'token' => $checkOtp->unique_id,
                            'cohost' => $request->cohost != "null" ? $request->cohost : null
                        ];

                        return apiResponse('Failure', ["otp" => ["User is not found"]], 422);
                    }
                    $is_verified = !!ElmDocument::where('user_id', $user->id)->first();
                    $bank = Bank::where('user_id', $user->id)->first();


                    $currentAppVersion = getSetting('app_version');

                    // Determine which method to call based on the app version
                    if (version_compare($currentAppVersion, '2.0.0', '<')) {
                        $payment_gateway = 'moyasar';
                    } else {
                        $payment_gateway = app('PAYMENT_METHOD');
                    }

                    $data = [
                        'user' => array_merge(
                            $user->toArray(),
                            ['show_yaqeen_reservation' => !$is_verified] // Adding the inverted is_verified directly into user object
                        ),
                        'token' => $user->createToken('API Token')->accessToken,
                        'yaqeen_verified' => $is_verified,
                        'bank' => $bank ?? null,
                        'payment_gateway' => $payment_gateway, // Conditional check for api_version

                    ];

                    // $jsonData = ['phone','iPhone','iOS','Safari','true','16_7_8'];

                    // Initialize the Agent instance and set the User-Agent

                    // Get device info
                    // $agent = new Agent();
                    // $agent->setUserAgent($userAgent);
                    // $agent->setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_8) AppleWebKit/537.13+ (KHTML, like Gecko) Version/5.1.7 Safari/534.57.2');


                    // Extract User-Agent from the request body
                    if ($request->has('user_agent')) {

                        $string = $request->input('user_agent');

                        // Convert JSON string to PHP array
                        $useragent = json_decode($string, true);
                        // Update or create device info
                        $device = UserDevice::updateOrCreate(
                            [
                                'user_id' => $user->id,
                                'device_name' => $useragent[0],
                            ],
                            [
                                'device_type' => $useragent[1],
                                'platform' => $useragent[2],
                                'browser' => null,
                                'is_mobile' => $useragent[3] == true ? 1 : 0,
                                'os_version' => $useragent[4],
                                'last_login_at' => now(),
                            ]
                        );
                    }

                    // $data = [
                    //     'user' => $user,
                    //     'token' => $user->createToken('API Token')->accessToken,
                    //     'yaqeen_verified' => $is_verified,
                    //     'show_yaqeen_reservation' => $is_verified,
                    //     'bank' => $bank ?? null,
                    //     'payment_getway' => app('PAYMENT_METHOD') ?? null
                    // ];

                    $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
                    $user_verification->phone = 'yes';
                    $user_verification->save();
                    if (isset($request->cohost)) {

                        $checkCohostToken = CoHostRequest::where('token', $request->cohost)->first();
                        if (isset($checkCohostToken)) {

                            $coHost = new PropertiesCohost();
                            $coHost->property_id = $checkCohostToken->property_id;
                            $coHost->host_id = $checkCohostToken->host_id;
                            $coHost->co_host_id = $user->id;
                            $coHost->co_host_request_id = $checkCohostToken->id;

                            $coHost->save();

                            $checkCohostToken->expire_at = Carbon::now();
                            $checkCohostToken->status = "Accepted";
                            $checkCohostToken->save();
                        }
                    }
                    // return $data;
                    return apiResponse($data, 'Success', 200);
                }
                $data = [
                    'user' => null,
                    'token' => $checkOtp->unique_id,
                    'cohost' => isset($request->cohost) ? $request->cohost : null
                ];
                return apiResponse($data, 'Success', 200);
            } else {
                return apiResponse('Failure', "Invalid OTP. Please try again.", 422);
            }
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function SignUp(Request $request, UserController $user_controller, EmailController $email_controller)
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'first_name' => 'required|max:255',
                'last_name' => 'required|max:255',
                'email' => 'email|max:255|nullable|unique:users,email',
                'token' => 'required',
                // 'password' => 'confirmed',
                // 'password_confirmation' => ''
            ], [
                'first_name.required' => 'First name is required.',
                'last_name.required' => 'Last name is required.',
                'email.required' => 'Email is required.',
                'email.unique' => 'Email Already Exist.',
                'token.required' => 'Token is required.',
                'password.required' => 'Password is required.',
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }
            // $checkEmailExist = User::where('email', $request->email)->where('is_email_verified', 1)->first();
            // if (isset($checkEmailExist)) {
            //     return apiResponse('Failure', "Email Already Exist", 422);
            // }
            $phoneVerificationUpdate = PhoneVerification::where('unique_id', $request->token)->first();
            if (!isset($phoneVerificationUpdate)) {
                return apiResponse('Failure', "Invalid Token", 422);
            }

            $user = new User();
            $user->first_name = $request->first_name;
            $user->last_name = $request->last_name;
            $user->email = isset($request->email) ? $request->email : null;
            $user->phone = $phoneVerificationUpdate->phone;
            $user->profile_image = "";
            $user->formatted_phone = $phoneVerificationUpdate->phone;
            $user->password = isset($request->password) ? bcrypt($request->password) : bcrypt('darent@2020');
            $user->is_email_verified = 1;
            $user->save();

            $user = $user->fresh();
            // $user['yaqeen_verified'] = 1;
            if ($phoneVerificationUpdate) {
                $phoneVerificationUpdate->unique_id = null;
                $phoneVerificationUpdate->save();
            }
            if (isset($request->cohost)) {
                $checkCohostToken = CoHostRequest::where('token', $request->cohost)->first();
                if (isset($checkCohostToken)) {

                    $coHost = new PropertiesCohost;
                    $coHost->property_id = $checkCohostToken->property_id;
                    $coHost->host_id = $checkCohostToken->host_id;
                    $coHost->co_host_id = $user->id;
                    $coHost->co_host_request_id = $checkCohostToken->id;
                    $coHost->save();

                    $checkCohostToken->expire_at = Carbon::now();
                    $checkCohostToken->status = "Accepted";
                    $checkCohostToken->save();
                }
            }

            $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
            $user_verification->phone = 'yes';
            $user_verification->save();

            if (isset($request->email)) {
                $otp = EmailVerification::generateCode();
                $emailVerifyData = [
                    'email' => $user->email,
                    'otp' => $otp,
                    'is_used' => '0',
                ];
                $createdOtp = EmailVerification::updateOrCreate(['email' => $user->email], $emailVerifyData);
            }

            DB::commit();
            // $user_controller->wallet($user->id);
            if (isset($request->noToken)) {
                $loggedIn = Auth::login($user);
                return apiResponse($loggedIn, 'Success', 200);
            }

            $token = $user->createToken('API Token')->accessToken;

            $data = [
                'user' => $user,
                'token' => $token,
                'email_otp' => isset($createdOtp->otp) ? $createdOtp->otp : null,
                'yaqeen_verified' => true,
            ];
            if (isset($request->email)) {
                SendOtpEmailJob::dispatch($user->email, $otp)->onQueue('emails');
            }
            return apiResponse($data, 'Success', 200);
        } catch (\Throwable $th) {
            DB::rollback();
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function CheckEmailValidity(Request $request)
    {
        return $this->helper->checkEmailAvailibility($request->email);
    }

    public function emailVerifyOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'otp' => 'required'
            ], [

                'otp.required' => 'Otp is required.',
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }
            $checkOtp = EmailVerification::where('email', Auth::guard('api')->user()->email)->where('otp', $request->otp)->first();
            if ($checkOtp) {
                $checkOtp->is_used = '1';
                $checkOtp->save();

                $user = User::find(Auth::guard('api')->user()->id);
                $user->is_email_verified = 1;
                $user->save();

                $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
                $user_verification->email = 'yes';
                $user_verification->save();


                return apiResponse(null, 'Success', 200);
            } else {
                return apiResponse("Failure", "Invalid OTP. Please try again.", 422);
            }
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function resendEmailOtp(Request $request)
    {
        try {
            $otp = EmailVerification::generateCode();

            $userEmail = Auth::guard('api')->user()->email;
            $emailVerifyData = [
                'email' => $userEmail,
                'otp' => $otp,
                'is_used' => '0',
            ];

            $createdOtp = EmailVerification::updateOrCreate(['email' => $userEmail], $emailVerifyData);
            SendOtpEmailJob::dispatch($userEmail, $otp)->onQueue('emails');
            // Mail::to($userEmail)->send(new OtpMail($otp));
            return apiResponse($createdOtp, 'Success', 200);
        } catch (\Throwable $th) {
            DB::rollback();
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function updateEmail(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|max:255',
            ], [

                'email.required' => 'Email is required',
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }

            $checkEmailExist = User::where('email', $request->email)->where('is_email_verified', 1)->first();
            if (isset($checkEmailExist)) {
                return apiResponse('Failure', "Email Already Exist", 422);
            }
            $user_verification = UsersVerification::firstOrCreate(['user_id' => Auth::guard('api')->user()->id]);
            $user_verification->email = 'no';
            $user_verification->save();

            $otp = EmailVerification::generateCode();

            $userUpdate = User::find(Auth::guard('api')->user()->id);
            $userUpdate->email = $request->email;
            $userUpdate->is_email_verified = 0;
            $userUpdate->save();

            $userUpdate = $userUpdate->fresh();

            $checkEmail = EmailVerification::where('email', $request->email)->first();

            if (isset($checkEmail)) {
                $checkEmail->otp = $otp;
                $checkEmail->save();
                $createdOtp = $checkEmail;
            } else {
                $emailVerifyData = [
                    'email' => $request->email,
                    'otp' => $otp,
                    'is_used' => '0',
                ];

                $createdOtp = EmailVerification::updateOrCreate(['email' => Auth::guard('api')->user()->email], $emailVerifyData);
            }
            // $createdOtp = EmailVerification::updateOrCreate(['email' => $userUpdate->email], $emailVerifyData);

            $data = [
                'user' => $userUpdate,
                'email_otp' => $createdOtp->otp,
            ];

            SendOtpEmailJob::dispatch($userUpdate->email, $otp)->onQueue('emails');
            // Mail::to()->send(new OtpMail($otp));
            return apiResponse($data, 'Success', 200);
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }


    public function sendOtpNew(Request $request)
    {

        try {
            $client = new Client([
                'base_uri' => config('app.unifonic.base_url'),
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ]
            ]);

            $request['message'] = "Your Darent verification code is " . $request->code . " don't share this code with anyone our team will never ask for the code.            ";
            $res = $client->post('rest/SMS/messages', [
                'json' => [
                    'AppSid' => config('services.unifonic.app_sid'),
                    'SenderID' => config('services.unifonic.sender_id'),
                    'Body' => $request['message'],
                    'Recipient' => $request['phone'],
                    'responseType' => 'JSON',
                    'CorrelationID' => 'CorrelationID',
                    'baseEncode' => 'true',
                    'statusCallback' => 'sent',
                    'async' => 'false',
                ]
            ]);
            if ($res->getStatusCode() == 200) {
                $result = json_decode((string)$res->getBody())->success;
                if (!$result) {
                    throw new \Exception('error ' . $res->getStatusCode());
                }
                return true;
            } else {
                throw new \Exception('error ' . $res->getStatusCode());
            }
        } catch (\Exception $e) {
            Log::debug('sms', ['message' => $e->getMessage(), 'code' => $e->getCode(), 'trace' => $e->getTrace()]);
        }
    }
}
