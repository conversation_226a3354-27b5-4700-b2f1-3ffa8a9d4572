<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ImageHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Helpers\Common;
use App\Models\PropertyManagementRequest;
use App\Http\Resources\HostReservationCollection;
use App\Http\Resources\HostReservationWithPropertiesResource;
use App\Http\Resources\HostPropertiesCollection;
use App\Http\Resources\PropertiesCollection;
use App\Http\Resources\PropertiesResource;
use App\Http\Resources\AllReservationCollection;
use App\Http\Resources\YourReservationResource;
use App\Notifications\UserNotify;
use App\Notifications\UserNotifySms;
use App\Jobs\SendCoHostLink;
use DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;



use App\Models\{
    Favourite,
    Properties,
    PropertyDetails,
    PropertyAddress,
    PropertyPhotos,
    PropertyPrice,
    PropertyType,
    PropertyDates,
    PropertyDescription,
    Currency,
    Settings,
    Bookings,
    SpaceType,
    BedType,
    PropertySteps,
    Country,
    Amenities,
    AmenityType,
    CustomPricing,
    User,
    Reviews,
    PropertiesTemp,
    PhotosTemp,
    PropertyChat,
    PropertyView,
    PropertyChatHead,
    CoHostRequest
};
use Illuminate\Support\Str;
use Carbon\Carbon;
use Image;

use Illuminate\Support\Facades\Auth, Validator;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class HostReservationController extends Controller
{
    private $helper;
    public function __construct()
    {
        $this->helper = new Common;
    }
    public function your_reservation(Request $request)
    {
        try {
            $request->validate([
                'size' => 'required|integer|min:1,max:10000',
                'page' => 'required|integer:min:1'
            ]);
            $size = $request->size;

            $bookings = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth('api')->id());

            $data = [

                'pending_reviews' => $bookings
                    ->where('end_date', '>', Carbon::now()->format('Y-m-d'))
                    ->where('status', 'Accepted')
                    ->orderBy('updated_at', 'desc')
                    ->paginate($size),

                'upcoming_bookings' => $bookings
                    ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                    ->where('status', 'Accepted')
                    ->orderBy('id', 'desc')
                    ->paginate($size),
            ];
            $allData = collect($data)->flatten(1);

            return new HostReservationCollection($allData);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }


        // try {
        //     // dd(Carbon::now()->format('Y-m-d'),Auth::guard('api')->user()->id);
        //     $bookings = Bookings::where('end_date', Carbon::now()->format('Y-m-d'))->where('status', 'Accepted')
        //         ->where('host_id', Auth::guard('api')->user()->id);
        //     $data['totalhosting'] = PropertyView::where('host_id', Auth::guard('api')->user()->id)->count();
        //     $data['todaycheckout'] = $bookings->get();
        //     // $data['todaycheckoutcount'] = $bookings->count();
        //     return response()->json(['message' => 'Success', 'data' => $data], 200);
        // } catch (\Throwable $th) {
        //     return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        // }
    }
    public function allReservation(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth('api')->id());

            return $this->hostReservationGeneralData($request, $booking);

            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostCheckingOut(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth('api')->id())
                ->where('status', 'Accepted')
                ->where('end_date', '=', Carbon::now()->format('Y-m-d'));

            return $this->hostReservationGeneralData($request, $booking);


            // return apiResponse($hostCheckingOut, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostOngoing(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth('api')->id())
                ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted');

            return $this->hostReservationGeneralData($request, $booking);
            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostArivingBooking(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth('api')->id())
                ->where(function ($query) {
                    $query->where('start_date', Carbon::now()->addDays(1)->format('Y-m-d'))
                        ->orWhere('start_date', Carbon::now()->addDays(2)->format('Y-m-d'));
                })
                ->where('status', 'Accepted');

            return $this->hostReservationGeneralData($request, $booking);
            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostPendingReviews(Request $request)
    {
        try {

            $booking = Bookings::where('host_id', Auth::user()->id)
                ->where('end_date', '<', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted')
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('reviews')
                        ->whereColumn('reviews.booking_id', 'bookings.id')
                        ->where('reviews.sender_id', Auth::user()->id); // Adjust the sender_id as needed
                })->WhereExists(function ($booking) {
                    $booking->select(DB::raw(1))
                        ->from('reviews')
                        ->whereColumn('reviews.sender_id', 'bookings.user_id');
                });

            return $this->hostReservationGeneralData($request, $booking);
            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostUpcomingBooking(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth('api')->id())
                ->where('end_date', '>', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted');

            return $this->hostReservationGeneralData($request, $booking);

            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostHistoryBooking(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth('api')->id())
                ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')));

            return $this->hostReservationGeneralData($request, $booking);

            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostPendingBooking(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth('api')->id())
                ->where(fn($q) => $q->whereIn('status', ['Processing', 'Pending', 'Unpaid'])
                    ->where('start_date', '>=', Carbon::now()->format('Y-m-d')));
            return $this->hostReservationGeneralData($request, $booking);

            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $e) {
            $arr = [
                'message' => 'Error',
                'status' => false
            ];
            $code = 500;
            if ($e instanceof ValidationException) {
                $code = 422;
                $arr['errors'] = $e->errors();
            } else {
                $arr['message'] = $e->getMessage();
                $arr['trace'] = $e->getTrace();
            }
            return response()->json($arr, $code);
            // return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostCancelledBooking(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth('api')->id())
                ->whereIn('status', ['Cancelled', 'Declined']);

            return $this->hostReservationGeneralData($request, $booking);

            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostExpiredBooking(Request $request)
    {
        try {
            $booking = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth('api')->id())
                ->where('status', 'Expired');

            return $this->hostReservationGeneralData($request, $booking);

            // return apiResponse($hostCombinedReservation, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostReservationGeneralData($request, $booking)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);

        $property_ids = $request->property_ids;
        $start_date = $request->start_date;
        $end_date = $request->end_date;
        $size = $request->size;

        if (!is_array($property_ids)) {
            if ($property_ids != '') {
                $property_ids = explode(',', $property_ids);
            } else {
                $property_ids = [];
            }
        }

        if (isset($start_date) && isset($end_date)) {
            $booking->whereBetween('bookings.start_date', [$start_date, $end_date])
                ->whereBetween('bookings.end_date', [$start_date, $end_date]);
        }


        if (count($property_ids)) {
            $booking->whereIn('bookings.property_id', $property_ids)->get();
        }

        if (isset($request->booking_code)) {
            $booking->where('bookings.code', 'Like', "%$request->booking_code%");
        }

        if (isset($request->filter_type)) {
            if ($request->filter_type == "check_in") {
                switch ($request->filter_value) {
                    case 'new':
                        $booking->orderBy('bookings.start_date', 'desc');
                        break;
                    case 'old':
                        $booking->orderBy('bookings.start_date', 'asc');
                        break;
                }
            }
            if ($request->filter_type == "booking") {
                switch ($request->filter_value) {
                    case 'new':
                        $booking->orderBy('bookings.updated_at', 'desc');
                        break;
                    case 'old':
                        $booking->orderBy('bookings.updated_at', 'asc');
                        break;
                }
            }
        } else {
            $booking->orderBy('bookings.id', 'desc');
        }
        $bookings = $booking->paginate($size);

        $bookings->getCollection()->transform(function ($booking) {
            // Check if a chat_head exists for the given host, guest, and property
            $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
            return $booking;
        });


        return new HostReservationCollection($bookings);

        // $propertyData = [];
        // foreach ($bookings as $booking) {
        //     $bookingProperties = $booking->properties; // Access the associated property

        //     $propertyData[] = $bookingProperties;
        // }
        // $properties = new PropertiesResource($propertyData);

        // return new HostReservationWithPropertiesResource([$hostReservationCollection, $properties]);
    }
    public function hostProperties(Request $request)
    {
        try {
            $data = isHostOrCohostQuery(PropertyView::with('property_photos'), Auth::guard('api')->user()->id, conId: 'id')->get();
            $properties = PropertiesResource::collection($data);
            return apiResponse($properties, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function hostList(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'size' => 'required|integer|min:1,max:10000',
                'page' => 'required|integer:min:1'
            ]);
            $size = $request->size;
            if ($validator->fails()) {
                return apiResponse($validator->errors(), "Failure", 422);
            }

            $properties = isHostOrCohostQuery(Properties::with('property_steps', 'property_address'), Auth::guard('api')->user()->id, conId: 'id');

            if ($request->method() == 'GET') {
                $properties = $properties->where('slug', '!=', null)
                    ->where('properties.status', '=', 'Listed');
                // $properties = $properties->where('slug', '!=', null)->where('properties.visibility', '=', '1')->where('properties.status', '=', 'Listed');
                $properties = $properties->distinct('properties.id')->paginate($size);
                return new HostPropertiesCollection($properties);
            }

            $bedrooms = $request->input('bedrooms');
            $beds = $request->input('beds');
            $bathrooms = $request->input('bathrooms');
            $amenities = $request->input('amenities');
            $instant_off = $request->input('instant_off');
            $property_status = $request->input('property_status');
            $property_type = $request->input('property_type');
            $min_price = $request->input('min_price');
            $max_price = $request->input('max_price');
            $search = $request->input('search');


            if (!is_array($property_type)) {
                if ($property_type != '') {
                    $property_type = explode(',', $property_type);
                } else {
                    $property_type = [];
                }
            }

            $max_price = (!isset($max_price) || $max_price >= 5000) ? ($maxPrice ?? FacadesDB::table('property_price')->max('price')) : $max_price;

            $properties_whereIn = [];
            if (count($property_type)) {
                $properties_whereIn['properties.property_type'] = $property_type;
            }

            if (isset($bedrooms) && $bedrooms !== 'Any') {
                $properties_where['properties.bedrooms'] = $bedrooms;
            }

            if (isset($bathrooms) && $bathrooms !== 'Any') {
                $properties_where['properties.bathrooms'] = $bathrooms;
            }

            if (isset($beds) && $beds !== 'Any') {
                $properties_where['properties.beds'] = $beds;
            }

            if (!is_array($amenities)) {
                if ($amenities != '') {
                    $amenities = explode(',', $amenities);
                } else {
                    $amenities = [];
                }
            }

            if ($properties_whereIn) {
                foreach ($properties_whereIn as $row_properties_whereIn => $value_properties_whereIn) {
                    $properties = $properties->whereIn($row_properties_whereIn, array_values($value_properties_whereIn));
                }
            }

            if (isset($properties_where)) {
                foreach ($properties_where as $row => $value) {

                    $operator = '=';
                    if ($value == '') {
                        $value = 0;
                    }
                    $properties = $properties->where($row, $operator, $value);
                }
            }

            if (count($amenities)) {
                foreach ($amenities as $amenities_value) {
                    $properties = $properties->whereRaw('find_in_set(' . $amenities_value . ', properties.amenities)');
                }
            }

            if (isset($instant_off)) {
                $properties = $properties->where('properties.booking_type', '=', 'request');
            }

            if (isset($property_status)) {
                switch ($property_status) {
                    case "Listed":
                        $properties =  $properties->where('properties.visibility', '=', '1')
                            ->where('properties.status', '=', 'Listed')
                            ->whereIn('properties.id', function ($query) {
                                $query->select('property_id')
                                    ->from('StepsCompleted')
                                    ->where('total_steps', '=', 0);
                            });
                        break;
                    case "Unlisted":
                        $properties =  $properties->where(function ($query) {
                            $query->where('properties.visibility', '=', '0')
                                ->orWhere('properties.status', '=', 'Unlisted');
                        })
                            ->whereIn('properties.id', function ($query) {
                                $query->select('property_id')
                                    ->from('StepsCompleted')
                                    ->where('total_steps', '=', 0);
                            });
                        break;
                    case "Processing":
                        $properties = $properties->whereRaw('properties.id IN (SELECT property_id FROM StepsCompleted WHERE total_steps != 0)');
                        break;
                }
            }
            if (isset($search)) {
                $properties = $properties->whereAny(['properties.property_code', 'properties.name','properties.name_ar'], 'LIKE', '%' . $search . '%');
            }
            $properties = $properties->orderBy('properties.updated_at', 'desc')->distinct('properties.id')->paginate($size);

            $request->merge(['host_id' => auth()->id()]);

            return new HostPropertiesCollection($properties);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }
    public function singleProperty(Request $request)
    {
        try {
            $property = isHostOrCohostQuery(Properties::with([
            'property_photos' => function ($query) {
                    $query->whereNotIn('id', function ($subQuery) {
                        $subQuery->select('remove_photo_id')
                            ->from('photos_temp')
                            ->whereNotNull('remove_photo_id'); // Ensure `remove_photo_id` is not null
                    });
                },
        'photos_temp', 'property_address', 'property_price', 'property_description', 'icalImport']), Auth::guard('api')->user()->id, conId: 'id')
                ->where('slug', $request->slug) // Check for matching slug
                ->orWhere('property_code', $request->slug) // If slug doesn't match, check for matching property_code
                ->first();
            $property['property_cohost'] = CoHostRequest::selectRaw('IFNULL(pchs.id, co_host_requests.id) AS id, (pchs.id IS NULL) AS is_request, IFNULL(IFNULL(IF(us.id IS Null, user_name, us.first_name), co_host_requests.formatted_phone), co_host_requests.email) AS name, IF(pchs.id IS Null, co_host_requests.status, "Accepted") AS status,
            IFNULL(us.profile_image, "icons/user.svg") AS image, IFNULL(us.id, co_host_requests.user_id) AS co_host_id, IFNULL(pchs.created_at, co_host_requests.created_at) AS created_at')
                ->join(DB::raw('(SELECT user_id, property_id, id AS id FROM co_host_requests GROUP BY user_id, property_id) AS tchrs'), 'tchrs.id', 'co_host_requests.id')
                ->leftJoin('properties_cohosts AS pchs', 'pchs.co_host_request_id', 'co_host_requests.id')
                ->leftJoin('users AS us', 'us.id', 'co_host_requests.user_id')
                ->where('co_host_requests.property_id', $property->id)->where(fn($q) => $q->whereNull('pchs.id')->orWhereNull('pchs.deleted_at'))
                ->orderBy('name')->get();
            if ($property && isset($property->property_photos)) {
                foreach ($property->property_photos as &$photo) {
                    $photo['image'] = $photo['photo'];
                }
            }
            if ($property && isset($property->photos_temp)) {
                foreach ($property->photos_temp as &$temp) {
                    $temp['photo'] = $temp['photo_path'];
                }
            }
            $property['license_mandatory'] = $property->property_type == 22 || $property->property_address->city == "AlUla" ? false : true;

            // $propertyCoHost = CoHostRequest::where('property_id',$property->id)->where('status',!=,'Expired');

            return apiResponse($property, '', 200);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }

    public function updateProperty(Request $request)
    {
        try {
            $custom_amenities = $request->input('custom_amenities');
            $step            = $request->step;
            $property_id     = $request->id;
            $data['result']  = $property  = isHostOrCohostQuery(Properties::query(), auth()->id(), conId: 'id')->findOrFail($property_id);

            if ($step == "photos") {

                $validate = Validator::make($request->all(), [
                    'file'  => 'required|max:2999', //3MB

                ], [
                    'file.required' => 'Please select Image(s) to upload.',
                    'file.max' => 'Image(s) may not be greater than 3MB.',
                    // 'edit.required' => 'Edit param required in binary (1 is request for edit photos & 0 for add photos)',
                ]);

                if ($validate->fails()) {
                    return apiResponse(['error' => $validate->errors()], 'failure', 422);
                }

                $uploadedPhotos = PropertyPhotos::where('property_id', $property_id)->orderBy('serial', 'asc')->get();

                $totalPhotos = count($uploadedPhotos); //Uploaded and Approved Photos
                $reqPhotos = count($request->file('file'));
                $photos_temp = PhotosTemp::where('property_id', $property_id)->count();

                $allphotos = $totalPhotos + $reqPhotos + $photos_temp;


                if ($allphotos) {
                    if ($allphotos < 4) {
                        if ($allphotos > 20) {
                            return apiResponse(['error' => ['file' => 'Please upload between 4 and 20 images.']], 'failure', 422);
                        }
                    }
                }

                $photosId = [];
                if ($request->hasFile('file')) {

                    $exist = PropertyPhotos::orderBy('serial', 'desc')
                        ->select('serial')
                        ->where('property_id', $property_id)
                        ->take(1)->first();

                    for ($i = 0; $i < count($request->file('file')); $i++) {
                        $ext = pathinfo($_FILES["file"]["name"][$i], PATHINFO_EXTENSION);
                        if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'JPG') {
                            $imgFile = Image::make($_FILES["file"]["tmp_name"][$i]);
                            $imgFile->resize(null, 400, function ($constraint) {
                                $constraint->aspectRatio();
                                $constraint->upsize();
                            });

                            $image_url = ImageHelper::upload($imgFile, 'property/' . $property_id);
                            if ($property->status == 'Unlisted') {
                                // IF ADD NEW PHOTOS (NEW PROPERTY BUT NOT LISTED)
                                $photos = new PropertyPhotos;
                                $photos->property_id = $property_id;
                                $photos->photo = $image_url;
                                $photos->serial = 1;
                                $photos->cover_photo = 1;

                                $exist = PropertyPhotos::orderBy('serial', 'desc')
                                    ->select('serial')
                                    ->where('property_id', $property_id)
                                    ->take(1)->first();

                                if (!empty($exist->serial)) {
                                    $photos->serial = $exist->serial + 1;
                                    $photos->cover_photo = 0;
                                }
                                // $this->helper->getLogs($photos, 'api');
                                $photos->save();
                                array_push($photosId, $photos->id);
                            } else {

                                // IF EDIT PHOTOS
                                $photos = new PhotosTemp;
                                $photos->property_id = $property_id;
                                $photos->photo = $image_url;
                                $photos->cover_photo = !empty($exist->serial) == true ?  0 : 1;

                                // $this->helper->getLogs($photos, 'api');
                                $photos->save();
                                array_push($photosId, $photos->id);
                            }
                        } else {
                            return apiResponse(['error' => ['file.mimes' => 'Only jpeg, jpg, and png images are allowed.']], 'failure', 422);
                        }
                    }
                }

                return response()->json(["message" => "Success", "data" => ["property_id" => $property_id, 'photosid' => $photosId]], 200);
            } elseif ($step == "numberofRoom") {
                if ($request->isMethod('Post')) {
                    $rules = array(
                        'bedroom'    => 'required|numeric|min:0',
                        'single_beds'    => 'required|numeric|min:0',
                        'double_beds'    => 'required|numeric|min:0',
                        // 'beds'       => 'required_if:bedroom,>0|numeric',
                        'bathroom'   => 'required',
                    );

                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return back()->withErrors($validator)->withInput();
                    } else {
                        $beds = $request->single_beds + $request->double_beds;
                        if ($request->bedroom > 0 && $beds < 1) {
                            return response()->json(['message' => 'failure', 'error' => 'Select at least 1 bed when the bedroom is greater than 0'], 422);
                        }

                        $propertyamenities                  = Properties::find($request->id);
                        $propertyamenities->bedrooms        = $request->bedroom;

                        $propertyamenities->single_beds     = $request->bedroom ? $request->single_beds : 0;
                        $propertyamenities->double_beds     = $request->bedroom ? $request->double_beds : 0;

                        $propertyamenities->beds            = $request->bedroom ? $beds : 0;
                        $propertyamenities->bathrooms       = $request->bathroom;
                        $this->helper->getLogs($propertyamenities, 'api');
                        $propertyamenities->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "basic") {

                if ($request->isMethod('Post')) {
                    $property                     = Properties::find($property_id);
                    $property->accommodates       = $request->adult + $request->children;
                    $property->adult_guest        = $request->adult;
                    $property->children_guest     = $request->children;
                    $this->helper->getLogs($property, 'api');
                    $property->save();
                    return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                }
            }
            elseif ($step == "title") {
                if ($request->isMethod('Post')) {
                    $validationRules = [
                        'name'    => 'required|max:50',
                        'name_ar' => 'required|max:500',
                    ];
                    $validator = Validator::make($request->all(), $validationRules);
                    if ($validator->fails()) {
                        return apiResponse(['error' => $validator->errors()], 'failure', 422);
                    }

                    $property               = Properties::find($property_id);
                    $property->name         = $request->name;
                    $property->name_ar      = $request->name_ar;

                    $this->helper->getLogs($property, 'api');
                    $property->save();
                    return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                }
            }
            elseif ($step == "cancellation") {
                if ($request->isMethod('Post')) {
                    $property                     = Properties::find($property_id);
                    $updateUserPolicy             = User::find($property->host_id);
                    $updateUserPolicy->cancel_policy      = $request->cancellation;
                    $property->cancellation = $request->cancellation;
                    $this->helper->getLogs($updateUserPolicy, 'api');
                    $this->helper->getLogs($property, 'api');
                    $property->save();
                    $updateUserPolicy->save();
                    return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                }
            }
            elseif ($step == "location") {
                if ($request->isMethod('Post')) {
                    // dd($request);

                    $rules = array(
                        'address_line_1'    => 'required|max:250',
                        'country'  => 'required',
                        'city' => 'required',
                        'state' => 'required',
                        'district' => 'required',
                        'latitude' => 'required|not_in:0',
                    );

                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return apiResponse(['error' => $validator->errors()], 'failure', 422);
                    } else {
                        $property_address                 = PropertyAddress::where('property_id', $property->id)->first();
                        $property_address->address_line_1 = $request->address_line_1;
                        $property_address->address_line_2 = isset($request->address_line_2) ? $request->address_line_2 : NULL;
                        $property_address->latitude       = $request->latitude;
                        $property_address->longitude      = $request->longitude;
                        $property_address->city           = $request->city;
                        $property_address->state          = $request->state;
                        $property_address->district       = $request->district;
                        $property_address->country        = $request->country;
                        $property_address->postal_code    = $request->postal_code ?? NULL;

                        $this->helper->getLogs($property_address, 'api');
                        $property_address->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id, 'title' => $property->name]], 200);
                    }
                }
            }
            elseif ($step == "description") {
                if ($request->isMethod('post')) {

                    $rules = array(
                        'summary'  => 'required|max:1000',
                        'summary_ar'  => 'required|max:1000'
                    );
                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $jsonAboutData = json_encode([
                            'about_place' => $request->about_place,
                            'about_place_ar' => $request->about_place_ar
                        ]);
                        $jsonGuestAccessData = json_encode([
                            'guest_can_access' => $request->guest_can_access,
                            'guest_can_access_ar' => $request->guest_can_access_ar
                        ]);
                        $jsonGuestInteractionData = json_encode([
                            'guest_interaction' => $request->guest_interaction,
                            'guest_interaction_ar' => $request->guest_interaction_ar
                        ]);
                        $jsonOtherData = json_encode([
                            'other' => $request->other,
                            'other_ar' => $request->other_ar
                        ]);

                        $jsonneighborhoodData = json_encode([
                            'neigthborhood' => $request->neighborhood,
                            'neigthborhood_ar' => $request->neighborhood_ar
                        ]);
                        $jsongetaroundData = json_encode([
                            'get_around' => $request->get_around,
                            'get_around_ar' => $request->get_around_ar
                        ]);

                        $property_description              = PropertyDescription::where('property_id', $property_id)->first();
                        $property_description->summary     = $request->summary;
                        $property_description->summary_ar  = $request->summary_ar;
                        $property_description->about_place = $jsonAboutData;
                        $property_description->guest_can_access = $jsonGuestAccessData;
                        $property_description->interaction_guests = $jsonGuestInteractionData;
                        $property_description->other = $jsonOtherData;
                        $property_description->about_neighborhood = $jsonneighborhoodData;
                        $property_description->get_around = $jsongetaroundData;

                        $this->helper->getLogs($property_description, 'api');
                        $property_description->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "amenities") {

                if ($request->isMethod('Post') && is_array($request->amenities)) {
                    $rules = array(
                        'amenities'  => 'required',
                    );

                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {

                        if ($custom_amenities) {
                            Properties::storeCustomAmenitiesApi($property_id, $custom_amenities);
                        }

                        $propertyamenities            = Properties::find($request->id);
                        $propertyamenities->amenities = implode(',', $request->amenities);
                        $this->helper->getLogs($propertyamenities, 'api');
                        $propertyamenities->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "houserules") {
                if ($request->isMethod('Post') && is_array($request->houserules)) {
                    $rules = array(
                        'houserules'  => 'required',
                    );

                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {

                        $propertyamenities            = Properties::find($request->id);
                        $existingAmenities = [$propertyamenities->amenities];
                        $newAmenities = $request->houserules;
                        $combinedAmenities = array_merge($existingAmenities, $newAmenities);
                        // Step 3: Update the column with the combined data
                        $propertyamenities->amenities = implode(',', $combinedAmenities);
                        $this->helper->getLogs($propertyamenities, 'api');
                        $propertyamenities->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "price") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'price' => 'required|numeric|min:50',
                        // 'weekly_discount' => 'nullable|numeric|max:99|min:0',
                        // 'monthly_discount' => 'nullable|numeric|max:99|min:0'
                    );

                    $data['special_days'] = Country::where(
                        'short_name',
                        !!$property->property_address ? $property->property_address->country : 'SA'
                    )->orWhere('short_name', 'SA')->first()->special_days;

                    foreach ($data['special_days'] as $special_day) {
                        $rules["price_{$special_day}"] = 'required|numeric|min:50';
                    }

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $special_days_price = [];
                        foreach ($data['special_days'] as $special_day) {
                            $special_days_price[$special_day] = $request->{"price_{$special_day}"};
                        }

                        $property_price                    = PropertyPrice::where('property_id', $property_id)->first();
                        $property_price->price             = $request->price;
                        $property_price->special_days_price = $special_days_price;

                        $this->helper->getLogs($property_price, 'api');
                        $property_price->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "extraprice") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'cleaning_fee' => 'nullable|numeric|max:9999|min:0',
                    );

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {

                        $property_price                    = PropertyPrice::where('property_id', $property_id)->first();
                        $property_price->cleaning_fee      = isset($request->cleaning_fee) ? $request->cleaning_fee : 0;

                        $this->helper->getLogs($property_price, 'api');
                        $property_price->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "cleaning_fee") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'cleaning_fee' => 'required|numeric|min:0',
                    );

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {

                        $property_price                    = PropertyPrice::where('property_id', $property_id)->first();
                        $property_price->cleaning_fee      = $request->cleaning_fee;

                        $this->helper->getLogs($property_price, 'api');
                        $property_price->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "security_fee") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'security_fee' => 'required|numeric|min:0',
                    );

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {

                        $property_price                    = PropertyPrice::where('property_id', $property_id)->first();
                        $property_price->security_fee      = $request->security_fee;

                        $this->helper->getLogs($property_price, 'api');
                        $property_price->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "question") {
                if ($request->isMethod('post')) {
                    $property           = Properties::find($property_id);
                    $property->booking_type = $request->booking_type;
                    $property->status = $property->status;
                    $property->visibility = $property->visibility;

                    $this->helper->getLogs($property, 'api');
                    $property->save();

                    return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                }
            }
            elseif ($step == "weekly_discount") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'weekly_discount' => 'required|numeric|min:0|max:100',
                    );

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $propertyPrice = PropertyPrice::where('property_id', $property_id)->first();
                        $propertyPrice->weekly_discount = $request->weekly_discount;
                        $weekly_amount = $propertyPrice->price * 7;
                        $weekly_amount_with_discount = $weekly_amount - ($weekly_amount * ($request->weekly_discount / 100));

                        $this->helper->getLogs($propertyPrice, 'api');
                        $propertyPrice->save();

                        return response()->json(['message' => 'Success', 'data' => [
                            'propertyid' => $property_id,
                            'weekly_amount' => $weekly_amount,
                            'weekly_amount_with_discount' => $weekly_amount_with_discount
                        ]], 200);
                    }
                }
            }
            elseif ($step == "monthly_discount") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'monthly_discount' => 'required|numeric|min:0|max:100',
                    );

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $propertyPrice = PropertyPrice::where('property_id', $property_id)->first();
                        $propertyPrice->monthly_discount = $request->monthly_discount;
                        $monthly_amount = $propertyPrice->price * 30;
                        $monthly_amount_with_discount = $monthly_amount - ($monthly_amount * ($request->monthly_discount / 100));

                        $this->helper->getLogs($propertyPrice, 'api');
                        $propertyPrice->save();

                        return response()->json(['message' => 'Success', 'data' => [
                            'propertyid' => $property_id,
                            'monthly_amount' => $monthly_amount,
                            'monthly_amount_with_discount' => $monthly_amount_with_discount
                        ]], 200);
                    }
                }
            }
            elseif ($step == "nightsandtime") {
                if ($request->isMethod('post')) {
                    $rules = array(
                        'checkinTime' => 'required',
                        'checkoutTime' => 'required',
                    );

                    $validator = Validator::make($request->all(), $rules);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $property = Properties::find($property_id);
                        $property->checkinTime = $request->checkinTime;
                        $property->checkoutTime = $request->checkoutTime;

                        $this->helper->getLogs($property, 'api');
                        $property->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "status") {
                if ($request->isMethod('post')) {

                    $rules = array(
                        'status' => 'required',
                        // "reason" => "nullable"
                    );
                    $validator = Validator::make($request->all(), $rules);
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $property = Properties::find($property_id);
                        // $property->status = $request->status;
                        if ($request->status == "Listed") {
                            $propertyVisibility = 1;
                        } else {
                            $propertyVisibility = 0;
                        }

                        // $property->reason = $request->reason ?? null;
                        $property->visibility       = $propertyVisibility;
                        $this->helper->getLogs($property, 'api');
                        $property->save();

                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
            elseif ($step == "co_host") {
                if ($request->isMethod('post')) {
                    $validator = Validator::make($request->all(), [
                        'phone' => 'required_without:email|string|min:9|max:14',
                        'email' => 'required_without:phone|email'
                    ]);
                    $key = !!$request->phone ? 'phone' : 'email';
                    $data = $request->input($key);
                    if (!!$request->phone) {
                        $data = $request->code . $data;
                    }
                    if (User::leftJoin('properties AS ps', fn($q) => $q->on('ps.host_id', 'users.id')->where('ps.id', $property_id))->leftJoin('properties_cohosts AS pchs', fn($q) => $q->where('property_id', $property_id))->where(fn($q) => $q->where('ps.host_id', auth()->id())->orWhereRaw('pchs.co_host_id = users.id')->orWhere(fn($q) => $q->where('pchs.co_host_id', auth()->id())->whereNull('pchs.deleted_at')))->where('users.' . $key, $data)->exists()) {
                        throw ValidationException::withMessages([
                            $key => ['The selected user cannot be made a cohost.']
                        ]);
                    }
                    if (CoHostRequest::where('property_id', $property_id)->where(($key == 'phone' ? 'formatted_' : '') . $key, $data)->whereDate('expire_at', '>=', now()->format('Y-m-d'))->exists()) {
                        throw ValidationException::withMessages([
                            $key => ['Cant sent request, you have already pending request.']
                        ]);
                    }
                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    } else {
                        $property = Properties::find($property_id);
                        $phone = $request->code . $request->phone;
                        $checkUser  = User::where('formatted_phone', $phone)->first();
                        $coHostRequest =  CoHostRequest::create([
                            'property_id' => $property_id,
                            'user_id' => !!$checkUser ? $checkUser->id : null,
                            'host_id' => $property->host_id,
                            'user_name' => !!$checkUser ? $checkUser->getFullNameAttribute() : $phone,
                            'formatted_phone' => !!$checkUser ? $checkUser->formatted_phone : $phone,
                            'email' => !!$checkUser ?? $checkUser->email ?? null,
                            'token' => Str::random(20),
                            'expire_at' => now()->addWeek(),
                            'status' => 'Not Accepted'
                        ]);

                        $baseURL = config('app.url') . '?login&cohost=' . $coHostRequest->token; // Get the base URL from your application configuration
                        if ($phone) {
                            $coHostRequest->notify(new UserNotify(
                                'host.send.cohost.request.user',
                                $baseURL,
                                [':url' => $baseURL]
                            ));
                        }
                        if (isset($request->email)) {
                            SendCoHostLink::dispatch($checkUser->email, $baseURL)->onQueue('emails');
                        }
                        return response()->json(['message' => 'Success', 'data' => ['propertyid' => $property_id]], 200);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::debug('error', ['message' => $e->getMessage(), 'code' => $e->getCode(), 'trace' => $e->getTrace()]);
            $arr = [
                'message' => 'Error',
                'status' => false
            ];
            $code = 500;
            if ($e instanceof ValidationException) {
                $code = 422;
                $arr['error'] = $e->errors();
            } else {
                $arr['message'] = $e->getMessage();
                $arr['trace'] = $e->getTrace();
            }
            return response()->json($arr, $code);
            // return response()->json(['error' => $th->getmessage()], 500);
        }
    }

    public function requestManagement(Request $request){
        try{

            $validator = Validator::make($request->all(), [
                'owner_name'     => 'required|string|max:255',
                'email'          => 'required|email|max:255',
                'property_name'  => 'required|string|max:255',
                'units'          => 'required|integer|min:0',
                'location'       => 'required|string|max:255',
                'city_id'        => 'required|exists:cities,id',
                'contact_no'     => 'required|string|max:20',
            ]);
        
            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            }
            $request->merge([
                'user_id' => auth()->user()->id
            ]);
            $manage = PropertyManagementRequest::create($request->all());
            return response()->json(['message' => 'success', 'data' => []], 200);

        } catch(\Exception $e) {
            return response()->json(['message' => 'error', 'data' => []], 500);
        }
    }
}
