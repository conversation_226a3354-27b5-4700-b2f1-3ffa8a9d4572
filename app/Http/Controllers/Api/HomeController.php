<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Helpers\Common;
use App\Http\Resources\ComingReservationCollection;
use App\Http\Resources\HistoryReservationCollection;
use App\Http\Resources\CancelledReservationCollection;
use App\Http\Resources\BookingHistoryCollection;
use App\Http\Resources\ReviewCollection;
use App\Http\Resources\RecommendedPropertiesResource;
use App\Http\Resources\RecommendedPropertiesCollection;
use App\Http\Resources\SearchFilterResource;
use App\Http\Resources\BannerResource;
use App\Http\Resources\SettingResource;
use App\Http\Resources\InboxCollection;
use App\Http\Resources\CombinedResource;
use App\Http\Resources\NotificationCollection;
use App\Http\Resources\BookmarkCollection;
use App\Http\Resources\UserCombinedResource;
use App\Http\Resources\CompletePropertiesCollection;
use App\Http\Resources\IncompletePropertiesCollection;
use App\Http\Resources\MyBookingVersionTwoCollection;
use App\Http\Resources\PromoCodeCollection;
use App\Http\Resources\AccountManagerCollection;
use App\Http\Resources\HostReservationCollection;
use App\Http\Resources\HostCombinedResource;
use App\Http\Resources\ReceiptResource;
use App\Http\Resources\PropertiesResource;
use App\Http\Resources\HostPropertiesCollection;
use App\Http\Resources\ReviewPaginatedResource;
use App\Http\Services\MTTourismService;
use Illuminate\Support\Facades\Storage;
use View;
use DateTime;
use Auth, Validator, Str;
use DB;
use Session;
use Carbon\Carbon;

use App\Models\{
    Bookings,
    BookingDetails,
    Messages,
    Penalty,
    Payouts,
    Properties,
    PayoutPenalties,
    PropertyDates,
    PropertyFees,
    Settings,
    Reviews,
    Notifications,
    PropertyType,
    SpaceType,
    Amenities,
    AmenityType,
    Country,
    Currency,
    StartingCities,
    Favourite,
    LocalizationKeyword,
    PromoCode,
    TicketTypes,
    User,
    Banners,
    City,
    HostLicense,
    PropertyAddress,
    PropertyChatHead,
    PropertyChat,
    PropertyView
};
use Exception;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator as FacadesValidator;

class HomeController extends Controller
{
    private $helper;

    public function __construct()
    {
        $this->helper = new Common;
    }

    public function index()
    {

        try {
            $properties      = Properties::where('status', 'listed')
                ->where('visibility', '1')
                ->with('users', 'property_price', 'property_address', 'property_description')
                ->where('recomended', '1')
                ->take(8)
                ->inRandomOrder()
                ->paginate();
            // $data['property_type']   = PropertyType::getAll()->where('status', 'Active');
            // return response()->json(["message"=>"Success","data"=>$properties]);

            // return RecommendedPropertiesResource::collection($properties);
            return new RecommendedPropertiesCollection($properties);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function booking_History(Request $request)
    {
        try {
            $request->validate([
                'size' => 'required|integer|min:1,max:10000',
                'page' => 'required|integer:min:1'
            ]);
            $size = $request->size;
            $data = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth()->id())
                ->where(fn($q) => $q->where('status', '=', 'Accepted')
                    ->where('end_date', '<', now()->format('Y-m-d')))
                ->orderBy('bookings.id', 'desc')->paginate($size);
            return new BookingHistoryCollection($data);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function getPrice(Request $request)
    {
        $data = json_decode($this->helper->getPrice($request->property_id, $request->checkin, $request->checkout, $request->guest_count));
        if ($data->status = "minimum stay" && isset($data->minimum)) {
            return response()->json(["message" => "failure", "error" => "minimum stay", "data" => $data], 422);
        } elseif ($data->status = "nights become max" && isset($data->max_nights)) {
            return response()->json(["message" => "failure", "error" => "Booking Nights must be less than or equal to Max nights " . $data->max_nights], 422);
        } elseif ($data->status = "nights become min" && isset($data->min_nights)) {
            return response()->json(["message" => "failure", "error" => "Booking Nights must be greater than or equal to Min nights " . $data->min_nights], 422);
        }

        return response()->json(["message" => "Success", "data" => $data]);
    }

    public function guestComingReservation(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        $size = $request->size;

        $data = Bookings::with('users', 'properties')
            ->where('user_id', auth()->guard('api')->id())
            ->whereIn('status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            // ->where('checkin_time', '>', Carbon::now()->format('H:i:s'))
            ->orderBy('id', 'desc')
            ->paginate($size);
        $data->getCollection()->transform(function ($booking) {
            $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
            return $booking;
        });

        return new ComingReservationCollection($data);
    }
    public function guestHistoryReservation(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        $size = $request->size;
        // dd(auth()->guard('api')->id());
        $data = Bookings::with('users', 'properties')
            ->where('user_id', auth()->id())
            ->where('status', 'Accepted')
            ->where('end_date', '<=', Carbon::now()->format('Y-m-d'))
            ->where('checkout_time', '<', Carbon::now()->format('H:i:s'))
            ->orderBy('id', 'desc')
            ->paginate($size);

        $data->getCollection()->transform(function ($booking) {
            // Check if a chat_head exists for the given host, guest, and property
            $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead(
                $booking->user_id,
                $booking->host_id,
                $booking->property_id
            );
            return $booking;
        });

        return new HistoryReservationCollection($data);
        // return response()->json(["message"=>"Success","data"=>$data],200);
    }
    public function guestCancelledReservation(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        $size = $request->size;
        // dd(auth()->guard('api')->id());
        $data = Bookings::with('users', 'properties')->where('user_id', auth()->id())
            ->where(fn($q) => $q->where('status', '=', 'Cancelled')->orWhere('status', '=', 'Declined'))->orderBy('updated_at', 'desc')->paginate($size);

        $data->getCollection()->transform(function ($booking) {
            // Check if a chat_head exists for the given host, guest, and property
            $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead(
                $booking->user_id,
                $booking->host_id,
                $booking->property_id
            );
            return $booking;
        });
        return new CancelledReservationCollection($data);
    }
    public function guestExpiredReservation(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        $size = $request->size;
        // dd(auth()->guard('api')->id());
        $data = Bookings::with('users', 'properties')->where('user_id', auth()->id())
            ->where('status', '=', 'Expired')->orderBy('updated_at', 'desc')->paginate($size);
        $data->getCollection()->transform(function ($booking) {
            $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
            return $booking;
        });
        return new CancelledReservationCollection($data);
    }
    public function hostCancelledReservation(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        $size = $request->size;
        $data = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth()->id())
            ->whereIn('status', ['Cancelled', 'Declined'])
            ->orderBy('bookings.updated_at', 'desc')->paginate($size);
        return new BookingHistoryCollection($data);
    }

    public function guestCurrentReservation(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        $size = $request->size;
        $data = Bookings::selectRaw('bookings.*, pchs.id as chat_head_id')
            ->leftJoin(
                'property_chat_heads as pchs',
                fn($q1) => $q1->on('pchs.property_id', 'bookings.property_id')
                    ->where('pchs.guest_id', auth()->guard('api')->id())
            )->with('users', 'properties')
            ->where('user_id', auth()->id())
            ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
            ->where('checkin_time', '<=', Carbon::now()->format('H:i:s'))
            ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
            ->where('checkout_time', '>=', Carbon::now()->format('H:i:s'))
            ->where('status', 'Accepted')
            ->orderBy('updated_at', 'desc')
            ->distinct()
            ->paginate($size);

        $data->getCollection()->transform(function ($booking) {
            // Check if a chat_head exists for the given host, guest, and property
            $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead(
                $booking->user_id,
                $booking->host_id,
                $booking->property_id
            );
            return $booking;
        });

        return new CancelledReservationCollection($data);
    }
    public function hostCurrentReservation(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        $size = $request->size;
        $data = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth()->id())
            ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
            ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
            ->where('status', 'Accepted')
            ->orderBy('bookings.updated_at', 'desc')
            ->paginate($size);
        // return response()->json($data, 200);
        return new BookingHistoryCollection($data);
    }
    public function hostExpiredReservation(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        $size = $request->size;
        $data = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth()->id())
            ->where('status', 'Expired')
            ->orderBy('bookings.updated_at', 'desc')
            ->paginate($size);
        // return response()->json($data, 200);
        return new BookingHistoryCollection($data);
    }
    public function HostReview(Request $request)
    {
        $page = $request->page ?? 5;

        if (request()->route()->getPrefix() == "api/v2") {
            $data = Reviews::with(['users_from', 'properties'])->where($request->type, Auth::id())->where('ispublic', true)->orderByDesc('reviews.id')->paginate(4);
            $reviews = Reviews::with(['users_from', 'properties'])->where($request->type, Auth::id())->where('ispublic', true)->orderByDesc('reviews.id')->get();
        } else {
            $data = Reviews::with(['users'])->where('receiver_id', Auth::id())->where('ispublic', true)->orderByDesc('reviews.id')->get();
            $reviews = $data;  // Get all items for non-paginated results
        }

        // Calculate averages
        $averages = $this->calculateAverages($reviews);

        // Scale individual review scores
        // $scaledReviews = array_map([$this, 'scaleReviewScores'], $reviews);

        // Build response for API v2 or v3
        $response = [
            'reviews' => new ReviewPaginatedResource($data),
            'average' => $averages
        ];

        return apiResponse($response, 'success', 200);
    }

    // Function to calculate and scale averages
    private function calculateAverages($reviews)
    {
        $totalReviews = count($reviews);
        if ($totalReviews === 0) {
            return [
                'cleanliness' => 0,
                'communication' => 0,
                'accuracy' => 0,
                'location' => 0,
                'rating' => 0,
            ];
        }

        // if (auth()->id() == 5869 || auth()->id() == 5311) {
        //     return [
        //         'cleanliness' => 4.5,
        //         'communication' => 4.5,
        //         'accuracy' => 4.5,
        //         'location' => 4.5,
        //         'rating' => 4.5,
        //     ];
        // }

        $sum = [
            'cleanliness' => 0,
            'communication' => 0,
            'accuracy' => 0,
            'location' => 0,
            'rating' => 0,
        ];

        foreach ($reviews as $review) {
            $sum['cleanliness'] += $review->cleanliness;
            $sum['communication'] += $review->communication;
            $sum['accuracy'] += $review->accuracy;
            $sum['location'] += $review->location;
            $sum['rating'] += $review->rating;
        }

        return [
            'cleanliness' => ($sum['cleanliness'] / $totalReviews) ,
            'communication' => ($sum['communication'] / $totalReviews) ,
            'accuracy' => ($sum['accuracy'] / $totalReviews) ,
            'location' => ($sum['location'] / $totalReviews) ,
            'rating' => ($sum['rating'] / $totalReviews) ,
        ];
    }

    // Function to scale review scores to 5-point scale
    private function scaleReviewScores($review)
    {
        return [
            'id' => $review->id,
            'rating' => $review->rating / 2,
            'message' => $review->message,
            'is_public' => $review->is_public,
            'created_at' => $review->created_at,
            'cleanliness' => $review->cleanliness / 2,
            'communication' => $review->communication / 2,
            'accuracy' => $review->accuracy / 2,
            'location' => $review->location / 2,
            'reviewer' => [
                'id' => $review->users_from->id,
                'name' => $review->users_from->first_name,
                'profile_image' => $review->users_from->profile_image ?? 'icons/user.svg',
            ],
        ];
    }

    public function notifications(Request $request)
    {
        $request->validate([
            'size' => 'required|integer|min:1,max:10000',
            'page' => 'required|integer:min:1'
        ]);
        if (!empty(auth()->user()->unreadNotifications())) {
            auth()->user()->unreadNotifications()->update(['read_at' => now()]);
        }
        $size = $request->size;
        $notifications = auth()->user()->notifications()->whereJsonContainsKey('data->data->slug')->take(10)->paginate($size);
        $notifications->setCollection($notifications->getCollection()->map(fn($noti) => (object) [
            'message' => $noti->data['messages'][auth()->user()?->lang ?? app()->getLocale()] ?? $noti->data['messages']['en'],
            'read_at' => $noti->read_at,
            'data' => $noti->data['data'],
            'created_at' => Carbon::parse($noti->created_at, (auth()->user()->timezone ?? config('app.timezone')))->diffForHumans()
        ]));
        return new NotificationCollection($notifications);
        // return response()->json(["message"=>"Success","data"=>auth()->user()->notifications]);
    }

    public function initialData(Request $request)
    {

        try {
            // Home Properties / Recommended Properties
            // $properties = Properties::where('status', 'listed')
            // ->where('visibility', '1')
            // ->with('users', 'property_price', 'property_address','property_description')
            // ->where('recomended', '1')
            // ->take(8)
            // ->inRandomOrder()
            // ->paginate();
            $properties = Properties::recommendedHome();
            $recommondedProperties = new RecommendedPropertiesCollection($properties);
            // Search Result Filters
            $propertiesForHome= Properties::recommendedForHome();
            $recommondedPropertiesForHome = new RecommendedPropertiesCollection($propertiesForHome['data']);
            $propertiesForHome['data'] = $recommondedPropertiesForHome;
            $propertiesForHome['pagination'] = [
                'total' => $propertiesForHome['total'],
                'per_page' => $propertiesForHome['per_page'],
                'current_page' => $propertiesForHome['current_page'],
                'last_page' => $propertiesForHome['last_page'],
            ];
            $data['lat'] = 0;
            $data['long'] = 0;
            $data['location'] = $request->input('location');
            $data['checkin'] = $request->input('checkin');
            $data['checkout'] = $request->input('checkout');
            $data['guest'] = $request->input('guest');
            $data['bedrooms'] = $request->input('bedrooms');
            $data['beds'] = $request->input('beds');
            $data['bathrooms'] = $request->input('bathrooms');
            $data['min_price'] = $request->input('min_price');
            $data['max_price'] = $request->input('max_price');

            $data['space_type'] = SpaceType::select('name', 'name_ar', 'id', 'icon_image', 'property_type', 'priority')->where('status', 'Active')->get();
            $data['property_type'] = PropertyType::select('name', 'name_ar', 'id', 'icon_image')->where('status', 'Active')->get();
            $data['common_amenities']        = Amenities::where('status', 'Active')->where('type_id', 1)->get();
            $data['safety_amenities'] = Amenities::where('status', 'Active')->where('type_id', 2)->get();
            $data['house_rules'] = Amenities::where('status', 'Active')->where('type_id', 3)->get();
            $data['amenities_type'] = AmenityType::select('name', 'id')->get();
            $data['special_days'] =  Country::orderBy('id')->first()->special_days;
            $countries = Country::all();

            // Find and remove the specific country you want to appear first
            $specificCountry = $countries->where('short_name', 'SA')->first();
            $countries = $countries->reject(function ($country) {
                return $country->short_name === 'SA';
            });

            // Add the specific country to the beginning of the collection
            $countries->prepend($specificCountry);

            if (strtolower(env('APP_ENV')) == 'local') { // If Environment is Local
                $pakistan = new Country([
                    'id' => 1,
                    'short_name'  => 'PK',
                    'name'        => 'Pakistan',
                    'name_ar'     => 'باكستان',
                    'iso3'        => 'PAK',
                    'number_code' => 'PK',
                    'limit'       => 10,
                    'phone_code'  => '92',
                    'special_days' => ["thursday", "friday", "saturday"],
                ]);

                $countries->push($pakistan);
            }

            $data['country_code'] = $countries;
            if (request()->route()->getPrefix() == "api/v3") {
                $data['cities'] = DB::table('cities')->where('deleted_at', null)->get()->map(function ($city) {
                    $city->image = "public3zukluszrx.jpg"; // adding because of mobile_application requirement
                    $city->media_id = 37481; // adding because of mobile_application requirement
                    return $city;
                });
            }

            $data['ticket_types'] =  TicketTypes::select('name', 'name_ar', 'id')->where('status', 'Active')->get();

            $data['property_type_selected'] = explode(',', $request->input('property_type') ?? '');
            $data['space_type_selected'] = explode(',', $request->input('space_type') ?? '');
            $data['amenities_selected'] = explode(',', $request->input('amenities') ?? '');
            $currency = Currency::getAll();
            if (Session::get('currency')) $data['currency_symbol'] = $currency->firstWhere('code', Session::get('currency'))->symbol;
            else $data['currency_symbol'] = $currency->firstWhere('default', 1)->symbol;
            $minPrice = Settings::getAll()->where('name', 'min_search_price')->first()->value;
            // $maxPrice = Settings::getAll()->where('name', 'max_search_price')->first()->value;
            $maxPrice = DB::table('property_price')->max('price');
            $data['default_min_price'] = $this->helper->convert_currency(Currency::getAll()->firstWhere('default')->code, '', $minPrice);
            $data['default_max_price'] = $this->helper->convert_currency(Currency::getAll()->firstWhere('default')->code, '', $maxPrice);
            if (!$data['min_price']) {
                $data['min_price'] = $data['default_min_price'];
                $data['max_price'] = $data['default_max_price'];
            }
            $data['date_format'] = Settings::getAll()->firstWhere('name', 'date_format_type')->value;
            $data['starting_cities'] = StartingCities::getAll();
            $data['recommended_amenities'] = Amenities::WhereIn('id',[2,4,43,14]) ->get();

            $searchData = new SearchFilterResource($data);

            $mobileBanner = Banners::where("status", "Active")->whereIn('banner_type', ['mobile main banner', 'mobile sub banner'])->get();
            $bannerData = new BannerResource($mobileBanner);
            //Settings Api
            if (request()->route()->getPrefix() == "api/v2" || request()->route()->getPrefix() == "api/v3") {
                $messages = LocalizationKeyword::select('parent_key', 'en_value', 'ar_value')->get()->toArray();
                $messages['listing_preference'] = Settings::select('value')->where('type', 'listing_preferences')->get();
                $settings = new SettingResource($messages);
            } else {
                $messages = Settings::getSettingAll();
                $settings = new SettingResource($messages);
            }
            // $settings = new SettingResource($messages);
            // return $settings;
            if (request()->route()->getPrefix() == "api/v3") {
                $districts = DB::table('districts')
                    ->select('name as en', 'name as ar')
                    ->where('deleted_at', null)->get();
            } else {
                $districts = PropertyAddress::whereRaw("latitude between 23.8859 and longitude between 45.0792")
                    ->distinct()
                    ->whereNotNull('district')
                    ->pluck('district');
            }

            $bankData = getBanksList();

            $combinedResource = new CombinedResource([$propertiesForHome, $searchData, $settings, $bannerData, $districts, true, $bankData]);


            // $messages = Settings::getSettingAll();
            return apiResponse($combinedResource, '', 200);
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function content_read($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, $url);
        $result = curl_exec($ch);
        curl_close($ch);

        return $result;
    }

    public function userData(Request $request)
    {

        try {
            // $request->validate([
            //     'size' => 'required|integer|min:1,max:10000',
            // ]);
            $size = isset($request->size) ? $request->size : 10;

            // $size = 12;
            //Notifications
            $notifications = auth()->user()->notifications()->whereJsonContainsKey('data->data->slug')->take(10)->paginate($size);
            $notifications->setCollection($notifications->getCollection()->map(fn($noti) => (object) [
                'message' => $noti->data['messages'][auth()->user()?->lang ?? app()->getLocale()] ?? $noti->data['messages']['en'],
                'read_at' => $noti->read_at,
                'data' => $noti->data['data'],
                'created_at' => Carbon::parse($noti->created_at, (auth()->user()->timezone ?? config('app.timezone')))->diffForHumans()
            ]));
            $notification = new NotificationCollection($notifications);

            //Favourites
            $favouriteData = Favourite::whereHas('properties', function ($q) {
                $q->where('status', 'Listed')->where('visibility', 1);
            })->where(['user_id' => Auth::id(), 'status' => 'Active'])->orderBy('id', 'desc')
                ->paginate($size);
            $favourite = new BookmarkCollection($favouriteData);

            //Inbox Api
            $messgesData = Messages::whereHas('properties', function ($query) {
                $query->whereNull('deleted_at');
            })
                ->with(['properties', 'sender', 'receiver', 'User:first_name,id,profile_image,date_of_birth'])
                ->where(function ($query) {
                    $query->where('sender_id', Auth::id())
                        ->orWhere('receiver_id', Auth::id());
                })
                ->orderBy('id', 'desc')
                ->groupBy('property_id')
                ->paginate($size);
            if (!empty($messgesData) || count($messgesData) > 0) {
                $inbox = new InboxCollection($messgesData);
            } else {

                $inbox = null;
            }


            //Guest Incoming Reservation
            $incomingData = Bookings::selectRaw('bookings.*, pchs.id AS chat_head_id')->with('users', 'properties.property_photos')
                ->leftJoin(
                    'property_chat_heads as pchs',
                    fn($q1) => $q1->on('pchs.property_id', 'bookings.property_id')
                        ->where('pchs.guest_id', auth()->guard('api')->id())
                )
                ->where('user_id', auth()->id())
                ->whereIn('status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
                ->where('start_date', '>', Carbon::now()->format('Y-m-d'))
                ->orderBy('id', 'desc')
                ->distinct()
                ->paginate($size);

            $incomingData->getCollection()->transform(function ($booking) {
                $booking['chat_head_id'] = $this->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
                return $booking;
            });
            // $incomingData = Bookings::with('users', 'properties')->where('user_id', auth()->id())
            // ->where(fn ($q) => $q->where([['status','Processing'],['status','Pending'],['status','Unpaid']])->where('start_date', '>=',Carbon::now()->format('Y-m-d')))->orderBy('id', 'desc')->paginate($size);
            $incoming = new ComingReservationCollection($incomingData);

            //Guest Cancelled Reservation
            $cancelledData = Bookings::with('users', 'properties')->where('user_id', auth()->id())
                ->where(fn($q) => $q->where('status', '=', 'Cancelled')->orWhere('status', '=', 'Declined'))->orderBy('updated_at', 'desc')->paginate($size);

            $cancelledData->getCollection()->transform(function ($booking) {
                $booking['chat_head_id'] = $this->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
                return $booking;
            });

            $cancelled = new CancelledReservationCollection($cancelledData);
            //Guest Expired Reservation
            $expiredData = Bookings::with('users', 'properties')->where('user_id', auth()->id())
                ->where('status', '=', 'Expired')->orderBy('updated_at', 'desc')->paginate($size);
            $expired = new CancelledReservationCollection($expiredData);

            //Guest History Reservation
            $historyData = Bookings::with('users', 'properties')->where('user_id', auth()->id())
                ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->orderBy('id', 'desc')->paginate($size);
            $historyData->getCollection()->transform(function ($booking) {
                $booking['chat_head_id'] = $this->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
                return $booking;
            });
            $history = new HistoryReservationCollection($historyData);

            //Guest Ongoing History
            $onGoingData = Bookings::with('users', 'properties')
                ->where('user_id', auth()->id())
                ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted')
                ->orderBy('updated_at', 'desc')
                ->paginate($size);
            $onGoingData->getCollection()->transform(function ($booking) {
                $booking['chat_head_id'] = $this->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
                return $booking;
            });
            $onGoing = new CancelledReservationCollection($onGoingData);
            $total_price = Bookings::with('users', 'properties')
                ->where('user_id', auth()->id())
                ->where(fn($q) => $q->where('status', '=', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))
                ->sum('total');
            $total_price = number_format($total_price, 2, '.', '');
            $total_price = (float) $total_price;
            $attribute = [
                'cancelled_reservations' => Bookings::with('users', 'properties')->where('user_id', auth()->id())
                    ->where(fn($q) => $q->where('status', '=', 'Cancelled')->orWhere('status', '=', 'Declined'))->count(),
                'current_reservations' => Bookings::with('users', 'properties')
                    ->where('user_id', auth()->id())
                    ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                    ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                    ->where('status', 'Accepted')
                    ->count(),
                'past_reservations' => Bookings::with('users', 'properties')->where('user_id', auth()->id())
                    ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->count(),
                'total_price' => $total_price
            ];
            $userCombinedResource = new UserCombinedResource([$notification, $favourite, $inbox, $incoming, $cancelled, $history, $onGoing, $expired, $attribute]);
            return apiResponse($userCombinedResource, '', 200);
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }



    public function hostAgreement(Request $request)
    {
        // Get the authenticated user
        if (Auth::guard('api')->check()) $user = Auth::guard('api')->user();
        if (Auth::guard('web')->check()) $user = Auth::guard('web')->user();

        // Check if a user is authenticated
        if (!$user) {
            return apiResponse(null, 'Unauthorized', 401);
        }

        // Try to update the terms_condition field
        try {
            $updateResult = $user->update(['terms_condition' => true]);

            if ($updateResult) {
                return apiResponse(null, 'Terms and conditions updated successfully.', 200);
            } else {
                return apiResponse(null, 'Failed to update terms and conditions.', 500);
            }
        } catch (\Exception $e) {
            // Catch and handle the exception
            return apiResponse(null, 'An error occurred: ' . $e->getMessage(), 500);
        }
    }


    public function hostData(Request $request)
    {
        try {
            $request->validate([
                'size' => 'required|integer|min:1,max:10000',
            ]);
            $size = $request->size;

            $special_days = Country::orderBy('id')->first()->special_days;
            //Complete Properties
            $properties = isHostOrCohostQuery(Properties::with(
                'property_price',
                'property_address',
                'property_photos',
                'property_description',
                'property_steps'
            ), auth()->id(), conId: 'id')->whereHas('property_steps', function ($query) {
                $query->where('total_steps', '=', 0);
            })->orderBy('properties.id', 'desc')
                ->paginate($size);
            $properties->getCollection()->transform(function ($property) use ($special_days) {
                $property['special_days_price'] = array_combine($special_days, array_map(fn($day) => property_exists($property->property_price->special_days_price, $day) ? $property->property_price->special_days_price->{$day} : $property->property_price->price, $special_days));
                return $property;
            });
            $completeproperties = new CompletePropertiesCollection($properties);

            //InComplete Properties
            $incompleteProperties = isHostOrCohostQuery(Properties::with(
                'property_price',
                'property_address',
                'property_photos',
                'property_description',
                'property_steps'
            ), auth()->id(), conId: 'id')
                ->whereHas('property_steps', function ($query) {
                    $query->where('total_steps', '!=', 0);
                })->orderBy('properties.id', 'desc')
                ->paginate($size);
            $incompleteProperties->getCollection()->transform(function ($property) use ($special_days) {
                $property['special_days_price'] = !$property->property_price ? (object) [] : array_combine($special_days, array_map(fn($day) => property_exists($property->property_price->special_days_price, $day) ? $property->property_price->special_days_price->{$day} : $property->property_price->price, $special_days));
                return $property;
            });
            $incompleteProperties = new IncompletePropertiesCollection($incompleteProperties);

            //my-booking
            $data = isHostOrCohostQuery(Bookings::query(), auth()->id(), selectData: 'bookings.*, pchs.id as chat_head_id', isRaw: true)
                ->leftJoin(
                    'property_chat_heads as pchs',
                    fn($q1) => $q1->on('pchs.property_id', 'bookings.property_id')
                        ->where('pchs.host_id', auth()->guard('api')->id())
                )
                ->whereIn('status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
                ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                ->orderBy('bookings.id', 'desc')
                ->distinct()
                ->paginate($size);
            $myBookings = new MyBookingVersionTwoCollection($data);

            // Get PromoCode
            $promoCodeData = PromoCode::with('codeProperties')->where(['user_id' => Auth::id(), 'status' => 'Active'])->orderBy('id', 'desc')
                ->withCount(['promoCodeUsage as usage_count' => function ($query) {
                    $query->where('is_used', 1);
                }])
                ->paginate($size);
            $promoCode = new PromoCodeCollection($promoCodeData);

            //Booking History
            $bookingHistoryData = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth()->id())
                ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))
                ->orderBy('bookings.id', 'desc')->paginate($size);

            $bookingHistory = new BookingHistoryCollection($bookingHistoryData);

            // Host Cancelled Reservation
            $cancelledBookingData = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth()->id())
                ->where(fn($q) => $q->where('status', '=', 'Cancelled')->orWhere('status', '=', 'Declined'))->orderBy('bookings.updated_at', 'desc')->paginate($size);
            $cancelledBooking = new BookingHistoryCollection($cancelledBookingData);
            // Host Expired Reservation
            $expiredBookingData = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth()->id())
                ->where('status', '=', 'Expired')->orderBy('updated_at', 'desc')->paginate($size);
            $expiredBooking = new BookingHistoryCollection($expiredBookingData);

            $hostOnGoingData = isHostOrCohostQuery(Bookings::with(['users' => fn($q) => $q->withTrashed(), 'properties' => fn($q) => $q->withTrashed()]), auth()->id())
                ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted')
                ->orderBy('bookings.updated_at', 'desc')
                ->paginate($size);
            $hostOnGoing = new BookingHistoryCollection($hostOnGoingData);

            $hostCombinedResource = new HostCombinedResource([$completeproperties, $incompleteProperties, $myBookings, $promoCode, $bookingHistory, $cancelledBooking, $hostOnGoing, $expiredBooking]);

            if (request()->route()->getPrefix() == "api/v2") {
                // Host All Properties
                $data = isHostOrCohostQuery(PropertyView::with('property_photos'), auth()->id(), conId: 'id')->get();
                $hostProperties = PropertiesResource::collection($data);

                // Host All Listing GET and POST
                $properties = isHostOrCohostQuery(Properties::with('property_steps', 'property_address'), auth()->id(), conId: 'id')
                    ->where('slug', '!=', null)->where('properties.status', '=', 'Listed')->distinct('properties.id')->paginate($size);
                $hostCalendarListing = new HostPropertiesCollection($properties);

                // Host Checking Out Bookings
                $bookings = isHostOrCohostQuery(Bookings::with('users', 'properties'), auth()->id())
                    ->where('status', 'Accepted')->where('end_date', '=', now()->format('Y-m-d'))
                    ->orderBy('bookings.id', 'desc')->paginate($size);

                $bookings->getCollection()->transform(function ($booking) {
                    // Check if a chat_head exists for the given host, guest, and property
                    $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
                    return $booking;
                });

                $hostCheckingout = new HostReservationCollection($bookings);

                // Host All properties status = Listed and visibility 1
                $properties = isHostOrCohostQuery(Properties::with('property_steps', 'property_address'), auth()->id(), conId: 'id')
                    ->where('slug', '!=', null)->where('properties.status', '=', 'Listed')->where('properties.visibility', '=', 1)->distinct('properties.id')->paginate($size);
                $dwellings = new HostPropertiesCollection($properties);

                $hostCombinedResource = new HostCombinedResource([$completeproperties, $incompleteProperties, $myBookings, $promoCode, $bookingHistory, $cancelledBooking, $hostOnGoing, $expiredBooking, $hostProperties, $hostCalendarListing, $hostCheckingout, $dwellings]);
            }



            return apiResponse($hostCombinedResource, '', 200);
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }
    public function receipt(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code'    => 'required',

        ]);
        if ($validator->fails()) {
            return apiResponse($validator->errors(), "Failure", 422);
            // return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
        }
        $data['booking'] = Bookings::with(['properties', 'users'])->leftJoin('promo_code_usage', 'bookings.id', '=', 'promo_code_usage.booking_id')
            ->leftJoin('promo_codes', 'promo_codes.id', '=', 'promo_code_usage.promo_code_id')
            ->select('bookings.*', 'promo_codes.code', 'promo_code_usage.discount_type', 'promo_code_usage.discount_value', 'promo_code_usage.after_discount', 'promo_code_usage.is_used')
            ->where('bookings.code', $request->code)->first();

        if ($data['booking']) {
            $data['date_price']       = json_decode($data['booking']->date_with_price);
            $data['title']            = 'Payment receipt for';
            $data['url']              = url('/') . '/';
            $data['additional_title'] = $request->code;
            return new ReceiptResource($data);
            // return apiResponse($data,"Success",200);
        } else {
            $data['message'] = "Booking Does Not Exist";
            return apiResponse($data, "Failure", 422);
        }
    }

    public function listAccountManager(Request $request)
    {
        try {
            $request->validate([
                'size' => 'required|integer|min:1,max:10000',
                'page' => 'required|integer:min:1'
            ]);
            $size = $request->size;
            $data = User::where('parent_id', Auth::id())->orderBy('id', 'desc')->paginate($size);
            return new AccountManagerCollection($data);
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function createAccountManager(Request $request,  UserController $user_controller)
    {
        try {
            $validation = Validator::make($request->all(), [
                'first_name' => 'required|max:255',
                'last_name' => 'required|max:255',
                'email' => 'required|email|max:255|unique:users',
                'phone' => 'required|regex:/\d+/|min:9|max:13',
                'password' => 'required|min:6',
                'status' => 'required',
                // 'password_confirmation' => 'required'
            ]);

            if ($validation->fails()) {
                return apiResponse($validation->errors(), "Failure", 422);
            }

            $insertUser = new User;
            $insertUser->parent_id = Auth::user()->id;
            $insertUser->first_name = $request->first_name;
            $insertUser->last_name = $request->last_name;
            $insertUser->email = $request->email;
            $insertUser->phone = $request->phone;
            $insertUser->formatted_phone = $request->phone;
            $insertUser->password = bcrypt($request->password);
            $insertUser->status = $request->status;
            $insertUser->role = "Account Manager";
            $insertUser->save();

            $user = User::where('email', $request->email)->first();
            if ($user) {
                $user_controller->wallet($user->id);
            }
            return apiResponse($user, '', 200);
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function editAccountManager(Request $request)
    {
        try {
            $validation = Validator::make($request->all(), [
                'first_name' => 'required|max:255',
                'last_name' => 'required|max:255',
                'phone' => 'required|regex:/\d+/|min:9|max:13',
                'status' => 'required',
                // 'password_confirmation' => 'required'
            ]);

            if ($validation->fails()) {
                return apiResponse($validation->errors(), "Failure", 422);
            }
            $insertUser = User::find($request->id);
            if ($insertUser) {
                $insertUser->first_name = $request->first_name;
                $insertUser->last_name = $request->last_name;
                $insertUser->phone = $request->phone;
                $insertUser->formatted_phone = $request->phone;
                $insertUser->status = $request->status;
                $insertUser->save();
                return apiResponse($insertUser, '', 200);
            }
            return apiResponse("Account Does Not Exists!", "Failure", 422);
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }
    public function deleteAccountManager(Request $request)
    {
        try {
            $accountManager = User::where('id', $request->id)->where('role', 'Account Manager')->first();
            if (!$accountManager) {
                return apiResponse("Account Does Not Exists!", "Failure", 422);
            }
            $accountManager->delete();
            return apiResponse("", 'Success', 200);
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function createOrUpdateChatHead($guestId, $hostId, $propertyId)
    {
        // Check if a chat_head exists for the given host, guest, and property
        return $this->helper->createOrUpdateChatHead($guestId, $hostId, $propertyId);
    }

    public function cityBasedDistricts()
    {
        try {
            $cityBasedDistricts = PropertyAddress::whereRaw("latitude between 23.8859 and longitude between 45.0792")
                ->distinct()
                ->whereNotNull('district')
                ->get()
                ->groupBy('city')
                ->map(function ($groupedData) {
                    return $groupedData->whereNotNull('district')->pluck('district')->unique()->values();
                });

            $cities = [];
            foreach ($cityBasedDistricts as $key => $city) {
                $payload = [
                    'city' => $key,
                    'districts' => $city,
                ];
                array_push($cities, $payload);
            }

            return apiResponse($cities, "City and Districts Fetched Successfully", 200);
        } catch (\Exception $e) {
            return apiResponse(null, "An error occurred: " . $e->getMessage(), 500);
        }
    }

    public function cityBasedDistrictsV3()
    {
        try {
            $lang = request('lang', 'en'); // Default to 'en' if not present
            $cities = City::with('districts')->get();

            if ($lang == 'ar') {
                $citiesWithDistricts = $cities->map(function ($city) {
                    $districtNames = $city->districts->pluck('name_ar')->toArray();
                    return [
                        'city' => $city->name_ar,
                        'districts' => $districtNames,
                    ];
                });
            } else {

                $citiesWithDistricts = $cities->map(function ($city) {
                    $districtNames = $city->districts->pluck('name')->toArray();
                    return [
                        'city' => $city->name,
                        'districts' => $districtNames,
                    ];
                });
            }
            return apiResponse($citiesWithDistricts, "City and Districts Fetched Successfully", 200);
        } catch (\Exception $e) {
            return apiResponse(null, "An error occurred: " . $e->getMessage(), 500);
        }
    }

    public function cityBasedDistrictsV4()
    {
        try {
            // $cities = City::with(['districts' => function ($query) {
            //     $query->select('id', 'city_id', 'name', 'name_ar');
            // }])->select('id', 'name', 'name_ar', 'country_id', 'latitude', 'longitude')
            //     ->get();

            $cities = City::with(['districts:id,city_id,name,name_ar'])
            ->select('id', 'name', 'name_ar', 'country_id', 'latitude', 'longitude')
            ->get();

            return apiResponse($cities, "City and Districts Fetched Successfully", 200);
        } catch (\Exception $e) {
            return apiResponse(null, "An error occurred: " . $e->getMessage(), 500);
        }
    }

    public function getCities()
    {
        try {
            $cities = DB::table('cities')->where('deleted_at', null)->get();
            return apiResponse($cities, "City Fetched Successfully", 200);
        } catch (\Exception $e) {
            return apiResponse(null, "An error occurred: " . $e->getMessage(), 500);
        }
    }

    /**
     * Handle the license upload.
     */
    public function uploadLicense(Request $request)
    {
        try {
            $rules = [
                'property_id' => 'required|exists:properties,id',
                'license_number' => 'nullable|numeric|min_digits:8|unique:properties,license_no,' . $request->property_id . ',id',
                'cr_number' => 'nullable|numeric|max_digits:10|unique:properties,cr_no,' . $request->property_id . ',id',
                'is_company' => 'nullable|in:0,1',
            ];

            $messages = [
                'property_id.exists' => 'Property not found',

                'license_number.required'   => 'License number is required',
                'license_number.numeric'    => 'License number must be numeric',
                'license_number.min_digits' => 'License number must be minimum 8 digits',
                'license_number.unique'     => 'License number already exists',

                'cr_number.required'   => 'CR Number is required',
                'cr_number.numeric'    => 'CR Number must be numeric',
                'cr_number.max_digits' => 'CR Number must be maximum 10 digits',
                'cr_number.unique'     => 'CR Number already exists',
            ];

            if (isset($request->is_company) && $request->is_company == 0) {
                $rules = [
                    'property_id' => 'required|exists:properties,id',
                    'license_number' => 'nullable|numeric|min_digits:8|unique:properties,license_no,' . $request->property_id . ',id',
                    'cr_number' => 'nullable|numeric|max_digits:20|unique:properties,cr_no,' . $request->property_id . ',id',
                    'is_company' => 'nullable|in:0,1',
                ];

                $messages = [
                    'property_id.exists' => 'Property not found',

                    'license_number.required'   => 'Permit Number is required',
                    'license_number.numeric'    => 'Permit Number must be numeric',
                    'license_number.min_digits' => 'Permit Number must be minimum 8 digits',
                    'license_number.unique'     => 'Permit Number already exists',

                    'cr_number.required'   => 'ID Number is required',
                    'cr_number.numeric'    => 'ID Number must be numeric',
                    'cr_number.max_digits' => 'ID Number must be maximum 20 digits',
                    'cr_number.unique'     => 'ID Number already exists',
                ];
            }

            $validator = FacadesValidator::make(
                $request->all(),
                $rules,
                $messages
            );

            if ($validator->fails()) return response()->json([
                'status' => false,
                'error' => $validator->errors()
            ], 403);

            if (isset($request->license_number) && $request->license_number != null && isset($request->cr_number) && $request->cr_number != null) {
                $mtTourismService = new MTTourismService();

                $user = FacadesAuth::guard('api')->user();

                if (isset($request->is_company) && $request->is_company == 0) $verify = $mtTourismService->verifyLicense($request->license_number, $request->cr_number, $request->is_company ?? 1, $request->property_id, $user->id);
                else $verify = $mtTourismService->verifyCompanyLicense($request->license_number, $request->cr_number, $request->is_company ?? 1, $request->property_id, $user->id);

                if (!$verify['status']) throw new Exception($verify['error']);

                $property = Properties::find($request->property_id)->update([
                    'license_no'          => $request->license_number,
                    'cr_no'               => $request->cr_number,
                    'license_is_company' => $request->is_company ?? 1,
                    'license_verified_at' => now(),
                    'license_expiry'      => $verify['expiry_date'],
                    'license_company_name' => json_encode($verify['company']),
                ]);
            }

            return apiResponse(['status' => "true"], 'Verification has been done successfully');
        } catch (Exception $e) {
            Log::info($e->getMessage());

            return response()->json([
                'status' => "false",
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ], 403);
        }
    }
}
