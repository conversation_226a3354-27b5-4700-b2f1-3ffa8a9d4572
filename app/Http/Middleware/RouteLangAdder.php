<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class RouteLangAdder
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {

        // Skip middleware for API routes
        if ($request->is('api/*')) {
            return $next($request);
        }
        // if (!$request->isMethod('get') || !preg_match('/^((\/(?:en|ar))?(?:(\/|\/properties(\/\w+(-\w+)*)$|\/landing-page(?:\/\w+)?|\/guest\/reservation(?:\/\w+)?|\/wishlists(?:\/\w+)?|\/ticket\/(list|create)|\/term_condition|\/about|\/privacy-policy|\/faqs|\/wallet|\/search)(\?\w+=\w+(?:&\w+=\w+)*$)?$)?)$/', $request->getRequestUri())) {
        //     return $next($request);
        // }
        // preg_match_all('/\b(?:ar|en)\b/i', $request->getRequestUri(), $matches);
        // if (session_status() !== PHP_SESSION_ACTIVE) {
        //     session_start();
        // }
        // $lang = !count($matches[0]) ? ($_SESSION['language'] ?? app()->getLocale()) : $matches[0][0];
        // if ($lang != app()->getLocale() || !(isset($_SESSION['language']) && $lang == $_SESSION['language'])) {
        //     app()->setLocale($lang);
        //     $_SESSION['language'] = $lang;
        // };
        // if (!preg_match("/^\/($lang)\/?/", $request->getRequestUri())) {
        //     return redirect($lang . preg_replace('/\/(en|ar)\//', '/', $request->getRequestUri(), 1));
        // }
        // return $next($request);

        // if (!$request->isMethod('get') || !preg_match('/^((\/(?:en|ar))?(?:(\/|\/properties\/(\w|-)+|\/landing-page(?:\/\w+)?|\/lp\/[\w-]+|\/guest\/reservation(?:\/\w+)?|\/wishlists(?:\/\w+)?|\/ticket\/(list|create)|\/term_condition||\/blog|\/property\/hostAgreement|\/insurance||\/announcement|\/ilmyaqeen|\/insurance_policy|\/about|\/privacy-policy|\/faqs|\/wallet|\/search)((\?\w+=.+(?:&\w+=.+)*$)|\?login)?$)?)$/', $request->getRequestUri())) {
        if (!$request->isMethod('get') || !preg_match(
            '/^((\/(?:en|ar))?(?:(\/|\/summary\/[\w-]+|\/property\/darStays|\/properties\/[\w-]+|\/landing-page(?:\/\w+)?|\/lp\/[\w-]+|\/guest\/reservation(?:\/\w+)?|\/wishlists(?:\/\w+)?|\/ticket\/(list|create)|\/term_condition|\/blog|\/property\/hostAgreement|\/insurance|\/announcement|\/ilmyaqeen|\/insurance_policy|\/about|\/privacy-policy|\/faqs|\/wallet|\/search)((\?\w+=.+(?:&\w+=.+)*)|\?login)?$)?)$/',
            $request->getRequestUri()
        )) {
            return $next($request);
        }

        preg_match_all('/\b(?:ar|en)\b/i', $request->getRequestUri(), $matches);
        if (session_status() !== PHP_SESSION_ACTIVE) {
            session_start();
            //            return $next($request);

        }
        $lang = !count($matches[0]) ? ($_SESSION['language'] ?? app()->getLocale()) : $matches[0][0];
        if ($lang != app()->getLocale() || !(isset($_SESSION['language']) && $lang == $_SESSION['language'])) {
            app()->setLocale($lang);
            $_SESSION['language'] = $lang;
        };
        if (!preg_match("/^\/($lang)\/?/", $request->getRequestUri())) {
            return redirect($lang . preg_replace('/\/(en|ar)\//', '/', $request->getRequestUri(), 1));
        }
        return $next($request);
    }
}
