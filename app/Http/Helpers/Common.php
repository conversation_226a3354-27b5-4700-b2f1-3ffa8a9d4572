<?php

namespace App\Http\Helpers;

use App\Enums\PaymentTypeEnum;
use App\Enums\PropertyChatTypeEnum;
use App\Http\Services\WalletService;
use App\Http\Services\PlatformReservationService;
use App\Models\{
    BookingInsurance,
    BookingPaymentDetails,
    Bookings,
    BookingStatusLog,
    Country,
    Currency,
    CustomPricing,
    ElmDocument,
    GridProperty,
    HostPerformanceScore,
    Log,
    Meta,
    NewUserWallet,
    Notification,
    penalty,
    PermissionRole,
    Permissions,
    Platform,
    PlatformApiLog,
    PromoCode,
    PromoCodeUsage,
    Properties,
    PropertyAddress,
    PropertyChatHead,
    PropertyDates,
    PropertyDescription,
    PropertyFees,
    PropertyPhotos,
    PropertyPrice,
    PropertySteps,
    Refund,
    Reviews,
    RoleAdmin,
    Tawuniya,
    TransactionCategories,
    Transactions,
    TransactionTypes,
    User,
    UserPropertyView,
    WalletCashback,
    PaymentResponse,
    Payouts,
    PayoutPenalties,
    BookingDetails
};
use App\Notifications\UserNotify;
use App\Providers\FCMService;
use Auth;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateTime;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log as FacadeLog;
use Illuminate\Support\Facades\Log as FacadesLog;
use Illuminate\Support\Facades\Notification as FacadesNotification;
use Illuminate\Support\Facades\Session as FacadesSession;
use Session;
use Throwable;
use Transliterator;
use View;

class Common
{

    function __construct()
    {
        setlocale(LC_ALL, 'en_US.UTF8');
    }


    public static function previousGuest($hostid, $guestid)
    {

        //Check this guest previous customer or not of this host
        $prevBookings = Bookings::where('host_id', $hostid)->where('status', 'Accepted')->pluck('user_id')->toArray();
        $guestExists = in_array($guestid, $prevBookings);
        return $guestExists;
    }

    public static function getChatHeadId($propId, $guestid)
    {

        $chatHead = PropertyChatHead::where('property_id', $propId)->where('guest_id', $guestid)->first();
        return $chatHead?->id;
    }

    public static function checkReviewFromHost($bookId)
    {

        $review = Reviews::where('booking_id', $bookId)->where('reviewer', 'host')->first();
        return $review?->message;
    }

    public function applyCoupon($code, $propertyId, $amount, $loginUser, $request)
    {
        $promoCode = PromoCode::where(['code' => $code, 'status' => 'Active'])->first();
        $MabaatProperty = Properties::where(['id' => $propertyId])->first();
        $GridProperty = GridProperty::where('id', $propertyId)->first();
        //CHECK CODE AVAILABLE OR NOT
        if ($promoCode) {
            // if($promoCode->is_campaign){
            //     $propertyFees = PropertyFees::pluck('value', 'field');
            //     $servicefeeapplied = round(($propertyFees['guest_service_charge'] / 100) * $amount, 2);

            //     return response()->json([
            //         "status" => "campaign",
            //         "message" => "The Discounted Amount will be Credited to your User Walllet",
            //         'amount' => $amount ,
            //         'new_servicefee' => (float)$servicefeeapplied,
            //         'amount_after_discount' => (float)$amount + (float)$servicefeeapplied
            //     ]);
            // }

            $discountedAmount = 0;
            $IsforNewCustomer = $promoCode->for_new_customer;

            $totalUsageOfCode = 0;
            $maxUsageLimit = $promoCode->max_usage;

            $totalUsageOfSameUser = 0;
            $maxUsageLimitSameUser = $promoCode->per_user_usage;

            $promoStart = Carbon::parse($promoCode->start_date);
            $promoEnd = Carbon::parse($promoCode->expiry_date)->addDay();
            $isStart = Carbon::now()->format('Y-m-d H:i:s') >= $promoStart->format('Y-m-d H:i:s');
            $isExpired = Carbon::now()->format('Y-m-d H:i:s') > $promoEnd->format('Y-m-d H:i:s');

            $promoProperties = $promoCode->codeProperties;
            $totalUsageOfCode = PromoCodeUsage::where(['promo_code_id' => $promoCode->id, 'is_used' => 1])->count();
            $totalUsageOfSameUser = PromoCodeUsage::where(['promo_code_id' => $promoCode->id, 'user_id' => $loginUser, 'is_used' => 1])->count();
            $appliedProperties = [];

            $discounttype = 'will decide';
            $discount_value = 'will decide';

            //CHECK START & EXPIRY DATE
            if ($isStart) {
                if ($isExpired) {
                    return response()->json([
                        "status" => "error",
                        "message" => "This Promo code was Expired at " . $promoEnd->format('d-m-Y'),
                        'amount' => $amount,
                        'amount_after_discount' => $amount,
                    ]);
                }
            } else {
                return response()->json([
                    "status" => "error",
                    "message" => "This Promo code will apply from " . $promoStart->format('d-m-Y'),
                    'amount' => $amount,
                    'amount_after_discount' => $amount,
                ]);
            }

            $checkin = Carbon::parse($request->checkin);
            $checkout = Carbon::parse($request->checkout);
            $diffInDays = $checkin->diffInDays($checkout);
            if ($diffInDays == 0) {
                $diffInDays += 1;
            }
            // Condition for Promo Codes with minimum nights
            if ($promoCode->min_nights != null) {
                if ($diffInDays < $promoCode->min_nights) {
                    return response()->json([
                        "status" => "error",
                        "message" => "This Promo code will apply in booking with minimum " . $promoCode->min_nights . ' nights',
                        'amount' => $amount,
                        'amount_after_discount' => $amount,
                    ]);
                }
            }
            //CHECK FOR NEW USER
            if ($IsforNewCustomer) {
                $userexist = Bookings::where(['user_id' => $loginUser, 'status' => 'Accepted'])->count();
                if ($userexist) {
                    return response()->json([
                        "status" => "error",
                        "message" => "This Promo Code is avialable for new users only",
                        'amount' => $amount,
                        'amount_after_discount' => $amount,
                    ]);
                }
            }

            //CHECK AVAILABLE FOR THIS PROPERTY OR NOT

            //UNIVERSAL CHECK
            // promo_type_id is 2 Univarsal apply
            if ($promoCode->promo_type_id == 1) {

                foreach ($promoProperties as $row) {
                    array_push($appliedProperties, $row->property_id);
                }

                if (!in_array($propertyId, $appliedProperties)) {

                    $cities = json_decode($promoCode->cities, true);
                    $districts = json_decode($promoCode->districts, true);
                    if (is_null($cities) || !in_array((string)$GridProperty->city_id, $cities)) {
                        // if(is_null($districts) || !in_array((string) $MabaatProperty->property_address->district_id, $districts)){

                        return response()->json([
                            "status" => "error",
                            "message" => "This Promo code not available for this property",
                            'amount' => $amount,
                            'amount_after_discount' => $amount,
                        ]);
                        //  }
                    }
                }
            }

            if (Auth::guard('admin')->check()) {
                if (Auth::guard('admin')->user()->id == $loginUser) {
                    $totalUsageOfSameUser = 0;
                    $totalUsageOfCode = 0;
                }
            }


            //CHECK MAXIMUM USAGE OF CODE
            // foreach($promoUsage as $row){
            //     $totalUsageOfCode += $row->usage_count;
            // }


            if ($totalUsageOfCode >= $maxUsageLimit) {
                return response()->json([
                    "status" => "error",
                    "message" => "This promo code reached Maximum limit of usage",
                    'amount' => $amount,
                    'amount_after_discount' => $amount,
                ]);
            }


            // CHECK USAGE OF THIS USER
            // foreach($promoUsageForUser as $row){
            //     $totalUsageOfSameUser += $row->usage_count;
            // }

            if (!Auth::guard('admin')->check()) {
                if ($totalUsageOfSameUser >= $maxUsageLimitSameUser) {
                    return response()->json([
                        "status" => "error",
                        "message" => "You have reached the usage limit for using this promo code",
                        'amount' => $amount,
                        'amount_after_discount' => $amount,
                    ]);
                }
            }


            $percentApplied = $amount * $promoCode->percentage / 100;
            $upto = $promoCode->discount_upto;


            FacadeLog::info('promoCode->percentage', [
                'promoCode->percentage' => $promoCode->percentage,
                'percentApplied' => gettype($percentApplied),
            ]);


            if ($promoCode->percentage > 0) {
                // Percentage-based discount
                if ($percentApplied >= $upto) {
                    // APPLY UPTO DISCOUNT
                    $discountedAmount = $amount - $upto;
                    $discounttype = 'Upto';
                    $discount_value = $upto;


                    FacadeLog::info('Discount Upto', [
                        'promo_code' => $promoCode->code,
                        'discount_type' => $discounttype,
                        'original_amount' => $amount,
                        'percent_applied' => $percentApplied,
                        'discount_upto' => $upto,
                        'discount_value' => $discount_value,
                        'discounted_amount' => $discountedAmount,
                    ]);
                } else {
                    // APPLY PERCENT DISCOUNT
                    $discountedAmount = $amount - $percentApplied;
                    $discounttype = 'Percent';
                    $discount_value = $percentApplied;

                    FacadeLog::info('Discount Percent', [
                        'promo_code' => $promoCode->code,
                        'discount_type' => $discounttype,
                        'original_amount' => $amount,
                        'percent_applied' => $percentApplied,
                        'discount_upto' => $upto,
                        'discount_value' => $discount_value,
                        'discounted_amount' => $discountedAmount,
                    ]);
                }
            } else {
                // Flat discount
                $discount_value = min($promoCode->discount_upto, $amount);
                $discountedAmount = $amount - $discount_value;
                $discounttype = 'Flat';

                FacadeLog::info('Discount Flat', [
                    'promo_code' => $promoCode->code,
                    'discount_type' => $discounttype,
                    'original_amount' => $amount,
                    'percent_applied' => $percentApplied,
                    'discount_upto' => $upto,
                    'discount_value' => $discount_value,
                    'discounted_amount' => $discountedAmount,
                ]);
            }
            // COUPON APPLIED

            //SERVICE FEE ON BASE PRICE AFTER COUPON APPLIED
            $property_price = PropertyPrice::where('property_id', $propertyId)->first();
            $propertyFees = PropertyFees::pluck('value', 'field');
            $servicefeeapplied = round(($propertyFees['guest_service_charge'] / 100) * $amount, 2);

            $cleaning_fee = $property_price->cleaning_fee ?? 0;
            $security_fee = $property_price->security_fee ?? 0;
            $mabaat_service_charge = 0;
            $clean_service_fee = 0;
            if ($MabaatProperty->platform_id == 4) {
                $prices = [1 => 199, 2 => 299, 3 => 399, 4 => 499, 5 => 599];
                $bedrooms = $MabaatProperty->bedrooms;
                $cleaning_fee = $property_price->cleaning_fee;
                // $mabaat_service_charge = $prices[$bedrooms];
            } elseif ($MabaatProperty->platform_id == 6 && $MabaatProperty->property_price->cleaning_fee_type == 'daily') {
                $cleaning_fee *= $diffInDays; // Multiply the cleaning fee by the difference in days
            }
            $clean_service_fee = round(($propertyFees['guest_service_charge'] / 100) * $cleaning_fee, 2);

            // $servicefeeapplied = round(($propertyFees['guest_service_charge'] / 100) * $discountedAmount, 2);
            $amount_after_discount_and_service_fee_applied = $discountedAmount + $servicefeeapplied;


            $finalAmount = !$promoCode->is_campaign
                ? (float)round($amount_after_discount_and_service_fee_applied, 2) + $mabaat_service_charge
                : (float)$amount + (float)$servicefeeapplied + $mabaat_service_charge;

            $newServiceFee = !$promoCode->is_campaign
                ? (float)round(($propertyFees['guest_service_charge'] / 100) * ($amount + $cleaning_fee + $property_price->security_fee), 2) + $mabaat_service_charge
                : (float)$servicefeeapplied + $mabaat_service_charge + $clean_service_fee;

            return response()->json([
                'status' => $promoCode->is_campaign ? 'campaign' : 'success',
                'amount' => $amount,
                'amount_after_discount' => $finalAmount,
                'servicefee' => (float)$servicefeeapplied + $mabaat_service_charge,
                'new_servicefee' => $newServiceFee,
                'discount_type' => $discounttype,
                'discount_value' => $discount_value,
            ]);
        } else {
            return response()->json([
                "status" => "error",
                "message" => "Invalid Promo Code",
                'amount' => $amount,
                'amount_after_discount' => $amount,
            ]);
        }
    }

    public function applyCouponForCashback($code, $propertyId, $amount, $loginUser)
    {
        // Check if the promo code contains Arabic characters
        if (preg_match('/\p{Arabic}/u', $code)) {
            // If Arabic, rearrange the code to have text before number
            $code = preg_replace('/(\d+)(\p{Arabic}+)/u', '$2$1', $code);
        }
        $promoCode = PromoCode::where(['code' => $code, 'status' => 'Active'])->first();


        //CHECK CODE AVAILABLE OR NOT
        if ($promoCode) {
            $discountedAmount = 0;
            $IsforNewCustomer = $promoCode->for_new_customer;

            $totalUsageOfCode = 0;
            $maxUsageLimit = $promoCode->max_usage;

            $totalUsageOfSameUser = 0;
            $maxUsageLimitSameUser = $promoCode->per_user_usage;

            $promoStart = Carbon::parse($promoCode->start_date);
            $promoEnd = Carbon::parse($promoCode->expiry_date)->addDay();
            $isStart = Carbon::now()->format('Y-m-d H:i:s') >= $promoStart->format('Y-m-d H:i:s');
            $isExpired = Carbon::now()->format('Y-m-d H:i:s') > $promoEnd->format('Y-m-d H:i:s');

            $promoProperties = $promoCode->codeProperties;
            $totalUsageOfCode = PromoCodeUsage::where(['promo_code_id' => $promoCode->id, 'is_used' => 1])->count();
            $totalUsageOfSameUser = PromoCodeUsage::where(['promo_code_id' => $promoCode->id, 'user_id' => $loginUser, 'is_used' => 1])->count();
            $appliedProperties = [];

            $discounttype = 'will decide';
            $discount_value = 'will decide';

            //CHECK START & EXPIRY DATE
            if ($isStart) {
                if ($isExpired) {
                    return response()->json([
                        "status" => "error",
                        "message" => "This Promo code was Expired at " . $promoEnd->format('d-m-Y'),
                        'amount' => $amount,
                        'amount_after_discount' => $amount,
                    ]);
                }
            } else {
                return response()->json([
                    "status" => "error",
                    "message" => "This Promo code will apply from " . $promoStart->format('d-m-Y'),
                    'amount' => $amount,
                    'amount_after_discount' => $amount,
                ]);
            }

            //CHECK FOR NEW USER
            if ($IsforNewCustomer) {
                $userexist = Bookings::where(['user_id' => $loginUser, 'status' => 'Accepted'])->count();
                if ($userexist) {
                    return response()->json([
                        "status" => "error",
                        "message" => "This Promo Code is avialable for new users only",
                        'amount' => $amount,
                        'amount_after_discount' => $amount,
                    ]);
                }
            }

            //CHECK AVAILABLE FOR THIS PROPERTY OR NOT

            //UNIVERSAL CHECK
            // promo_type_id is 2 Univarsal apply
            if ($promoCode->promo_type_id == 1) {

                foreach ($promoProperties as $row) {
                    array_push($appliedProperties, $row->property_id);
                }

                if (!in_array($propertyId, $appliedProperties)) {

                    return response()->json([
                        "status" => "error",
                        "message" => "This Promo code not available for this property",
                        'amount' => $amount,
                        'amount_after_discount' => $amount,
                    ]);
                }
            }


            //CHECK MAXIMUM USAGE OF CODE
            // foreach($promoUsage as $row){
            //     $totalUsageOfCode += $row->usage_count;
            // }
            if (Auth::guard('admin')->check()) {
                if (Auth::guard('admin')->user()->id == $loginUser) {
                    $totalUsageOfSameUser = 0;
                    $totalUsageOfCode = 0;
                }
            }

            if ($totalUsageOfCode >= $maxUsageLimit) {
                return response()->json([
                    "status" => "error",
                    "message" => "This promo code reached Maximum limit of usage",
                    'amount' => $amount,
                    'amount_after_discount' => $amount,
                ]);
            }


            // CHECK USAGE OF THIS USER
            // foreach($promoUsageForUser as $row){
            //     $totalUsageOfSameUser += $row->usage_count;
            // }

            if ($totalUsageOfSameUser >= $maxUsageLimitSameUser) {
                return response()->json([
                    "status" => "error",
                    "message" => "You have reached the usage limit for using this promo code",
                    'amount' => $amount,
                    'amount_after_discount' => $amount,
                ]);
            }

            $percentApplied = $amount * $promoCode->percentage / 100;
            $upto = $promoCode->discount_upto;
            if ($percentApplied >= $upto) {
                // APPLY UPTO DISCOUNT
                $discountedAmount = $amount - $upto;
                $discounttype = 'Upto';
                $discount_value = $upto;
            } else {
                // APPLY PERCENT DISCOUNT
                $discountedAmount = $amount - $percentApplied;
                $discounttype = 'Percent';
                $discount_value = $percentApplied;
            }

            // COUPON APPLIED

            //SERVICE FEE ON BASE PRICE AFTER COUPON APPLIED
            $property_price = PropertyPrice::where('property_id', $propertyId)->first();
            $propertyFees = PropertyFees::pluck('value', 'field');
            $servicefeeapplied = round(($propertyFees['guest_service_charge'] / 100) * $amount, 2);
            // $servicefeeapplied = round(($propertyFees['guest_service_charge'] / 100) * $discountedAmount, 2);
            $amount_after_discount_and_service_fee_applied = $discountedAmount + $servicefeeapplied;

            return response()->json([
                'status' => 'success',
                'amount' => $amount,
                'amount_after_discount' => (float)round($amount_after_discount_and_service_fee_applied, 2),
                'servicefee' => (float)$servicefeeapplied,
                'new_servicefee' => (float)round(($propertyFees['guest_service_charge'] / 100) * ($amount + $property_price->cleaning_fee + $property_price->security_fee), 2),
                // 'new_servicefee' => (float)round(($propertyFees['guest_service_charge'] / 100) * ($discountedAmount + $property_price->cleaning_fee + $property_price->security_fee), 2),
                'discount_type' => $discounttype,
                'discount_value' => $discount_value,
            ]);
        } else {
            return response()->json([
                "status" => "error",
                "message" => "Invalid Promo Code",
                'amount' => $amount,
                'amount_after_discount' => $amount,
            ]);
        }
    }

    // public static function payableToHost($booking)
    // {
    //     $guestpaid = $booking->total_with_discount != 0 ?  $booking->total_with_discount : $booking->total;
    //     $basePrice = 0;
    //     $prices = json_decode($booking->date_with_price) ?? [];


    //     $basePrice = $booking->base_price_with_discount ? $booking->base_price_with_discount :  $booking->base_price;

    //     $cleaningFee = $booking->cleaning_charge;
    //     $totalamount = $basePrice + $cleaningFee;

    //     $securityMoney = $booking->security_money;
    //     $totalBookingPrice = $basePrice + $cleaningFee + $securityMoney;

    //     $promo_host_discount = 0;
    //     $promo_darent_discount = 0;
    //     $promocodeCodeUsage = PromoCodeUsage::where('booking_id', $booking->id)->first();
    //     if (isset($promocodeCodeUsage)) {
    //         $createdBy = $promocodeCodeUsage->promoCode->created_by;
    //         if ($createdBy == "User") {
    //             $promo_host_discount = $promocodeCodeUsage->discount_value;
    //         } elseif ($createdBy == "Admin") {
    //             $promo_darent_discount = $promocodeCodeUsage->discount_value;
    //         }
    //     }

    //     $netSales = $totalBookingPrice - ($promo_host_discount + $promo_darent_discount);
    //     $property_fees_DARENT_INSURANCE = PropertyFees::where('field', PropertyFees::DARENT_INSURANCE)->first();
    //     $percent = $booking->host->commission + $property_fees_DARENT_INSURANCE->value; // Dynamic for every host default(15%)

    //     // if ($booking->referal_code != null) { //IF REFERAL LINK WAS USED WHILE BOOKING
    //     //     $property_fees = PropertyFees::where('field', PropertyFees::DARENT_COMMISSION)->first();
    //     //     $percent = (int)$property_fees->value + $property_fees_DARENT_INSURANCE->value;
    //     // }

    //     $propertyFeesInsurance = PropertyFees::where('field', PropertyFees::DARENT_INSURANCE)->first();
    //     $percent = $booking->host->commission + $propertyFeesInsurance->value; // Dynamic for every host default(15%)

    //     if ($booking->referal_code != null) { // If referral link was used while booking
    //         $propertyFeesCommission = PropertyFees::where('field', PropertyFees::DARENT_COMMISSION)->first();
    //         $percent = (int)$propertyFeesCommission->value + $propertyFeesInsurance->value;
    //     }


    //     $darentCommissionAmount = ($netSales + $promo_darent_discount) * ($percent / 100);

    //     $base_15_percent = $basePrice * $percent / 100;
    //     $cleaning_15_percent = $cleaningFee * $percent / 100;

    //     $totalcharge = round($base_15_percent + $cleaning_15_percent, 2);

    //     $VATfromCommission = round($totalcharge * $percent / 100, 2);
    //     $insurance_fees_percentage = PropertyFees::where('field', 'insurance_fees_percentage')->first();
    //     $data = [
    //         'guestpaid' => $guestpaid,
    //         'hostid' => $booking->host->id,
    //         // 'commission' => $totalcharge,
    //         'commission' => $darentCommissionAmount,
    //         'commission_in_percenatge' => $percent,
    //         'insurance_fees_percentage' => $insurance_fees_percentage->value,
    //         'security_deposit' => $booking->properties->property_price->security_fee,
    //         'VAT' => $VATfromCommission,
    //         'host_pay' => round(($netSales + $promo_darent_discount) - $darentCommissionAmount, 2)
    //     ];

    //     return $data;
    // }

    public static function payableToHost($booking)
    {

        $mabaat_charges = ($booking->properties?->platform_id == 4) ? $booking->iva_tax + $booking->cleaning_charge : 0;
        $mabaat_vat = ($booking->properties?->platform_id == 4) ? $booking->iva_tax : 0;

        // Pricing
        $basePrice = 0;
        $guestPaid = $booking->total_with_discount != 0 ? $booking->total_with_discount : $booking->total;
        $prices = json_decode($booking->date_with_price) ?? [];
        $basePrice = $booking->base_price_with_discount != 0 ? $booking->base_price_with_discount : $booking->base_price;

        // Fees
        $cleaningFee = $booking->cleaning_charge + $mabaat_vat;
        $securityMoney = $booking->security_money;

        // Total
        $totalamount = $basePrice + $cleaningFee;
        $totalBookingPrice = $basePrice + $cleaningFee + $securityMoney;

        // Promo Code
        $promo_host_discount = 0;
        $promo_darent_discount = 0;
        $promocodeCodeUsage = PromoCodeUsage::where('booking_id', $booking->id)->first();

        if (!empty($promocodeCodeUsage) && isset($promocodeCodeUsage)) {
            $createdBy = $promocodeCodeUsage->promoCode->created_by ?? "";
            if ($createdBy == "User") $promo_host_discount = $promocodeCodeUsage->discount_value;
            elseif ($createdBy == "Admin") $promo_darent_discount = $promocodeCodeUsage->discount_value;

            if ($promocodeCodeUsage->promoCode?->is_campaign == 1) {

                $totalBookingPrice = $totalBookingPrice - $promocodeCodeUsage->discount_value;
            }
        }



        // Net Sales & Fee Percentage
        $netSales = $totalBookingPrice;

        // Host Discount
        if ($promo_host_discount != 0) $netSales = $totalBookingPrice;

        // Darent Discount
        if ($promo_darent_discount != 0) $netSales = $totalBookingPrice;


        // Getting Insurance
        $insurance = PropertyFees::where('field', PropertyFees::DARENT_INSURANCE)->first();
        $insuranceFeePercentage = $insurance->value;
        $insuranceFee = ($netSales * $insuranceFeePercentage) / 100;

        // Commission
        $bookingHostComissionPercentage = $booking->host->commission;


        // Total Insurance Fees & Commission
        $feesPercentage = $bookingHostComissionPercentage + $insuranceFeePercentage;

        // Commission (Through Referral Code)
        if ($booking->referal_code != null) {
            $commission = PropertyFees::where('field', PropertyFees::DARENT_COMMISSION)->first();
            $feesPercentage = (int)$commission->value + $insuranceFeePercentage;
        }


        // Calculation
        $bookingHostComissionAmount = $booking->properties?->platform_id == 6? ($feesPercentage * $totalamount) / 100: ($feesPercentage * $booking->base_price) / 100;

        $feesAmount = $bookingHostComissionAmount;


        $commissionAmount = $booking->properties?->platform_id == 6 ? $totalamount * $feesPercentage / 100 : $basePrice * $feesPercentage / 100;

        // $commissionAmount = $netSales * $feesPercentage / 100;
        $base_15_percent = $basePrice * $feesPercentage / 100;
        $cleaning_15_percent = $cleaningFee * $feesPercentage / 100;
        $totalcharge = round($base_15_percent + $cleaning_15_percent, 2);
        $vatFromCommission = round($totalcharge * $feesPercentage / 100, 2);



        $hostPay = $booking->properties?->platform_id == 6? round($totalamount - $feesAmount, 2):round($booking->base_price - $feesAmount, 2) + $cleaningFee; ;

        $data = [
            'guestpaid' => $guestPaid,
            'hostid' => $booking->host->id,
            'commission' => $commissionAmount,
            'commission_in_percenatge' => $feesPercentage,
            'insurance_fees_percentage' => $insuranceFeePercentage,
            'security_deposit' => $booking->properties->property_price->security_fee,
            'VAT' => $vatFromCommission,
            'host_pay' => $hostPay,
            'host_discount' => $promo_host_discount,
            'darent_discount' => $promo_darent_discount,
            'mabaat_charges' => ($booking->properties?->platform_id == 4) ? $booking->iva_tax + $booking->cleaning_charge : 0,

        ];

        return $data;
    }

    public static function getHostInProgressListing($hostId)
    {
        return Properties::where('host_id', $hostId)
            ->whereRaw('id IN (SELECT property_id FROM StepsCompleted WHERE total_steps != 0)')
            ->orderByDesc('id')
            ->get();
    }

    function sendOtp(string $phone, string $code): bool
    {
        try {
            FacadesLog::info('OTP start sending', [
                'phone' => $phone,
                'otp' => $code,
            ]);

            // Prevent sending SMS in local environment
            if (app()->environment('local')) {
                FacadesLog::info('SMS not sent - Local environment detected');
                return true;
            }
            $client = new Client([
                'base_uri' => config('app.unifonic.base_url'),
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ]
            ]);

            $message = "Your Darent verification code is {$code}. Do not share this code with anyone; our team will never ask for it.";

            $res = $client->post('rest/SMS/messages', [
                'json' => [
                    'AppSid' => config('services.unifonic.app_sid'),
                    'SenderID' => config('services.unifonic.sender_id'),
                    'Body' => $message,
                    'Recipient' => $phone,
                    'responseType' => 'JSON',
                    'CorrelationID' => 'CorrelationID',
                    'baseEncode' => 'true',
                    'statusCallback' => 'sent',
                    'async' => 'false',
                ]
            ]);

            if ($res->getStatusCode() == 200) {
                $result = json_decode((string)$res->getBody())->success;

                if (!$result) {
                    FacadesLog::error('Failed to send OTP', [
                        'phone' => $phone,
                        'otp' => $code,
                        'error' => json_decode((string)$res->getBody())
                    ]);

                    throw new \Exception('Unifonic API returned an error.');
                }

                FacadesLog::info('OTP sent', [
                    'phone' => $phone,
                    'otp' => $code,
                ]);

                return true;
            }

            FacadesLog::error('Failed to send OTP', [
                'phone' => $phone,
                'otp' => $code,
                'error' => $res->getStatusCode()
            ]);

            throw new \Exception('Unifonic API responded with HTTP ' . $res->getStatusCode());
        } catch (\Exception $e) {
            FacadesLog::error('Failed to send OTP via Unifonic', [
                'phone' => $phone,
                'code' => $code,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    public function staahBooking($bookingId)
    {
        $postData = '';
        try {
            $booking = Bookings::with('properties', 'payment_type')->find($bookingId);
            $guest = $booking->guest()->first();


            // FacadesLog::debug('message-----asdaksdnk',[$booking, $booking->properties->platform_id]);
            $status = 'Confirm';
            if (!$booking || !$guest || !$booking->properties->platform_id) {
                return false;
            }

            if ($booking->properties->platform_id != 4) {
                return false;
            }

            // Determine status based on booking status
            $status = '';
            if ($booking->status == Bookings::CANCELLED) {
                $status = 'Cancel';
            } elseif ($booking->status == Bookings::ACCEPTED) {
                $status = 'Confirm';
            } else {
                throw new Exception("Invalid booking status.");
            }


            $platform = Platform::find($booking->properties->platform_id);
            if (!$platform) {
                throw new Exception("Platform not found.");
            }

            $postData = $this->buildPostData($booking, $guest, $status);

            // FacadesLog::debug('message-----asdaksdnk',[$booking, $booking->properties->platform_id]);

            $response = Http::withHeaders([
                'User-Agent' => 'Apidog/1.0.0 (https://apidog.com)',
                'Content-Type' => 'application/json',
            ])->post('https://cmbooking.otaswitch.com/common-cgi/booking_darent.pl', $postData);

            $httpStatusCode = $response->status();
            $responseBody = $response->body();

            $this->logApiResponse('https://cmbooking.otaswitch.com/common-cgi/booking_darent.pl', 'POST', $postData, $httpStatusCode, $responseBody);

            return $responseBody;
        } catch (Throwable $th) {
            $this->logApiResponse('https://cmbooking.otaswitch.com/common-cgi/booking_darent.pl', 'POST', $postData, $th->getCode(), $th->getMessage());
            throw $th; // Optionally re-throw the exception to be handled by a higher-level handler.
        }
    }

    private function buildPostData($booking, $guest, $status)
    {
        $total_discount = $booking->total_discount ?? 0;
        $total_with_discount = $booking->total_with_discount && $booking->total_with_discount != 0 ? $booking->total_with_discount : $booking->total;
        $guest_count = $booking->guest ?? 1;

        // Build the price array with multiple dates and corresponding prices
        $priceArray = [];
        $dateWithPrice = json_decode($booking->date_with_price, true);

        // Get the related property price
        $propertyPrice = $booking->propertyPrice;

        $extraAdult = 0;
        $extraAdultRate = 0;

        if ($booking->guest > $propertyPrice->guest_after) {
            $extraAdult = $booking->guest - $propertyPrice->guest_after;
            // $extraAdultRate = $extraAdult * $propertyPrice->guest_fee;
            $extraAdultRate = $extraAdult * $propertyPrice->guest_fee * $booking->total_night;
        }

        $roomAmountAfterTax = $total_with_discount + $extraAdultRate;
        $totalAmountAfterTax = $roomAmountAfterTax; // Assuming only one room, else sum of all rooms


        FacadesLog::debug('dateWithPrice', [$dateWithPrice]);

        foreach ($dateWithPrice as $datePrice) {
            $adjustedPrice = $total_with_discount / $booking->total_night;
            $priceArray[] = [
                "date" => $datePrice['date'],
                "rate_id" => "1",
                "rate_name" => "BAR",
                "amountaftertax" => (string)$adjustedPrice,
                "extraGuests" => [
                    "extraAdult" => (string)$extraAdult,
                    "extraChild" => "0",
                    "extraAdultRate" => (string)($extraAdult * $propertyPrice->guest_fee), // daily extra adult rate
                    "extraChildRate" => "0",
                ],
            ];
        }
        $timestamp = $booking->updated_at;
        if ($status == 'Cancel') {
            $timestamp = $booking->cancelled_at;
        }

        $start_date = new DateTime($booking->start_date);
        $end_date = new DateTime($booking->end_date);

        // Check if start date and end date are the same
        if ($start_date == $end_date) {
            $end_date->modify('+1 day');
        }

        return [
            "propertyid" => $booking->property_id,
            "apikey" => env('API_KEY', 'fNVo-JfhU7-dJw8wZ-h2rXeb-MYIjs-Bg5emf'),
            "action" => "reservation_info",
            "version" => "2",
            "reservations" => [
                "reservation" => [
                    [
                        "reservation_datetime" => Carbon::createFromFormat('Y-m-d H:i:s', $timestamp)->format('Y-m-d\TH:i:s'),
                        "propertyname" => $booking->properties->name,
                        "reservation_id" => $booking->code,
                        "payment_required" => "0",
                        "payment_type" => 'Channel Collect',
                        "commissionamount" => "0.00",
                        "discountamount" => (string)$total_discount,
                        "deposit" => "0.00",
                        "totalamountaftertax" => (string)$totalAmountAfterTax,
                        "totaltax" => "0",
                        "currencycode" => "AED",
                        "status" => $status,
                        "customer" => [
                            "address" => "",
                            "city" => "",
                            "country" => "Saudi Arabia",
                            "email" => $guest->email ?? '',
                            "salutation" => $guest->gender === 'male' ? 'Mr.' : 'Ms.',
                            "first_name" => $guest->first_name,
                            "last_name" => $guest->last_name,
                            "remarks" => "Do not Disturb",
                            "telephone" => $guest->formatted_phone,
                            "zip" => "",
                        ],
                        "room" => [
                            [
                                "arrival_date" => $start_date->format('Y-m-d'),
                                "departure_date" => $end_date->format('Y-m-d'),
                                "room_id" => $booking->property_id,
                                "room_name" => $booking->properties->name,
                                "price" => $priceArray,
                                "salutation" => $guest->gender === 'male' ? 'Mr.' : 'Ms.',
                                "first_name" => $guest->first_name,
                                "last_name" => $guest->last_name,
                                "taxes" => [],
                                "Addons" => [],
                                "amountaftertax" => (string)$roomAmountAfterTax,
                                "remarks" => "No Smoking",
                                "GuestCount" => [
                                    [
                                        "AgeQualifyingCode" => "10",
                                        "Count" => (string)$guest_count,
                                    ],
                                ],
                            ],
                        ],
                        "POS" => "DARENT",
                    ],
                ],
            ],
        ];
    }


    private function logApiResponse($url, $method, $payload, $statusCode, $response)
    {
        $logPayload = [
            'request_url' => $url,
            'request_method' => $method,
            'request_payload' => json_encode($payload), // Convert array to JSON string
            'response_code' => $statusCode,
            'response_data' => $response,
        ];

        PlatformApiLog::createPlatformLogs($logPayload, 1);
    }


    public function bookingInsurance($booking)
    {
        $propertyType = $booking->properties->propertyType;
        if ($propertyType->property_insurance_amount) {
            // --------------------Calculation------------------------------------------------
            $buildingAmount = (($propertyType->property_insurance_amount * ($propertyType->property_insurance_percentage / 100)) / 365) * $booking->total_night;
            $contentAmount = (($propertyType->content_insurance_amount * ($propertyType->content_insurance_percentage / 100)) / 365) * $booking->total_night;
            $responsibilityAmount = $propertyType->responsibility_per_day * $booking->total_night;
            $rentAmount = ($propertyType->rent_insurance_amount * ($propertyType->rent_insurance_percentage / 100)) * $booking->total_night;
            $actualTotal = $buildingAmount + $contentAmount + $responsibilityAmount + $rentAmount;
            $insuranceCap = PropertyFees::where('field', 'insurance_cap')->first();
            // --------------------Calculation------------------------------------------------

            $bookingInsurance = new BookingInsurance();
            $bookingInsurance->fill([
                'booking_id' => $booking->id,
                'building_amount' => round($buildingAmount, 4),
                'content_amount' => round($contentAmount, 4),
                'responsibility_amount' => round($responsibilityAmount, 4),
                'rent_amount' => round($rentAmount, 4),
                'actual_total' => round($actualTotal, 4),
                'cap_amount' => $insuranceCap->value,
            ]);

            $this->getLogs($bookingInsurance, 'admin');
            $bookingInsurance->save();

            $data = [
                'msg' => 'Insurance has been succesfully',
                'status' => 'success',
            ];
            return $data;
        }

        $data = [
            'msg' => 'Unable to create insurance due to lack of insurance data',
            'status' => 'error',
        ];
        return $data;
    }

    /*************  ✨ Codeium Command ⭐  *************/
    /**
     * Duplicates a property.
     *
     * @param int $count How many properties to duplicate.
     * @param int $originalPropertyId The ID of the property to duplicate.
     * @param string $reqSource The source of the request (web or api).
     *
     * @return bool
     */
    /******  fa3f9988-8db7-460e-aa22-7074df3c4a1c  *******/
    public function duplicateEntry($count, $originalPropertyId, $reqSource)
    {
        // $count is for: how many properties duplicate
        // $originalPropertyId is for: what property do want duplicate
        // $reqSource is for log web/api
        return false;
        //loop execute no_of_apparment times if greter than one
        $property = Properties::find($originalPropertyId);
        if ($count > 1) {
            for ($i = 1; $i < $count; $i++) {
                //PROPERTY ENTRY
                $duplicate_property = new Properties;
                $duplicate_property->host_id = Auth::id();
                $duplicate_property->name = $property->name;
                $duplicate_property->name_ar = $property->name_ar;
                $duplicate_property->bedrooms = $property->bedrooms;
                $duplicate_property->beds = $property->beds;
                $duplicate_property->single_beds = $property->single_beds;
                $duplicate_property->double_beds = $property->double_beds;

                //new columns added
                $duplicate_property->single_beds = $property->single_beds;
                $duplicate_property->double_beds = $property->double_beds;

                $duplicate_property->bathrooms = $property->bathrooms;
                $duplicate_property->amenities = $property->amenities;
                $duplicate_property->property_type = $property->property_type;
                $duplicate_property->space_type = $property->space_type;
                $duplicate_property->accommodates = $property->accommodates;
                $duplicate_property->adult_guest = $property->adult_guest;
                $duplicate_property->children_guest = $property->children_guest;
                $duplicate_property->booking_type = $property->booking_type;
                $duplicate_property->cancellation = $property->cancellation;
                $duplicate_property->status = 'Unlisted';
                $duplicate_property->visibility = 0;
                $duplicate_property->no_of_appartment = 1;
                $duplicate_property->max_nights = $property->max_nights;
                $duplicate_property->min_nights = $property->min_nights;

                $duplicate_property->save();
                $duplicate_property->slug = $this->pretty_url($property->name, $duplicate_property->property_code);
                $duplicate_property->save();
                $this->getLogs($duplicate_property, $reqSource);

                $duplicatedPropId = $duplicate_property->id;
                //STEPS ENTRY
                $duplicate_property_steps = new PropertySteps;
                $duplicate_property_steps->property_id = $duplicatedPropId;
                $duplicate_property_steps->save();

                $this->propertyStep($duplicatedPropId, 'amenities');
                $this->propertyStep($duplicatedPropId, 'spacetype');
                $this->propertyStep($duplicatedPropId, 'title');
                $this->propertyStep($duplicatedPropId, 'basics');
                $this->propertyStep($duplicatedPropId, 'booking');
                $this->propertyStep($duplicatedPropId, 'numberofRoom');


                //DESCRIPTION STEP
                $duplicate_property_description = new PropertyDescription;
                $original_property_description = PropertyDescription::where('property_id', $originalPropertyId)->first();
                $duplicate_property_description->property_id = $duplicatedPropId;
                $duplicate_property_description->summary = $original_property_description->summary;
                $duplicate_property_description->summary_ar = $original_property_description->summary_ar;

                $duplicate_property_description->save();
                $this->getLogs($duplicate_property_description, $reqSource);
                $this->propertyStep($duplicatedPropId, 'description');

                //ADDRESS STEP
                $duplicate_property_address = new PropertyAddress;
                $original_property_address = PropertyAddress::where('property_id', $originalPropertyId)->first();
                $duplicate_property_address->property_id = $duplicatedPropId;
                $duplicate_property_address->address_line_1 = $original_property_address->address_line_1;
                $duplicate_property_address->address_line_2 = $original_property_address->address_line_2;
                $duplicate_property_address->latitude = $original_property_address->latitude;
                $duplicate_property_address->longitude = $original_property_address->longitude;
                $duplicate_property_address->city = $original_property_address->city;
                $duplicate_property_address->state = $original_property_address->state;
                $duplicate_property_address->country = $original_property_address->country;
                $duplicate_property_address->postal_code = $original_property_address->postal_code;
                $duplicate_property_address->save();

                $this->getLogs($duplicate_property_address, $reqSource);
                $this->propertyStep($duplicatedPropId, 'location');
                $this->propertyStep($duplicatedPropId, 'confirmLocation');


                //PRICE STEP
                $duplicate_property_price = new PropertyPrice;
                $original_property_price = PropertyPrice::where('property_id', $originalPropertyId)->first();
                $duplicate_property_price->property_id = $duplicatedPropId;
                $duplicate_property_price->price = $original_property_price->price;
                $duplicate_property_price->weekly_discount = $original_property_price->weekly_discount;
                $duplicate_property_price->monthly_discount = $original_property_price->monthly_discount;
                $duplicate_property_price->currency_code = $original_property_price->currency_code;
                $duplicate_property_price->cleaning_fee = $original_property_price->cleaning_fee;
                $duplicate_property_price->guest_fee = $original_property_price->guest_fee;
                $duplicate_property_price->guest_after = $original_property_price->guest_after;
                $duplicate_property_price->security_fee = $original_property_price->security_fee;
                $duplicate_property_price->weekend_price = $original_property_price->weekend_price;
                $duplicate_property_price->special_days_price = $original_property_price->special_days_price;
                $duplicate_property_price->save();

                $this->getLogs($duplicate_property_price, $reqSource);
                $this->propertyStep($duplicatedPropId, 'pricing');

                //PHOTOS STEP
                $duplicate_path = 'images/property/' . $duplicatedPropId . '/';
                if (!file_exists($duplicate_path)) {
                    mkdir($duplicate_path, 0777, true);
                }

                $original_property_photos = PropertyPhotos::where('property_id', $originalPropertyId)->get();

                foreach ($original_property_photos as $original_photo) {

                    $duplicate_property_photos = new PropertyPhotos;
                    $duplicate_property_photos->property_id = $duplicatedPropId;
                    $duplicate_property_photos->photo = $original_photo->photoName;
                    $duplicate_property_photos->serial = $original_photo->serial;
                    $duplicate_property_photos->cover_photo = $original_photo->cover_photo;
                    $duplicate_property_photos->save();
                }

                // Source folder
                $sourceFolder = 'images/property/' . $originalPropertyId . '/';

                // Destination folder
                $destinationFolder = $duplicate_path;

                // Get a list of image files from the source folder
                $imageFiles = File::allFiles(public_path($sourceFolder));

                // Loop through each file and copy it to the destination folder
                foreach ($imageFiles as $file) {
                    $filename = $file->getRelativePathname(); // Get the filename with the relative path
                    $newFilePath = public_path($destinationFolder . '/' . $filename);

                    // Copy the file to the new destination
                    File::copy($file->getPathname(), $newFilePath);
                }

                $this->getLogs($duplicate_property_photos, $reqSource);
                $this->propertyStep($duplicatedPropId, 'photos');
                $this->propertyStep($duplicatedPropId, 'setCover');
                $this->propertyStep($duplicatedPropId, 'nightsandtime');
                $this->propertyStep($duplicatedPropId, 'reviewListing');
            }
            // Originial Porperty update no of appartments to one
            $property->no_of_appartment = 1;
            $property->save();
        }
    }

    public function propertyStep($prop_id, $columns)
    {
        // dd($columns);
        $count = PropertySteps::where('property_id', $prop_id)->where($columns, 0)->count();
        if ($count) {
            return PropertySteps::where('property_id', $prop_id)->update([$columns => 1]);
        }
        return true;
    }

    function d($var, $a = false)
    {
        echo "<pre>";
        print_r($var);
        echo "</pre>";
        if ($a) exit;
    }

    public function content_read($url)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_AUTOREFERER, TRUE);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);

        $data = curl_exec($ch);
        curl_close($ch);

        return $data;
    }

    public function one_time_message($class, $message)
    {

        if ($class == 'error') $class = 'danger';
        FacadesSession::flash('alert-class', 'alert-' . $class);
        FacadesSession::flash('message', $message);
        FacadesSession::flash('error', $message);
    }

    public static function key_value($key, $value, $ar)
    {
        $ret = [];
        foreach ($ar as $k => $v) {
            $ret[$v[$key]] = $v[$value];
        }
        return $ret;
    }

    public function current_action($route)
    {
        $current = explode('@', $route);
        View::share('current_action', $current[1]);
    }

    public static function has_permission($user_id, $permissions = null)
    {
        $permissions = explode('|', $permissions);
        $user_permissions = Permissions::getAll()->whereIn('name', $permissions);

        $permission_id = [];
        $i = 0;
        foreach ($user_permissions as $value) {
            $permission_id[$i++] = $value->id;
        }

        $role = RoleAdmin::where('admin_id', $user_id)->first();

        if (count($permission_id) && isset($role->role_id)) {
            $has_permit = PermissionRole::where('role_id', $role->role_id)->whereIn('permission_id', $permission_id);
            return $has_permit->count();
        } else return 0;
    }

    public static function meta($url, $field)
    {
        $metas = Meta::where('url', $url);

        if ($metas->count())
            return $metas->first()->$field;
        else if ($field == 'title')
            // return 'Page Not Found';
            return 'Darent';
        else
            return '';
    }

    public function vrCacheForget($key)
    {
        Cache::forget($key);
    }

    function backup_tables($host, $user, $pass, $name, $tables = '*')
    {
        try {
            $con = mysqli_connect($host, $user, $pass, $name);
        } catch (Exception $e) {
        }

        if (mysqli_connect_errno()) {
            $this->one_time_message('danger', "Failed to connect to MySQL: " . mysqli_connect_error());
            return 0;
        }

        if ($tables == '*') {
            $tables = [];
            $result = mysqli_query($con, 'SHOW TABLES');
            while ($row = mysqli_fetch_row($result)) {
                $tables[] = $row[0];
            }
        } else {
            $tables = is_array($tables) ? $tables : explode(',', $tables);
        }

        $return = '';
        foreach ($tables as $table) {
            $result = mysqli_query($con, 'SELECT * FROM ' . $table);
            $num_fields = mysqli_num_fields($result);


            $row2 = mysqli_fetch_row(mysqli_query($con, 'SHOW CREATE TABLE ' . $table));
            $return .= "\n\n" . str_replace("CREATE TABLE", "CREATE TABLE IF NOT EXISTS", $row2[1]) . ";\n\n";

            for ($i = 0; $i < $num_fields; $i++) {
                while ($row = mysqli_fetch_row($result)) {
                    $return .= 'INSERT INTO ' . $table . ' VALUES(';
                    for ($j = 0; $j < $num_fields; $j++) {
                        $row[$j] = addslashes($row[$j]);
                        $row[$j] = preg_replace("/\n/", "\\n", $row[$j]);
                        if (isset($row[$j])) {
                            $return .= '"' . $row[$j] . '"';
                        } else {
                            $return .= '""';
                        }
                        if ($j < ($num_fields - 1)) {
                            $return .= ',';
                        }
                    }
                    $return .= ");\n";
                }
            }

            $return .= "\n\n\n";
        }

        $backup_name = date('Y-m-d-His') . '.sql';

        $handle = fopen(storage_path("db-backups") . '/' . $backup_name, 'w+');
        fwrite($handle, $return);
        fclose($handle);

        return $backup_name;
    }

    public function add_notification($user_id, $message)
    {
        $notification = new Notification;
        $notification->user_id = $user_id;
        $notification->message = $message;
        $notification->status = 'unread';
        $notification->save();
    }

    public static function thousandsCurrencyFormat($num)
    {
        if ($num < 1000) return $num;
        $x = round($num);
        $x_number_format = number_format($x);
        $x_array = explode(',', $x_number_format);
        $x_parts = ['k', 'm', 'b', 't'];
        $x_count_parts = count($x_array) - 1;
        $x_display = $x;
        $x_display = $x_array[0] . ((int)$x_array[1][0] !== 0 ? '.' . $x_array[1][0] : '');
        $x_display .= $x_parts[$x_count_parts - 1];
        return $x_display;
    }

    public function senitize($val)
    {
        $inp = trim($val);
        $inp = strip_tags($inp);
        $inp = htmlspecialchars($inp);
        return $inp;
    }

    public function pretty_url($str, $code)
    {
        $url = $this->convert_to_url_friendly($str);
        $turl = $url . '-' . $code;

        // $turl = $url;
        // $i = 0;
        // while (1) {
        //     $i++;
        //     $cnt = Properties::where('slug', $turl)->count();
        //     if ($cnt != 0)
        //         $turl = $url . '-' . $i;
        //     else break;
        // }
        return $turl;
    }

    public function convert_to_url_friendly($str, $replace = [], $delimiter = '-')
    {
        if (!empty($replace)) {
            $str = str_replace((array)$replace, ' ', $str);
        }

        if (class_exists('Transliterator')) {
            $transliterator = Transliterator::create('NFKD; Latin; Latin-ASCII');
            $str = $transliterator->transliterate($str);
        } else {
            $str = preg_replace('/[^\x20-\x7E]/u', '', $str);
        }

        $str = preg_replace("/[^a-zA-Z0-9_|+ -]/", '', $str);
        $str = strtolower(trim($str, '-'));
        $str = preg_replace("/[_|+ -]+/", $delimiter, $str);

        return $str;
    }


    // public function convert_to_url_friendly($str, $replace=array(), $delimiter='-')
    // {
    //     if( !empty($replace) ) {
    //         $str = str_replace((array)$replace, ' ', $str);
    //     }
    //     // dd($str);
    //     $clean = iconv('UTF-8', 'ASCII//TRANSLIT', $str);
    //     $clean = preg_replace("/[^a-zA-Z0-9_|+ -]/", '', $clean);
    //     $clean = strtolower(trim($clean, '-'));
    //     $clean = preg_replace("/[_|+ -]+/", $delimiter, $clean);

    //     return $clean;
    // }

    public function currency_rate($from, $to)
    {
        $from_rate = Currency::getAll()->firstWhere('code', $from)->rate;
        $to_rate = Currency::getAll()->firstWhere('code', $to)->rate;

        $rate = round($from_rate / $to_rate, 6);
        if ((int)$rate)
            return $rate;
        return round($from_rate / $to_rate, 6);
    }

    public function convert_currency($from = null, $to = null, $price = null)
    {
        $from = currentCurrency($from);
        $to = currentCurrency($to);
        $price = str_replace(']', '', $price); //For Php Version 7.2
        $base_amount = (float)$price / $from->rate;

        return round($base_amount * $to->rate, $to->rate > 1000 ? 0 : 2);
    }


    public function getCurrentCurrencySymbol()
    {
        return Session::get('currency') ? Currency::code_to_symbol(Session::get('currency'))
            : Currency::getAll()->firstWhere('default', 1)->symbol;
    }

    public function getCurrentCurrencyCode()
    {
        return Session::get('currency') ?? Currency::getAll()->firstWhere('default', 1)->code;
    }

    public function getCurrentCurrency()
    {
        return Session::get('currency') ? Currency::firstWhere('code', Session::get('currency'))
            : Currency::getAll()->firstWhere('default', 1);
    }

    public function getPrice2(Properties $property, $date, $number_of_days = 1): array
    {
        if (gettype($property->property_price->special_days_price) == 'string') {
            $property->property_price->special_days_price = json_decode($property->property_price->special_days_price);
        }
        $newDate = $date->format('Y-m-d');
        $special_days = ["thursday", "friday", "saturday"];
        $day_price_after_discount = 0;
        $differentPriceDatePrice = DB::table('custom_pricing')
            ->where('property_id', $property->id)
            ->where('date', $newDate)
            ->orderBy('date')
            ->get()
            ->toArray() ?? [];

            $discount = 0;
            $day = lcfirst($date->format('l')); // day of week
            $dayAmount = in_array($day, $special_days) && property_exists($property->property_price->special_days_price, $day) ? $property->property_price->special_days_price->{$day} : $property->property_price->price;

            if (in_array($newDate, array_keys($differentPriceDatePrice))) {
                if (!!$differentPriceDatePrice[$newDate]['price']) {
                    $dayAmount = $differentPriceDatePrice[$newDate]['price'];
                }
                $discount = $differentPriceDatePrice[$newDate]['discount'];
            }

            $day_price_after_discount += $dayAmount - ($dayAmount * ($discount / 100));

        $discount = 0;
        $weekly_discount_amount = 0;
        $monthly_discount_amount = 0;
        $monthly_discount = $property->property_price->monthly_discount ?? 0;
        $weekly_discount = $property->property_price->weekly_discount ?? 0;
        if ($weekly_discount > 0) {
            $weekly_discount_amount = round(($day_price_after_discount * ($weekly_discount) / 100), 2);
        } elseif ($monthly_discount > 0) {
            $monthly_discount_amount = round(($day_price_after_discount * $monthly_discount) / 100, 2);
        }

        $total_days_amount = $day_price_after_discount - $discount;
        return [
            'price' =>  round($day_price_after_discount,2),
            'weekly_discount_amount' => round($weekly_discount_amount,2),
            'monthly_discount_amount' => round($monthly_discount_amount,2)
        ];

    }

    public static function getWeeklyMonthlyDiscount(Properties $property, $dateRange): array
    {
        $symbol = customTrans('utility.sar');

        // Prepare an array of date strings
        $dates = [];
        foreach ($dateRange as $date) {
            $dates[] = $date->format('Y-m-d');
        }

        // Fetch determine prices from availability
        $dailyPricingData = $property->getDeterminPrices($dates);

        // Fill missing dates with default price and default discounts
        $dailyPrices = [];
        foreach ($dates as $date) {
            if (isset($dailyPricingData[$date])) {
                $dailyPrices[$date] = [
                    'price' => $dailyPricingData[$date]['price'],
                    'weekly_discount' => $dailyPricingData[$date]['weekly_discount'] ?? $dailyPricingData[$date]['price'] ?? 0,
                    'monthly_discount' => $dailyPricingData[$date]['monthly_discount'] ?? $dailyPricingData[$date]['price'] ?? 0,
                ];
            } else {
                $dailyPrices[$date] = [
                    'price' => $property->property_price->price,
                    'weekly_discount' => $property->property_price->price ?? 0,
                    'monthly_discount' => $property->property_price->price ?? 0,
                ];
            }
        }

        // Flattened values for original total
        $totalBeforeDiscount = array_sum(array_column($dailyPrices, 'price'));
        $discountedTotal = 0;
        $monthly_discount = $property->property_price->monthly_discount ?? 0;
        $weekly_discount = $property->property_price->weekly_discount ?? 0;
        $values = array_values($dailyPrices); // preserve order
        $totalDays = count($values);
        $i = 0;

        while ($i < $totalDays) {
            // Check monthly discount eligibility
            if ($i + 28 <= $totalDays && $monthly_discount > 0) {
                $monthBlock = array_slice($values, $i, 28);
                $monthlyDiscount = array_sum(array_column($monthBlock, 'monthly_discount')); // choose the smallest applicable
                $discountedTotal += $monthlyDiscount;
                $i += 28;
            }
            // Check weekly discount eligibility
            elseif ($i + 7 <= $totalDays && $weekly_discount > 0) {
                $weekBlock = array_slice($values, $i, 7);
                $weeklyDiscount = array_sum(array_column($weekBlock, 'weekly_discount')); // choose the smallest applicable
                $discountedTotal += $weeklyDiscount;
                $i += 7;
            }
            // Fallback to daily price
            else {
                $discountedTotal += $values[$i]['price'];
                $i++;
            }
        }

        return [
            'total_before_discount' => $totalBeforeDiscount,
            'total_after_discount' => $discountedTotal <= 0 ? $totalBeforeDiscount : $discountedTotal,
        ];
    }
    

    public function getPriceDetails(Properties $property, $dateRange, $number_of_days, $property_custom_pricing, int $guestCount): array
{
    $rangeAsStrings = iterator_to_array($dateRange->map(fn($date) => $date->format('Y-m-d')));

    $notAvailableDates1 = CustomPricing::where(['property_id' => $property->id])
        ->whereIn('date', $rangeAsStrings)
        ->where('type', 'calendar')
        ->where('status', 'Not available')
        ->pluck('date')->toArray();

    $notAvailableDates2 = PropertyDates::where(['property_id' => $property->id])
        ->whereIn('date', $rangeAsStrings)
        ->where('status', 'Not available')
        ->pluck('date')->toArray();

    $repeatDates1 = array_count_values($notAvailableDates1);
    $repeatDates2 = array_count_values($notAvailableDates2);

    $minDayStay = PropertyDates::where(['property_id' => $property->id])
        ->whereIn('date', $rangeAsStrings)
        ->max('min_stay');

    if (
        count(array_filter($repeatDates1, fn($item) => $item >= $property->no_of_appartment)) ||
        count(array_filter($repeatDates2, fn($item) => $item >= $property->no_of_appartment))
    ) {
        return ['status' => 'Not available'];
    }

    if ($minDayStay && $number_of_days < $minDayStay) {
        return ['status' => 'minimum stay', 'minimum' => $minDayStay];
    }

    if ($property->min_nights > $number_of_days) {
        return ['status' => 'nights become min', 'min_nights' => $property->min_nights];
    }

    if ($property->max_nights < $number_of_days) {
        return ['status' => 'nights become max', 'min_nights' => $property->max_nights];
    }

    $special_days = ["thursday", "friday", "saturday"];
    $property_custom_pricing = $property_custom_pricing->toArray();
    $symbol = customTrans('utility.sar');

    $dates_with_prices = [];
    $day_prices = [];
    $day_price_after_discount = 0;

    foreach ($dateRange as $date) {
        $day = lcfirst($date->format('l'));
        $dayString = $date->format('Y-m-d');

        $dayAmount = in_array($day, $special_days) && property_exists((object)$property->property_price->special_days_price, $day)
            ? $property->property_price->special_days_price->{$day}
            : $property->property_price->price;

        $discount = $property_custom_pricing[$dayString]['discount'] ?? 0;
        if (!empty($property_custom_pricing[$dayString]['price'])) {
            $dayAmount = $property_custom_pricing[$dayString]['price'];
        }

        $final_day_price = $dayAmount - ($dayAmount * ($discount / 100));
        $day_prices[] = $final_day_price;
        $day_price_after_discount += $final_day_price;

        $dates_with_prices[] = [
            'discount' => $discount,
            'price' => moneyFormat($symbol, numberFormat($final_day_price, 2)),
            'original_price' => $dayAmount,
            'date' => setDateForDb($dayString),
        ];
    }

    $monthly_discount = $property->property_price->monthly_discount ?? 0;
    $weekly_discount = $property->property_price->weekly_discount ?? 0;

    $total_discounted_price = 0;
    $remaining_days = count($day_prices);
    $current_index = 0;

    while ($remaining_days >= 28 && $monthly_discount > 0) {
        $block = array_slice($day_prices, $current_index, 28);
        $total_discounted_price += array_sum($block) * (1 - $monthly_discount / 100);
        $current_index += 28;
        $remaining_days -= 28;
    }

    while ($remaining_days >= 7 && $weekly_discount > 0) {
        $block = array_slice($day_prices, $current_index, 7);
        $total_discounted_price += array_sum($block) * (1 - $weekly_discount / 100);
        $current_index += 7;
        $remaining_days -= 7;
    }

    if ($remaining_days > 0) {
        $block = array_slice($day_prices, $current_index, $remaining_days);
        $total_discounted_price += array_sum($block);
    }

    $total_price_before_discounts = array_sum($day_prices);
    $discount = $monthly_or_weekly_discount = $total_price_before_discounts - $total_discounted_price;
    $total_days_amount = $day_price_after_discount - $discount;

    $propertyFees = PropertyFees::pluck('value', 'field');
    $service_fee = round(($propertyFees['guest_service_charge'] / 100) * $total_days_amount, 2);
    $service_fee_with_discount = $service_fee;

    $cleaning_fee = $property?->property_price?->cleaning_fee ?? 0;
    if ($property->platform_id == 6) {
        $propertyPrice = PropertyPrice::where('property_id', $property->id)->first();
        if ($propertyPrice->cleaning_fee_type == 'daily') {
            $cleaning_fee *= $number_of_days;
        }
    }

    if ($cleaning_fee) {
        $service_fee += $cleaning_fee;
        $service_fee_with_discount += round(($propertyFees['guest_service_charge'] / 100) * $cleaning_fee, 2);
    }

    $iva_tax = $property->platform_id == 4
        ? round(0.15 * $total_days_amount, 2)
        : round(($propertyFees['iva_tax'] / 100) * $total_days_amount, 2);

    $silkhaus_markup = 0;
    if ($property->platform_id == 5) {
        $silkhaus_markup = round(($propertyFees['silkhaus'] / 100) * $total_days_amount, 2);
        $service_fee += $silkhaus_markup;
        $service_fee_with_discount += round(($propertyFees['guest_service_charge'] / 100) * $silkhaus_markup, 2);
    }

    $accomodation_tax = round(($propertyFees['accomodation_tax'] / 100) * $total_days_amount, 2);

    $additional_guest = 0;
    if ($guestCount > $property->property_price->guest_after) {
        $additional_guest = ($guestCount - $property->property_price->guest_after)
            * $property->property_price->guest_fee
            * $total_days_amount;
    }

    $total_with_discount = $service_fee_with_discount
        + $total_days_amount
        + $additional_guest
        + ($property?->property_price?->security_fee ?? 0)
        + $cleaning_fee
        + $iva_tax
        + $accomodation_tax
        + $silkhaus_markup;

    $result = [
        'status' => 'available',
        'date_with_price' => $dates_with_prices,
        'total_night_price' => $total_days_amount + $silkhaus_markup,
        'cleaning_fee' => $cleaning_fee,
        'total_nights' => $number_of_days,
        'per_night_price_with_symbol' => moneyFormat(numberFormat($day_price_after_discount / $number_of_days, 2), $symbol),
        'total_night_price_after_discount_with_symbol' => moneyFormat(numberFormat($total_days_amount + $silkhaus_markup, 2), $symbol),
        'total_day_discounted_price_not_weekly_monthly_discount_with_symbol' => moneyFormat(numberFormat($day_price_after_discount, 2), $symbol),
        'yousaved' => moneyFormat($discount, $symbol),
        'yousaved_without_discount' => $discount,
        'service_fee_with_discount' => $service_fee_with_discount,
        'service_fee_with_discount_with_symbol' => moneyFormat(numberFormat($service_fee_with_discount, 2), $symbol),
        'service_fee_with_symbol' => moneyFormat(numberFormat($service_fee, 2), $symbol),
        'discount_with_symbol' => moneyFormat(numberFormat($discount, 2), $symbol),
        'iva_tax' => $iva_tax,
        'iva_tax_with_symbol' => moneyFormat(numberFormat($iva_tax, 2), $symbol),
        'accomodation_tax' => $accomodation_tax,
        'accomodation_tax_with_symbol' => moneyFormat(numberFormat($accomodation_tax, 2), $symbol),
        'additional_guest' => $additional_guest,
        'additional_guest_fee_with_symbol' => moneyFormat(numberFormat($additional_guest, 2), $symbol),
        'security_fee' => $property?->property_price?->security_fee ?? 0,
        'security_fee_with_symbol' => moneyFormat(numberFormat($property?->property_price?->security_fee ?? 0, 2), $symbol),
        'service_fee_security' => round(($propertyFees['guest_service_charge'] / 100) * ($property?->property_price?->security_fee ?? 0), 2),
        'service_fee_cleaning' => round(($propertyFees['guest_service_charge'] / 100) * $cleaning_fee, 2),
        'cleaning_fee_with_symbol' => moneyFormat(numberFormat($cleaning_fee, 2), $symbol),
        'total_with_discount' => $total_with_discount,
        'total_with_discount_with_symbol' => moneyFormat(numberFormat($total_with_discount, 2), $symbol),
        'monthly_or_weekly_discount' => $monthly_or_weekly_discount,
    ];

    return $result;
}


    public function getPropertiesPricing(string $checkin, string $checkout): string
    {

        $dateRange = CarbonPeriod::create(Carbon::parse($checkin), Carbon::parse($checkout));
        $dateRange = iterator_to_array($dateRange->map(fn($date) => $date->format('Y-m-d')));

        $dateRange = implode("','", $dateRange);
        $dateRange = "'" . $dateRange . "'";

        DB::statement('SET SESSION group_concat_max_len=102400');

        return "
select temp.property_id,
       IF(temp.number_of_days < 1, 1, temp.number_of_days) as number_of_days,
       SUM(temp.price) / temp.number_of_days as day_price,
       IF(
               temp.number_of_days >= 7 and temp.number_of_days < 28,
               SUM(temp.price) - ( SUM(temp.price) * temp.weekly_discount / 100 ),
               IF(
                       temp.number_of_days >= 28,
                       SUM(temp.price) - ( SUM(temp.price) * temp.monthly_discount / 100 ),
                       SUM(temp.price)
               )
       ) as total_price,
       SUM(temp.day_price) as before_discount
from
(with recursive dates as (select '" . $checkin . "' as date
                          union all
                          select date + interval 1 day
                          from dates
                          where date < '" . $checkout . "' - interval 1 day)
 select (
            IF(
                    INSTR(properties.custom_pricing, dates.date) > 0 and JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".price'))) > 0,
                    JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".price'))),
                    IF(
                            INSTR(properties.special_days, lower(DAYNAME(dates.date))) > 0 AND
                            INSTR(properties.special_days_price, lower(DAYNAME(dates.date))) > 0,
                            JSON_UNQUOTE(JSON_EXTRACT(properties.special_days_price,
                                                      CONCAT('$.', '\"', lower(DAYNAME(dates.date)), '\"'))),
                            properties.original_price
                    )
            ))                                                                                                      as day_price,
        (IF(INSTR(properties.custom_pricing, dates.date) > 0 and JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".discount'))) > 0,
            JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".discount'))),
            0))                                                                                                     as day_discount,
        dates.date,
        (IF(
                 INSTR(properties.custom_pricing, dates.date) > 0 and JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".price'))) > 0,
                 JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".price'))),
                 IF(
                         INSTR(properties.special_days, lower(DAYNAME(dates.date))) > 0 AND
                         INSTR(properties.special_days_price, lower(DAYNAME(dates.date))) > 0,
                         JSON_UNQUOTE(JSON_EXTRACT(properties.special_days_price,
                                                   CONCAT('$.', '\"', lower(DAYNAME(dates.date)), '\"'))),
                         properties.original_price
                 )
         ) - (IF(
                               INSTR(properties.custom_pricing, dates.date) > 0 and JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".price'))) > 0,
                               JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".price'))),
                               IF(
                                       INSTR(properties.special_days, lower(DAYNAME(dates.date))) > 0 AND
                                       INSTR(properties.special_days_price, lower(DAYNAME(dates.date))) > 0,
                                       JSON_UNQUOTE(JSON_EXTRACT(properties.special_days_price,
                                                                 CONCAT('$.', '\"', lower(DAYNAME(dates.date)), '\"'))),
                                       properties.original_price
                               )
              ) * IF(INSTR(properties.custom_pricing, dates.date) > 0 and JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".discount'))) > 0,
                     JSON_UNQUOTE(JSON_EXTRACT(properties.custom_pricing, CONCAT('$.', '\"', dates.date, '\".discount'))),
                     0) / 100)) as price,
        lower(DAYNAME(dates.date)) as week_day,
        properties.*
 from dates
          cross join (select properties.id                                                               as property_id,
                             DATEDIFF('" . $checkout . "', '" . $checkin . "')                                               as number_of_days,
                             CAST('[
                               \"thursday\",
                               \"friday\",
                               \"saturday\"
                             ]' as json)                                                                 as special_days,
                             ANY_VALUE(property_price.price)                                             as original_price,
                             ANY_VALUE(property_price.weekly_discount)                                   as weekly_discount,
                             ANY_VALUE(property_price.monthly_discount)                                  as monthly_discount,
                             CONCAT('{', GROUP_CONCAT(DISTINCT CONCAT('\"', custom_pricing.date, '\": { \"discount\": ',
                                                                      custom_pricing.discount, ', \"price\": ',
                                                                      custom_pricing.price, ' }')),
                                    '}')                                                          as custom_pricing,
                             CAST(ANY_VALUE(property_price.special_days_price) as json)                  as special_days_price
                      from properties
                               left join property_price on properties.id = property_price.property_id
                               left join custom_pricing on properties.id = custom_pricing.property_id
                          and custom_pricing.type = 'calendar'
                          and custom_pricing.status = 'Available'
                          and custom_pricing.date in (" . $dateRange . ")
                      group by properties.id) as properties) as temp
group by temp.property_id
        ";
    }

    public function getPrice($propertyId, $checkIn, $checkOut, $guestCount, $force = false, $booking_id = null)
    {

        $checkIn = setDateForDb($checkIn);
        $checkOut = setDateForDb($checkOut);
        $date1 = date('Y-m-d', strtotime($checkIn));
        $enddate = date('Y-m-d', strtotime($checkOut));
        $date2 = date('Y-m-d', (strtotime('-1 day', strtotime($enddate))));
        $dates = $this->get_days($date1, $date2);
        $property = Properties::with('users', 'property_price', 'property_address')->firstWhere('id', $propertyId);
        $property_booking_count = $property->no_of_appartment;

        $customPricing = CustomPricing::where(['property_id' => $propertyId])->whereIn('date', $dates)
            ->where('type', 'calendar')->get();


        $checkRepeatDates = CustomPricing::where(['property_id' => $propertyId])
            ->whereIn('date', $dates)->where('type', 'calendar')
            ->where('status', 'Not available');

        $checkRepeatDates2 = PropertyDates::where(['property_id' => $propertyId])
            ->whereIn('date', $dates)
            ->where('status', 'Not available');

        if ($booking_id) {
            // $checkRepeatDates = $checkRepeatDates->where('booking_id', '!=', $booking_id);
            $checkRepeatDates2 = $checkRepeatDates2->where('booking_id', '!=', $booking_id);
        }
        $checkRepeatDates = $checkRepeatDates->pluck('date')->toArray();
        $checkRepeatDates2 = $checkRepeatDates2->pluck('date')->toArray();

        $checkRepeatDates = array_count_values($checkRepeatDates);
        $checkRepeatDates2 = array_count_values($checkRepeatDates2);

        // all days that unit not available in
        foreach ($checkRepeatDates as $date => $dateCount) {
            if ($dateCount >= $property_booking_count) {
                $result['status'] = "Not available";
                return json_encode($result);
            }
        }

        foreach ($checkRepeatDates2 as $date => $dateCount) {
            if ($dateCount >= $property_booking_count) {
                $result['status'] = "Not available";
                return json_encode($result);
            }
        }

        $special_days = Country::orderBy('id')->first()->special_days;

        $result['property_default'] = [
            'price' => $property->property_price->price,
            'currency_code' => $property->property_price->currency_code,
            'symbol' => Currency::getAll()->firstWhere('code', $property->property_price->currency_code)->symbol,
            'rate' => Currency::getAll()->firstWhere('code', $property->property_price->currency_code)->rate,
            'local_to_propertyRate' => $this->currency_rate($property->property_price->currency_code, $this->getCurrentCurrencyCode()),
        ];

        $differentPriceDatePrice = $force ? $customPricing->mapWithKeys(fn($c) => [$c->date => $c->only(['price', 'discount'])])->toArray()
            : $customPricing->where('status', 'Available')->mapWithKeys(fn($c) => [$c->date => $c->only(['price', 'discount'])])->toArray();

        $nightPrice_afterdiscount = 0;
        $countDays = count($dates);

        currentCurrency()->rate > 1000 ? $decimals = 0 : $decimals = 2;

        $symbol = $this->getCurrentCurrencySymbol();
        // WEEKEND PRICE CALCULATION
        $allDate = [];
        foreach ($dates as $key => $date) {
            $discount = 0;
            $day = lcfirst(Carbon::parse($date)->format('l'));
            $dayString = Carbon::parse($date)->format('Y-m-d');

            // Base price logic with special day check
            $dayAmount = in_array($day, $special_days) && property_exists((object)$property->property_price->special_days_price, $day)
                ? $property->property_price->special_days_price->{$day}
                : $property->property_price->price;

            // Apply custom pricing and discount if available
            if (isset($differentPriceDatePrice[$dayString])) {
                if (!empty($differentPriceDatePrice[$dayString]['price'])) {
                    $dayAmount = $differentPriceDatePrice[$dayString]['price'];
                }
                $discount = $differentPriceDatePrice[$dayString]['discount'] ?? 0;
            }

            // Calculate discounted price
            $finalprice = $dayAmount - ($dayAmount * ($discount / 100));
            $nightPrice_afterdiscount += $finalprice;

            // Populate the synced variable structure
            $allDate[$key] = [
                'discount' => $discount,
                'price' => moneyFormat($symbol, numberFormat($finalprice, $decimals)),
                'original_price' => $dayAmount,
                'date' => setDateForDb($dayString),
            ];
        }

        $monthly_discount = $property->property_price->monthly_discount ?? 0;
        $weekly_discount = $property->property_price->weekly_discount ?? 0;

        $day_prices = [];
        foreach ($dates as $date) {
            $dayStr = Carbon::parse($date)->format('Y-m-d');
            $day = lcfirst(Carbon::parse($date)->format('l'));

            $price = in_array($day, $special_days) && property_exists((object)$property->property_price->special_days_price, $day)
                ? $property->property_price->special_days_price->{$day}
                : $property->property_price->price;

            if (isset($differentPriceDatePrice[$dayStr]) && !empty($differentPriceDatePrice[$dayStr]['price'])) {
                $price = $differentPriceDatePrice[$dayStr]['price'];
            }

            $discount = $differentPriceDatePrice[$dayStr]['discount'] ?? 0;
            $final_price = $price - ($price * ($discount / 100));
            $day_prices[] = $final_price;
        }

        $total_discounted_price = 0;
        $remaining_days = count($day_prices);
        $current_index = 0;

        while ($remaining_days >= 28 && $monthly_discount > 0) {
            $block = array_slice($day_prices, $current_index, 28);
            $total_discounted_price += array_sum($block) * (1 - $monthly_discount / 100);
            $current_index += 28;
            $remaining_days -= 28;
        }

        while ($remaining_days >= 7 && $weekly_discount > 0) {
            $block = array_slice($day_prices, $current_index, 7);
            $total_discounted_price += array_sum($block) * (1 - $weekly_discount / 100);
            $current_index += 7;
            $remaining_days -= 7;
        }

        if ($remaining_days > 0) {
            $block = array_slice($day_prices, $current_index, $remaining_days);
            $total_discounted_price += array_sum($block);
        }

        $total_price_before_discounts = array_sum($day_prices);
        $discount = $total_price_before_discounts - $total_discounted_price;
        $result['discount'] = $monthly_or_weekly_discount = round($discount, $decimals);
        $result['total_night_price_before_discount'] = $total_price_before_discounts;
        $result['total_night_price_after_discount'] = $result['total_night_price'] = round($total_discounted_price, $decimals);
        $result['date_with_price'] = $allDate;

        // $minDayStay = PropertyDates::where(['property_id' => $propertyId])->whereIn('date', $dates)->where('min_stay', 1)->max('min_day');
        $minDayStay = PropertyDates::where(['property_id' => $propertyId])->whereIn('date', $dates)->max('min_stay');

        $PropertyForNights = Properties::where('id', $propertyId)->first();

        if ($minDayStay && $minDayStay > $countDays) {
            $result['status'] = 'minimum stay';
            $result['minimum'] = $minDayStay;
            return json_encode($result);
        }

        if ($PropertyForNights->min_nights <= $countDays) {
            if ($PropertyForNights->max_nights >= $countDays) {
                $propertyFees = PropertyFees::pluck('value', 'field');


                $result['property_price'] = round($result['total_night_price'] / $countDays, $decimals);
                $result['property_price_before_discount'] = round($result['total_night_price_before_discount'] / $countDays, $decimals);
                $result['total_nights'] = $countDays;

                $result['service_fee'] = round(($propertyFees['guest_service_charge'] / 100) * $result['total_night_price'], $decimals);
                $result['service_fee_with_discount'] = round(($propertyFees['guest_service_charge'] / 100) * $result['total_night_price_after_discount'], $decimals);
                $result['flat_discount_expire_at'] = $property->property_discount?->end_date;
                $result['flat_discount_percent'] = $property->property_discount?->discount ?? 0;
                $result['host_fee_percent'] = $property->users->commission;

                $mabaat_service_charge = 0;
                if ($PropertyForNights->platform_id == 4) {
                    $result['iva_tax'] = round((15 / 100) * $result['total_night_price'], $decimals);
                    $prices = [1 => 199, 2 => 299, 3 => 399, 4 => 499, 5 => 599];
                    $bedrooms = $PropertyForNights->bedrooms;
                    // $mabaat_service_charge = $prices[$bedrooms];
                } else {

                    $result['iva_tax'] = round(($propertyFees['iva_tax'] / 100) * $result['total_night_price'], $decimals);
                }

                $result['accomodation_tax'] = round(($propertyFees['accomodation_tax'] / 100) * $result['total_night_price'], $decimals);
                $result['additional_guest'] = 0;
                $result['security_fee'] = 0;
                $result['cleaning_fee'] = 0;
                if ($guestCount > $property->property_price->guest_after) {
                    $additional_guest_count = $guestCount - $property->property_price->guest_after;
                    $result['additional_guest'] = $additional_guest_count * $property->property_price->guest_fee * $countDays;
                }

                /*if ($property->property_price->security_fee) {
                    $result['security_fee'] = $property->property_price->security_fee;
                    $result['service_fee_security'] = round(($propertyFees['guest_service_charge'] / 100) *
                        $result['security_fee'], $decimals);
                    $result['service_fee'] = $result['service_fee'] + $result['service_fee_security'];
                    $result['service_fee_with_discount'] = $result['service_fee_with_discount'] +
                        round(($propertyFees['guest_service_charge'] / 100) * $result['security_fee'], $decimals);
                }*/

                $result['silkhaus_markup'] = 0;
                if ($property->platform_id == 5) {
                    $result['silkhaus_markup'] = round(($propertyFees['silkhaus'] / 100) * $result['total_night_price'], $decimals);
                    $result['service_fee_silkhaus'] = round(($propertyFees['guest_service_charge'] / 100) * $result['silkhaus_markup'], $decimals);
                    $result['service_fee'] = $result['service_fee'] + $result['service_fee_silkhaus'];
                    $result['service_fee_with_discount'] = $result['service_fee_with_discount'] +
                        round(($propertyFees['guest_service_charge'] / 100) * $result['silkhaus_markup'], $decimals);
                }

                if ($property->property_price->cleaning_fee) {
                    $result['cleaning_fee'] = $property->property_price->cleaning_fee;
                    if ($property->platform_id == 6) {
                        if ($property->property_price->cleaning_fee_type == 'daily') {
                            $result['cleaning_fee'] = $property->property_price->cleaning_fee * $result['total_nights'];
                        }
                    }

                    // if($PropertyForNights->platform_id != 4){
                    $result['service_fee_cleaning'] = round(($propertyFees['guest_service_charge'] / 100) *
                        $result['cleaning_fee'], $decimals);
                    $result['service_fee'] = $result['service_fee'] + $result['service_fee_cleaning'];
                    $result['service_fee_with_discount'] = $result['service_fee_with_discount'] +
                        round(($propertyFees['guest_service_charge'] / 100) * $result['cleaning_fee'], $decimals);
                    // }
                }

                $CleaningSecurityTotalNightFee = $result['total_night_price'] + $result['cleaning_fee'] + $result['security_fee'];
                $result['host_fee'] = round(($property->users->commission / 100) * $CleaningSecurityTotalNightFee, $decimals);

                $tawunya_insurance = PropertyFees::where('field', 'insurance_fees_percentage')->first();

                if ($tawunya_insurance) {
                    $result['tawunya_insurance_fee'] = round(($tawunya_insurance->value / 100) * $CleaningSecurityTotalNightFee, $decimals);
                } else {
                    $result['tawunya_insurance_fee'] = 0;
                }

                $result['subtotal'] = $result['total_night_price'] + $result['additional_guest'];

                $saving = $result['total_night_price'] - $result['total_night_price_after_discount'];
                $result['yousaved'] = 0;

                $result['service_fee'] += $mabaat_service_charge;
                $result['service_fee_with_discount'] += $mabaat_service_charge;

                $result['total'] = $result['service_fee']
                    + $result['total_night_price'] + $result['additional_guest']
                    + $result['security_fee']
                    + $result['cleaning_fee']
                    + $result['iva_tax']
                    + $result['accomodation_tax']
                    + $result['silkhaus_markup'];

                $result['total_with_discount'] = $result['service_fee_with_discount']
                    + $result['total_night_price_after_discount'] + $result['additional_guest']
                    + $result['security_fee']
                    + $result['cleaning_fee']
                    + $result['iva_tax']
                    + $result['accomodation_tax']
                    + $result['silkhaus_markup'];

                if ($saving) {
                    $result['total'] = $result['total'] - $saving;
                }

                $result['yousaved'] = moneyFormat($monthly_or_weekly_discount, $symbol);
                $result['yousaved_without_symbol'] = $monthly_or_weekly_discount;

                $result['total_night_price_with_symbol'] = moneyFormat($symbol, numberFormat($result['total_night_price'], $decimals));

                $result['service_fee_with_symbol'] = moneyFormat($symbol, numberFormat($result['service_fee'], $decimals));
                $result['service_fee_with_discount_with_symbol'] = moneyFormat($symbol, numberFormat($result['service_fee_with_discount'], $decimals));
                $result['total_with_symbol'] = moneyFormat($symbol, numberFormat($result['total'], $decimals));
                $result['total_with_discount_with_symbol'] = moneyFormat($symbol, numberFormat($result['total_with_discount'], $decimals));
                $result['total_night_price_after_discount_with_symbol'] = moneyFormat($symbol, numberFormat($result['total_night_price_after_discount'], $decimals));
                $result['iva_tax_with_symbol'] = moneyFormat($symbol, numberFormat($result['iva_tax'], $decimals));
                $result['accomodation_tax_with_symbol'] = moneyFormat($symbol, numberFormat($result['accomodation_tax'], $decimals));
                $result['additional_guest_fee_with_symbol'] = moneyFormat($symbol, numberFormat($result['additional_guest'], $decimals));
                $result['security_fee_with_symbol'] = moneyFormat($symbol, numberFormat($result['security_fee'], $decimals));
                $result['cleaning_fee_with_symbol'] = moneyFormat($symbol, numberFormat($result['cleaning_fee'], $decimals));
                $result['per_night_price_with_symbol'] = moneyFormat($symbol, numberFormat($result['total_night_price'] / $countDays, $decimals));
                $result['discount_with_symbol'] = moneyFormat($symbol, numberFormat($result['discount'], 2));
                $result['currency'] = $this->getCurrentCurrencyCode();

                $result['per_night'] = $property->property_price->price;
                $result['service_fee_percent'] = +$propertyFees['guest_service_charge'];
                $result['guest_fee'] = $property->property_price->guest_fee;
                $currentAppVersion = getSetting('app_version');

                // Determine which method to call based on the app version
                if (version_compare($currentAppVersion, '2.0.0', '<')) {
                    $payment_gateway = 'moyasar';
                } else {
                    $payment_gateway = app('PAYMENT_METHOD');
                }
                $result['payment_getway'] = $payment_gateway;


                // New code for third-party listing taxes
                if ($property->thirdparty_listing && $property->platform_id == 5) {
                    $propertyPrice = PropertyPrice::where('property_id', $propertyId)->first();
                    if ($propertyPrice && $propertyPrice->taxes_json) {
                        $taxes = $propertyPrice->taxes_json;
                        // Check if $taxes is already an array
                        if (!is_array($taxes)) {
                            // If it's not an array, try to decode it
                            $taxes = json_decode($taxes, true);
                        }

                        $result['third_party_taxes'] = [];
                        $total_tax_amount = 0;
                        $markup = ($propertyFees['silkhaus'] / 100) * $result['total_night_price'];
                        $fareAccommodation = $result['total_night_price'] + $markup;
                        // Check if we now have a valid array
                        if (is_array($taxes) && isset($taxes['taxes'])) {
                            $taxes = $taxes['taxes']; // Now, $taxes is the actual array of taxe
                            foreach ($taxes as $tax) {
                                $tax_amount = 0;

                                if ($tax['units'] === 'PERCENTAGE') {
                                    if (in_array('AF', $tax['appliedOnFees'])) {
                                        $tax_amount += ($tax['amount'] / 100) * $fareAccommodation;
                                    }
                                    if (in_array('CF', $tax['appliedOnFees'])) {
                                        $tax_amount += ($tax['amount'] / 100) * $result['cleaning_fee'];
                                    }
                                } elseif ($tax['units'] === 'FIXED') {
                                    if ($tax['quantifier'] === 'PER_NIGHT') {
                                        $tax_amount = $tax['amount'] * $countDays;
                                    } elseif ($tax['quantifier'] === 'PER_STAY') {
                                        $tax_amount = $tax['amount'];
                                    }
                                }

                                $result['third_party_taxes'][] = [
                                    'name' => $tax['type'],
                                    'amount' => $tax_amount,
                                    'amount_with_symbol' => moneyFormat($symbol, numberFormat($tax_amount, $decimals))
                                ];
                                $total_tax_amount += $tax_amount;
                            }

                            $result['total_third_party_taxes'] = $total_tax_amount;
                            $result['total_third_party_taxes_with_symbol'] = moneyFormat($symbol, numberFormat($total_tax_amount, $decimals));

                            // Add third-party taxes to the total
                            $result['iva_tax'] += $total_tax_amount;
                            $result['iva_tax_with_symbol'] = moneyFormat($symbol, numberFormat($result['iva_tax'], $decimals));
                            $result['total'] += $total_tax_amount;
                            $result['total_with_discount'] += $total_tax_amount;
                            $result['total_with_symbol'] = moneyFormat($symbol, numberFormat($result['total'], $decimals));
                            $result['total_with_discount_with_symbol'] = moneyFormat($symbol, numberFormat($result['total_with_discount'], $decimals));
                        } else {
                            // Log an error if we couldn't get a valid array
                            FacadeLog::error('Invalid taxes_json format for property ID: ' . $propertyId);
                        }
                    }
                }

                return json_encode($result);
            } else {
                $result['status'] = 'nights become max';
                $result['max_nights'] = $PropertyForNights->max_nights;
                return json_encode($result);
            }
        }

        $result['status'] = 'nights become min';
        $result['min_nights'] = $PropertyForNights->min_nights;
        return json_encode($result);
    }


    public function getWeekendDiscount($propertymodel)
    {
        $weekendAmount = $propertymodel->property_price->weekend_price;
        if ($propertymodel->property_discount) {

            $weekendAmount = $propertymodel->property_price->weekend_price * $propertymodel->property_discount->discount / 100;
            $weekendAmount = $propertymodel->property_price->weekend_price - $weekendAmount;
        }
        return $weekendAmount;
    }


    // public function get_days($startDate, $endDate)
    // {
    //     $days[]     = $startDate;
    //     $startDate   = is_numeric($startDate) ? $startDate : strtotime($startDate);
    //     $endDate     = is_numeric($endDate) ? $endDate : strtotime($endDate);

    //     $startDate   = gmdate("Y-m-d", $startDate);
    //     $endDate     = gmdate("Y-m-d", $endDate);
    //     $currentDate = $startDate;
    //     while ($currentDate < $endDate) {
    //         $currentDate = gmdate("Y-m-d", strtotime("+1 day", strtotime($currentDate)));
    //         $days[]      = $currentDate;
    //     }
    //     return $days;
    // }

    public function get_days($startDate, $endDate)
    {
        $days[] = $startDate;
        $startDate = is_numeric($startDate) ? Carbon::createFromTimestamp($startDate) : Carbon::parse($startDate);
        $endDate = is_numeric($endDate) ? Carbon::createFromTimestamp($endDate) : Carbon::parse($endDate);
        $startDate = $startDate->format('Y-m-d');
        $endDate = $endDate->format('Y-m-d');
        $currentDate = Carbon::parse($startDate);
        // $currentDate = $startDate;
        while ($currentDate->lessThan($endDate)) {
            $currentDate = $currentDate->addDay();
            $days[] = $currentDate->format('Y-m-d');
        }
        return $days;
    }

    public function y_m_d_convert($date)
    {
        return date('Y-m-d', strtotime($date));
    }

    public function host_penalty_check($penalty, $booking_amount, $currency_code)
    {
        $penalty_id = '';
        $penalty_amnt = '';

        $penalty_sum = 0;
        if ($penalty->count() > 0) {

            $host_amount = $booking_amount;

            foreach ($penalty as $pen) {

                $host_amount = $this->convert_currency($currency_code, $pen->currency_code, $host_amount);

                $remaining_penalty = $pen->remaining_penalty;

                if ($host_amount > $remaining_penalty) {

                    $host_amount = $host_amount - $remaining_penalty;

                    $penalty = Penalty::find($pen->id);
                    $penalty->remaining_penalty = 0;
                    $penalty->status = "Completed";
                    $penalty->save();

                    $penalty_id .= $pen->id . ',';
                    $penalty_amnt .= $remaining_penalty . ',';
                    $penalty_sum += $remaining_penalty;
                } else {

                    $amount_reamining = $remaining_penalty - $host_amount;

                    $penalty = Penalty::find($pen->id);

                    $penalty->remaining_penalty = $amount_reamining;

                    $penalty->save();

                    $penalty_id .= $pen->id . ',';
                    $penalty_amnt .= $host_amount . ',';
                    $penalty_sum += $host_amount;
                    $host_amount = 0;
                }

                $host_amount = $this->convert_currency($pen->currency_code, $currency_code, $host_amount);
            }

            $penalty_amnt = rtrim($penalty_amnt, ',');
            $penalty_id = rtrim($penalty_id, ',');
        } else {
            $host_amount = $booking_amount;

            $penalty_id = 0;
            $penalty_amnt = '';
            $penalty_sum = 0;
        }

        $result['host_amount'] = $host_amount;
        $result['penalty_ids'] = $penalty_id;
        $result['penalty_total'] = $penalty_sum;
        $result['panalty_amounts'] = $penalty_amnt;

        return $result;
    }

    function randomCode($length = 20)
    {
        $var_num = 3;
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $num_set = '0123456789';
        $low_ch_set = 'abcdefghijklmnopqrstuvwxyz';
        $high_ch_set = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

        $randomString = '';

        $randomString .= $num_set[rand(0, strlen($num_set) - 1)];
        $randomString .= $low_ch_set[rand(0, strlen($low_ch_set) - 1)];
        $randomString .= $high_ch_set[rand(0, strlen($high_ch_set) - 1)];

        for ($i = 0; $i < $length - $var_num; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }

        $randomString = str_shuffle($randomString);

        return strtoUpper($randomString);
    }

    public static function dateRange($startDate, $endDate, $step = '+1 day', $format = 'Y-m-d')
    {
        $dates = [];
        $current = strtotime($startDate);
        $endDate = strtotime($endDate);
        if ($current > $endDate) {
            return $dates;
        }
        while ($current <= $endDate) {

            $dates[] = date($format, $current);
            $current = strtotime($step, $current);
        }
        return $dates;
    }

    public function uploadSingleFile($file, $path = 'public/uploads/')
    {
        $tmp_name = $file["tmp_name"];
        $name = str_replace(' ', '_', $file["name"]);
        $ext = pathinfo($name, PATHINFO_EXTENSION);
        $name = explode('.', $name)[0] . time() . '.' . $ext;
        try {
            if (!file_exists($path)) {
                mkdir($path, 0777, true);
            }
            move_uploaded_file($tmp_name, $path . $name);
            return $name;
        } catch (Exception $e) {
            return false;
        }
    }

    public static function CheckReview($propertyid)
    {
        $reviews = Reviews::where('property_id', $propertyid)->where('sender_id', Auth::id())->first();
        if ($reviews) {
            return true;
        } else {
            return false;
        }
    }

    public function GetRefundAmount($bookings, $interval)
    {

        $data = [
            'amount_to_refund' => 0,
            'status' => 0,
        ];
        $Currentdate = date("H:i:s");
        $propertyhost = $bookings->properties->users;
        $property = $bookings->properties;
        $bookingHours = $bookings->created_at->diffInHours(Carbon::now());
        if ($property->cancellation == 'Flexible') {
            if ($interval == '0') {
                $data['amount_to_refund'] = $bookings->base_price;
                $data['status'] = 1;
            }
        }
        if ($property->cancellation == 'Moderate') {
            if ($interval == '4') {
                $data['amount_to_refund'] = $bookings->base_price;
                $data['status'] = 1;
            }
        }
        if ($property->cancellation == 'Firm') {
            if ($interval >= '29') {
                $data['amount_to_refund'] = $bookings->base_price;
                $data['status'] = 1;
            } else if ($interval >= '13' && $bookingHours <= '48') {
                $data['amount_to_refund'] = $bookings->base_price;
                $data['status'] = 1;
            } else if ($interval > "6" && $interval < "13") {
                // REFUND 50%
                $data['amount_to_refund'] = $bookings->base_price * 0.50;
                $data['status'] = 1;
            } else {
                $data['msg'] = "No Refund Upto 7 Days Before Check in";
                $data['status'] = 0;
            }
        }
        if ($property->cancellation == 'Strict') {
            if ($interval >= '13' && $bookingHours <= '48') {
                $data['amount_to_refund'] = $bookings->base_price;
                $data['status'] = 1;
            }
            if ($interval >= '6') {
                $data['amount_to_refund'] = 0.50 * $bookings->base_price;
                $data['status'] = 1;
            }
            if ($interval < '6') {
                $data['msg'] = "No Refund Upto 7 Days Before Check in";
                $data['status'] = 0;
            }
        }
        return $data;
    }

    public function refundAmount($booking, $amount_to_refund)
    {
        $refund = new Refund();
        $refund->user_id = $booking->user_id;
        $refund->host_id = $booking->host_id;
        $refund->booking_id = $booking->id;
        $refund->amount = $amount_to_refund;
        $refund->status = 'Cancelled';
        $refund->save();
    }

    function callAPI($endpointURL, $myfatoorah_apikey, $postFields = [], $requestType = 'POST')
    {

        // FacadesLog::info($endpointURL);
        // FacadesLog::info($myfatoorah_apikey);
        // FacadesLog::info($postFields);
        // FacadesLog::info($requestType);
        $curl = curl_init($endpointURL);
        curl_setopt_array($curl, [
            CURLOPT_CUSTOMREQUEST => $requestType,
            CURLOPT_POSTFIELDS => json_encode($postFields),
            CURLOPT_HTTPHEADER => ["Authorization: Bearer $myfatoorah_apikey", 'Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
        ]);


        $response = curl_exec($curl);
        $curlErr = curl_error($curl);

        curl_close($curl);

        if ($curlErr) {
            //     ApiLogs::create([
            //        'user_id'  => null,
            //        'response' => $curlErr,
            //        'endpoint' => 'myFatoorah',
            //        'type'     => 'api'
            //    ]);
        }

        if ($curlErr) {
            //Curl is not working in your server
            die("Curl Error: $curlErr");
        }

        $error = $this->handleError($response);
        if ($error) {
            // die("Error: $error");
            return $error;
        }

        if ($error) {
            //     ApiLogs::create([
            //        'user_id'  => null,
            //        'response' => $error,
            //        'endpoint' => 'myFatoorah',
            //        'type'     => 'api'
            //    ]);
        }
        // if($v==1){
        //     dd($response);
        // }

        // FacadesLog::info($response);
        $json = json_decode($response);
        if ($response) {
            //     ApiLogs::create([
            //        'user_id'  => null,
            //        'response' => $response,
            //        'endpoint' => 'myFatoorah',
            //        'type'     => 'api'
            //    ]);
        }
        return $json->Data;
    }

    function createQuotationRequest($booking)
    {


        $createQoutationEndpointURL = env('TAWUNIYA_BASE_URL') . 'createQuotation';
        $twanapikey = env('TAWUNIYA_API_KEY');
        // dd((string) $booking->base_price_with_or_without_discount, (string) $booking->per_night);
        FacadesLog::debug('twanapikey', [$twanapikey]);
        FacadesLog::debug('createQoutationEndpointURL', [$createQoutationEndpointURL]);

        $postFields = [
            'createQuotationRequest' => [
                'bookingID' => $booking->id,
                'policyNumber' => env('TAWUNIYA_POLICY_NUMBER'),   //  old policy number //'202200662',
                'bookingDate' => Carbon::parse($booking->created_at)->format('Y-m-d H:i:s'),
                "propertyCatagory" => $booking->properties->propertyType->twaniya_property_type_category_id,
                'propertyTypeCode' => $booking->properties->propertyType->twaniya_property_type_code,
                'unitCode' => $booking->properties->property_code,
                'propertySumInsured' => $booking->base_price_with_or_without_discount,
                'rentPerDay' => $booking->per_night,
                'duration' => $booking->total_night,
                'propertyAddress' => (string)$booking->properties->property_address->city,
                'accommodationNum' => $booking->code,
                'noOfBedrooms' => $booking->properties->beds,
                'checkInDate' => Carbon::parse($booking->start_date)->format('Y-m-d H:i:s'),
                'guestDetails' => [
                    'Name' => $booking->users->first_name . ' ' . $booking->users->last_name,
                    'Email' => $booking->users->email ?? "<EMAIL>",
                    'phoneNum' => (string)$booking->users->formatted_phone,
                    'IDNum' => $booking->users->elmDocument?->code ?? null,
                ],
                'ownerDetails' => [
                    'Name' => $booking->host->first_name . ' ' . $booking->host->last_name,
                    'Email' => $booking->host->email ?? "<EMAIL>",
                    'phoneNum' => (string)$booking->host->formatted_phone,
                    'IDNum' => $booking->host->elmDocument?->code ?? null,
                ],
                'createdBy' => 'Darent',
                'comments' => 'This is new booking',
            ],
        ];


        $curl = curl_init($createQoutationEndpointURL);
        curl_setopt_array($curl, [
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postFields),
            CURLOPT_HTTPHEADER => ["twanapikey: $twanapikey", 'Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
        ]);

        $response = curl_exec($curl);
        curl_close($curl);
        $json = json_decode($response);
        FacadesLog::debug('json', [$json]);


        if ($json->resultCode === "F") {
            $tawuniyaRecord = Tawuniya::updateOrCreate(
                ['booking_id' => $booking->id], // Condition to check for existing record
                ['create_qoutation_failure_response' => json_encode($json)] // Attributes to set if the record does not exist
            );
        } else {
            $tawuniyaRecord = Tawuniya::updateOrCreate(
                ['booking_id' => $booking->id], // Condition to check for existing record
                [
                    'create_qoutation_payload' => json_encode($json),
                    'booking_payload' => json_encode($postFields),
                    'create_qoutation_status' => true
                ] // Attributes to set if the record does not exist
            );
            $booking->is_quotation_created = true;
            $booking->save();
        }

        return $json;
    }

    /**
     * Activates a certificate for a given booking.
     *
     * @param mixed $booking The booking object.
     * @return mixed The JSON response from the activation request.
     * @throws Exception If there is an error during the activation process.
     */
    function activateCertificate($booking)
    {
        $activateCertificateEndpointURL = env('TAWUNIYA_BASE_URL') . 'activateCertificate';
        $twanapikey = env('TAWUNIYA_API_KEY');
        $policyNum = env('TAWUNIYA_POLICY_NUMBER');

        $tawuniyaQoutationPayload = json_decode($booking->tawuniya->create_qoutation_payload);

        $postFields = [
            'activatePolicyRequest' => [
                'policyNum' => $policyNum,
                'certificateNum' => $tawuniyaQoutationPayload->certificateNum,
                'bookingId' => $booking->id,
                'startDateTime' => $booking->start_date,
                'endDateTime' => $booking->end_date,
            ],
        ];

        $curl = curl_init($activateCertificateEndpointURL);
        curl_setopt_array($curl, [
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postFields),
            CURLOPT_HTTPHEADER => ["twanapikey: $twanapikey", 'Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
        ]);

        $response = curl_exec($curl);
        curl_close($curl);


        $json = json_decode($response);
        FacadesLog::debug('activateCertificate response', [$json]);


        if ($json->resultCode === "F") {
            Tawuniya::updateOrCreate(
                ['booking_id' => $booking->id],
                ['activate_certificate_failure_response' => json_encode($json)]
            );
        } else {
            Tawuniya::updateOrCreate(
                ['booking_id' => $booking->id],
                ['certificate_activation_payload' => json_encode($json)]
            );

            $booking->is_certificate_activated = true;
            $booking->save();
        }

        return $json;
    }


    function getPriceTawuniya($booking)
    {
        $getPriceEndpointURL = env('TAWUNIYA_BASE_URL') . 'getPrice';
        $twanapikey = env('TAWUNIYA_API_KEY');

        $getPricePayload = [
            'getPriceRequest' => [
                'propertyType' => (string)$booking->properties->propertyType->twaniya_property_type_category_id,
                'rentPerDay' => (string)$booking->per_night,
                'duration' => (string)$booking->total_night,
            ],
        ];

        $curl = curl_init($getPriceEndpointURL);
        curl_setopt_array($curl, [
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($getPricePayload),
            CURLOPT_HTTPHEADER => ["twanapikey: $twanapikey", 'Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
        ]);

        $response = curl_exec($curl);
        curl_close($curl);

        $json = json_decode($response);
        // Insertion in Tawuniya table
        // if($json->getPriceResponse->resultCode === "F"){
        //     $tawuniyaRecord = Tawuniya::updateOrCreate(
        //         ['booking_id' => $booking->id], // Condition to check for existing record
        //         ['failure_response' => json_encode($json)] // Attributes to set if the record does not exist
        //     );
        // }else{
        //     $tawuniyaRecord = Tawuniya::updateOrCreate(
        //         ['booking_id' => $booking->id], // Condition to check for existing record
        //         ['get_price_payload' => json_encode($json)] // Attributes to set if the record does not exist
        //     );
        // }
        return $json;
    }

    function cancelPolicyTawuniya($booking)
    {
        $getPriceEndpointURL = env('TAWUNIYA_BASE_URL') . 'cancelPolicy';
        $twanapikey = env('TAWUNIYA_API_KEY');

        if ($booking->tawuniya != null && $booking->tawuniya->create_qoutation_payload != null) {
            $tawuniyaQoutationPayload = json_decode($booking->tawuniya->create_qoutation_payload);
            $tawuniyaQoutationPayload = $tawuniyaQoutationPayload->createQuotationResponse;

            $getPricePayload = [
                'cancelPolicyRequest' => [
                    'certificateNum' => $tawuniyaQoutationPayload->certificateNum,
                    'policyNum' => env('TAWUNIYA_POLICY_NUMBER'),
                    'darentBookingId' => $booking->id,
                    'cancelReason' => "1",
                ],
            ];

            $curl = curl_init($getPriceEndpointURL);
            curl_setopt_array($curl, [
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($getPricePayload),
                CURLOPT_HTTPHEADER => ["twanapikey: $twanapikey", 'Content-Type: application/json'],
                CURLOPT_RETURNTRANSFER => true,
            ]);

            $response = curl_exec($curl);
            curl_close($curl);

            $json = json_decode($response);
            // Insertion in Tawuniya table
            if ($json->resultCode === "F") {
                $tawuniyaRecord = Tawuniya::updateOrCreate(
                    ['booking_id' => $booking->id], // Condition to check for existing record
                    ['cancel_policy_failure_response' => json_encode($json)] // Attributes to set if the record does not exist
                );
            } else {
                $tawuniyaRecord = Tawuniya::updateOrCreate(
                    ['booking_id' => $booking->id], // Condition to check for existing record
                    ['cancel_policy_payload' => json_encode($json)] // Attributes to set if the record does not exist
                );
            }
            return $json;
        } else {
            return "Does Not Cancelled";
        }
    }

    function callAPI2($endpointURL, $myfatoorah_apikey, $postFields = [], $requestType = 'POST')
    {


        $curl = curl_init($endpointURL);
        curl_setopt_array($curl, [
            CURLOPT_CUSTOMREQUEST => $requestType,
            CURLOPT_POSTFIELDS => json_encode($postFields),
            CURLOPT_HTTPHEADER => ["Authorization: Bearer $myfatoorah_apikey", 'Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
        ]);

        $response = curl_exec($curl);
        $curlErr = curl_error($curl);

        curl_close($curl);

        if ($curlErr) {
            //Curl is not working in your server
            die("Curl Error: $curlErr");
        }

        $error = $this->handleError2($response);
        if ($error) {
            // die("Error: $error");
            return $error;
        }

        // return json_decode($response);

        $json = json_decode($response);
        return ["IsSuccess" => $json->IsSuccess, "response" => $response];
    }

    function handleError($response)
    {

        $json = json_decode($response);
        if (isset($json->IsSuccess) && $json->IsSuccess == true) {
            return null;
        }

        //Check for the errors
        if (isset($json->ValidationErrors) || isset($json->FieldsErrors)) {
            $errorsObj = isset($json->ValidationErrors) ? $json->ValidationErrors : $json->FieldsErrors;
            $blogDatas = array_column($errorsObj, 'Error', 'Name');

            $error = implode(', ', array_map(function ($k, $v) {
                return "$k: $v";
            }, array_keys($blogDatas), array_values($blogDatas)));
        } else if (isset($json->Data->ErrorMessage)) {
            $error = $json->Data->ErrorMessage;
        }

        if (empty($error)) {
            $error = (isset($json->Message)) ? $json->Message : (!empty($response) ? $response : 'API key or API URL is not correct');
        }

        return $error;
    }

    function handleError2($response)
    {

        $json = json_decode($response);
        if (isset($json->IsSuccess) && $json->IsSuccess == true) {
            return null;
        }

        //Check for the errors
        if (isset($json->ValidationErrors) || isset($json->FieldsErrors)) {
            $errorsObj = isset($json->ValidationErrors) ? $json->ValidationErrors : $json->FieldsErrors;
            $blogDatas = array_column($errorsObj, 'Error', 'Name');

            $error = implode(', ', array_map(function ($k, $v) {
                return "$k: $v";
            }, array_keys($blogDatas), array_values($blogDatas)));
        } else if (isset($json->Data->ErrorMessage)) {
            $error = $json->Data->ErrorMessage;
        }

        if (empty($error)) {
            $error = (isset($json->Message)) ? $json->Message : (!empty($response) ? $response : 'API key or API URL is not correct');
        }

        return ["IsSuccess" => false, "response" => $error];
    }

    function sendPushNotification($token, $body, $data = null)
    {

        $title = 'Darent | دارينت';

        $fcm = FCMService::send(
            $token,
            [
                'title' => $title,
                'body' => $body,
                // "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                // 'sound'        => "cute_notification.mp3",
                // "alert"        => "New",
            ],
            $data
        );

        FacadesLog::debug('data', [$fcm]);
    }

    public function checkEmailAvailibility($email)
    {
        $msg = "";
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $msg = "Invalid Format";
            return apiResponse('Failure', $msg, 422);
            // return ['status'=>true,'msg'=>$msg];
        }
        $checkUserEmail = User::where('email', $email)->where('is_email_verified', 1)->first();
        if ($checkUserEmail) {
            $msg = "Already Exist";
            return apiResponse('Failure', $msg, 422);

            // return ['status'=>true,'msg'=>$msg];
        }
        $msg = "Success";
        return apiResponse($msg, 'Success', 200);
    }

    public function checkPhoneNumberAvailability($phone)
    {
        $msg = "";
        if (strtolower(env('APP_ENV')) == 'prod') {
            // Regex pattern for +966XXXXXXXXX format
            $regexPattern = "/^\+966\d{9}$/";
        } else {
            // Regex pattern for +92XXXXXXXXXX format
            $regexPattern = "/^\+92\d{10}$/";
        }

        if (!preg_match($regexPattern, $phone)) {

            $msg = "Invalid Format";
            return apiResponse('Failure', $msg, 422);
        }

        $checkPhone = User::where('formatted_phone', $phone)->first();
        if (isset($checkPhone)) {
            $msg = "Phone Number Already Exist";
            return apiResponse("Failure", $msg, 422);
        }
        $msg = "Success";
        return apiResponse($msg, 'Success', 200);
    }

    public function getLogs($model, $guard, $storedPermissions = null, $requestPermissions = null)
    {
        return; // Exit early if $model is null
        if ($model === null) {
        }
        if (in_array(get_class($model), [
            'App\Model\Properties',
            'App\Model\PropertyDescription',
            'App\Model\PropertyPrice',
            'App\Model\PropertyAddress',
            'App\Model\CustomPricing',
            'App\Model\PropertySteps',
            'App\Model\PropertyDates',
            'App\Model\PropertyPhotos',
        ])) {
            return;
        }
        return;
        $oldData = $model->getOriginal();
        $newData = $model->getAttributes();
        // isset($storedPermissions) ? $oldData['storedPermissions'] = $storedPermissions : '';
        // isset($storedPermissions) ? $newData['storedPermissions'] = $requestPermissions : '';


        $changedData = [];

        if (!empty($oldData) && count($oldData) > 0) {
            foreach ($newData as $key => $value) {
                if ($oldData[$key] != $value) {
                    $changedData[$key] = $value;
                }
            }
            $changedData["change_from"] = $guard;
        } else {
            $changedData = $newData;
        }

        $user_id = auth()->guard($guard)->id();
        $user_name = auth()->guard($guard)->user()->first_name . ' ' . auth()->guard($guard)->user()->last_name;
        if ($guard == "admin") {
            $user_name = auth()->guard($guard)->user()->username;
        }
        // dd($user_name);

        $log = new Log();
        $log->user_id = $user_id;
        $log->change_by = $user_name;
        $log->old = json_encode($oldData);
        $log->new = json_encode($newData);
        $log->change_values = json_encode($changedData);
        $model->logs()->save($log);

        // $log = $model->logs()->create([
        //     'user_id' => $user_id,
        //     'change_by' => $user_name,
        //     'old' => json_encode($oldData),
        //     'new' => json_encode($newData),
        //     'change_values' => json_encode($changedData)
        // ]);
    }


    public function getPermissionLogs($model, $guard, $storedPermissions = null, $requestPermissions = null)
    {
        $oldData = $model->getOriginal();
        $newData = $model->getAttributes();
        isset($storedPermissions) ? $oldData['storedPermissions'] = $storedPermissions : '';
        isset($storedPermissions) ? $newData['storedPermissions'] = $requestPermissions : '';


        $changedData = [];

        if (!empty($oldData) && count($oldData) > 0) {
            foreach ($newData as $key => $value) {
                if ($oldData[$key] != $value) {
                    $changedData[$key] = $value;
                }
            }
            $changedData["change_from"] = $guard;
        } else {
            $changedData = $newData;
        }

        $user_id = auth()->guard($guard)->id();
        $user_name = auth()->guard($guard)->user()->first_name . ' ' . auth()->guard($guard)->user()->last_name;
        if ($guard == "admin") {
            $user_name = auth()->guard($guard)->user()->username;
        }
        // dd($user_name);

        $log = new Log();
        $log->user_id = $user_id;
        $log->change_by = $user_name;
        $log->old = json_encode($oldData);
        $log->new = json_encode($newData);
        $log->change_values = json_encode($changedData);
        $model->logs()->save($log);

        // $log = $model->logs()->create([
        //     'user_id' => $user_id,
        //     'change_by' => $user_name,
        //     'old' => json_encode($oldData),
        //     'new' => json_encode($newData),
        //     'change_values' => json_encode($changedData)
        // ]);
    }

    public function iCalDecoder($file)
    {
        $ical = file_get_contents($file);
        dd($ical);
        preg_match_all('/(BEGIN:VEVENT.*?END:VEVENT)/si', $ical, $result, PREG_PATTERN_ORDER);
        foreach ($result as $element) {
            $element = str_replace("\r\n", "\n", $element);
        }


        for ($i = 0; $i < count($element); $i++) {

            $tmpbyline = explode("\n", $element[$i]);


            foreach ($tmpbyline as $item) {
                $tmpholderarray = explode(":", $item);
                if (count($tmpholderarray) > 1) {
                    $majorarray[$tmpholderarray[0]] = $tmpholderarray[1];
                }
            }

            if (preg_match('/DESCRIPTION:(.*)END:VEVENT/si', $result[0][$i], $regs)) {
                $majorarray['DESCRIPTION'] = str_replace("  ", " ", str_replace("\r\n", "", $regs[1]));
            }
            $icalarray[] = $majorarray;
            unset($majorarray);
        }

        return $icalarray;
    }

    public function trackProductView($property_id,$device = DESKTOP_APP_KEY_NAME,$token = null)
    {
        $sessionId = FacadesSession::getId();
        $guestUuid = request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null;
        $user_id = null;
        $checkin = request()->get('checkin') ?? null;
        $checkout = request()->get('checkout') ?? null;
        $referrer = request()->headers->get('referer');
        if(auth()->check()){
            $user_id = auth()->user()->id;
        }
        UserPropertyView::updateorcreate(
            ['property_id' => $property_id, 'sessionId' => $sessionId, 'guest_uuid' => $guestUuid, 'device' => $device, 'api_token' => $token, 'user_id' => $user_id, 'referer_url' => $referrer, 'checkin' => $checkin , 'checkout'=> $checkout],
        );
    }

    public function updateProductView($request){
        try{
            $sessionId = FacadesSession::getId();
            $guestUuid = request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null;
            $referrer = $request->header('referer');
            $view = UserPropertyView::where('guest_uuid',$guestUuid)->where('property_id',$request->property_id)->latest()->first();
            if(empty($view)){
                $view = new UserPropertyView;
                $view->sessionId = $sessionId;
                $view->guestUuid = $guestUuid;
                $view->device = DESKTOP_APP_KEY_NAME;
                $view->referer_url = $referrer;
            }
            $view->checkin = !empty($request->checkin) ? $request->checkin : $view->checkin;
            $view->checkout = !empty($request->checkout) ? $request->checkout : $view->checkout;
            $view->clicked_booking = !empty($request->clicked) ? 1 : $view->clicked_booking;
            $view->save();
            return true;
        } catch(\Exception $e){
            return false;
        }
    }

    public static function formatProductViewCount($count)
    {
        $suffixes = ['+', 'k', 'M', 'B', 'T'];
        $suffixIndex = 0;

        while ($count >= 1000 && $suffixIndex < count($suffixes) - 1) {
            $count /= 1000;
            $suffixIndex++;
        }

        return round($count, 1) . $suffixes[$suffixIndex];
    }

    public function isRequestFromMobile(Request $request)
    {
        $userAgent = $request->header('User-Agent');

        return (strpos($userAgent, 'Mobile') !== false);
    }

    public function checkGuestStatus($hostId)
    {
        $bookingCount = Bookings::where('host_id', $hostId)->where("status", "Accepted")->count();
        return $bookingCount;
    }

    public function createOrUpdateChatHead($guestId, $hostId, $propertyId, $message = 'hello')
    {
        $chat = PropertyChatHead::firstOrCreate(['property_id' => $propertyId, 'guest_id' => $guestId, 'host_id' => $hostId]);
        if ($chat->wasRecentlyCreated) {
            $chat->propertyChats()->create([
                'message' => $message,
                'sender_id' => $guestId,
                'type' => PropertyChatTypeEnum::Text->value,
            ]);
        }
        return $chat->id;
    }

    public function calculateNearbyCoordinates($latitude, $longitude, $distance, $bearing)
    {
        // Earth's radius in kilometers (mean radius)
        $earthRadius = 6371;

        // Convert latitude and longitude from degrees to radians
        $lat = deg2rad($latitude);
        $lon = deg2rad($longitude);

        // Convert bearing from degrees to radians
        $bearing = deg2rad($bearing);

        // Calculate the new latitude
        $newLat = asin(sin($lat) * cos($distance / $earthRadius) + cos($lat) * sin($distance / $earthRadius) * cos($bearing));

        // Calculate the new longitude
        $newLon = $lon + atan2(sin($bearing) * sin($distance / $earthRadius) * cos($lat), cos($distance / $earthRadius) - sin($lat) * sin($newLat));

        // Convert new latitude and longitude from radians to degrees
        $newLat = rad2deg($newLat);
        $newLon = rad2deg($newLon);

        return ['latitude' => $newLat, 'longitude' => $newLon];
    }

    public function createHostPerformanceScore($host_id, $payload)
    {
        $hostPerformanceScoreObj = new HostPerformanceScore();
        $hostScoreExist = $hostPerformanceScoreObj::where('host_id', $host_id)->first();
        if ($hostScoreExist) {
            $hostPerformanceScoreObj::where('host_id', $host_id)->update($payload);
        } else {
            $payload['host_id'] = $host_id;
            $hostPerformanceScoreObj::create($payload);
        }

        return $payload;
    }

    public function createBookingPaymentDetail($booking, $bookingdetails, $promo_host_discount, $promo_darent_discount)
    {
        // Check if a booking payment detail with the same booking_id already exists
        $existingDetail = BookingPaymentDetails::where('booking_id', $booking->id)->first();

        if ($existingDetail) {
            // If the record exists, do not create a new one
            return;
        }

        BookingPaymentDetails::create([
            'booking_id' => $booking->id,
            'guest_id' => $booking->user_id,
            'host_id' => $bookingdetails['hostid'],
            'payment_key' => 'PaymentId',
            'payment_value' => 'TABBY ' . $booking->id,
            'guest_paid' => $bookingdetails['guestpaid'],
            'vat' => $bookingdetails['VAT'],
            'commission' => $bookingdetails['commission'],
            'commission_in_percenatge' => $bookingdetails['commission_in_percenatge'],
            'security_deposit' => $bookingdetails['security_deposit'],
            'host_pay' => $bookingdetails['host_pay'],
            'host_discount' => $promo_host_discount,
            'darent_discount' => $promo_darent_discount,
        ]);
    }

    protected function createPaymentResponse($request, $booking_id)
    {
        $paymentResponse = new PaymentResponse();
        $paymentResponse->payment_type = $paymentResponse::SPLIT_PAYMENT;
        $paymentResponse->booking_id = $booking_id;
        $paymentResponse->response = json_encode([
            "ALL" => $request->all() ?? [],
            "REQUEST" => $request,
            "HEADERS" => $request->headers->all() ?? [],
        ]);
        $paymentResponse->save();

        return true;
    }


    public function payByWallet($finalamount, $booking)
    {
        $walletService = new WalletService();
        $newUserWallet = NewUserWallet::where('user_id', FacadesAuth::id())->first();

        if ($finalamount > $newUserWallet->balance) {
            $finalamount = $finalamount - $newUserWallet->balance;
            $walletBalance = 0;
            $walletData = ['finalamount' => $finalamount, 'walletBalance' => $walletBalance, 'webhookDeduction' => true];


            Bookings::find($booking->id)->update([
                'pay_by_wallet' => $newUserWallet->balance != 0 ? $newUserWallet->balance : null,
                'pay_by_card' => $finalamount,
            ]);
        } elseif ($finalamount < $newUserWallet->balance) {
            $walletBalance = $newUserWallet->balance - $finalamount;
            $walletService->calculateCashBack($booking->user_id, $finalamount);
            Bookings::find($booking->id)->update(['pay_by_wallet' => $finalamount]);

            $promo_host_discount = 0;
            $promo_darent_discount = 0;
            $promocodeCodeUsage = PromoCodeUsage::where('booking_id', $booking->id)->first();
            if (isset($promocodeCodeUsage)) {
                $createdBy = $promocodeCodeUsage->promoCode->created_by;
                if ($createdBy == "User") {
                    $promo_host_discount = $promocodeCodeUsage->discount_value;
                } elseif ($createdBy == "Admin") {
                    $promo_darent_discount = $promocodeCodeUsage->discount_value;
                }
            }

            $bookingdetails = $this->payableToHost($booking);


            // $this->createBookingPaymentDetail($booking, $bookingdetails, $promo_host_discount, $promo_darent_discount);
            $createpaymentResponse = $this->createPaymentResponse(request(), $booking->id);


            $transaction_data = [
                'payment_type_id' => PaymentTypeEnum::Wallet,
                'transaction_type_id' => TransactionTypes::DEBIT,
                'transaction_category_id' => TransactionCategories::BOOKING_CONFIRMATION,
                'user_id' => $booking->user_id,
                'record_id' => $booking->id,
                'type' => 'booking',
                // 'user_id' => $booking->user_id,
                'amount' => $finalamount,
                'ref_json' => json_encode(['booking_id' => $booking->id]),
                'booking_id' => $booking->id,
                'wallet_id' => $newUserWallet->id,
            ];
            Transactions::create($transaction_data);

            $finalamount = 0;
            $this->webhoookBehaviour($booking, 1);
            $newUserWallet->balance = $walletBalance;
            $newUserWallet->save();

            $walletData = ['finalamount' => $finalamount, 'walletBalance' => $walletBalance, 'webhookDeduction' => false];
        } else {
            Bookings::find($booking->id)->update(['pay_by_wallet' => $finalamount]);
            $walletService->calculateCashBack($booking->user_id, $finalamount);
            $transaction_data = [
                'payment_type_id' => PaymentTypeEnum::Wallet,
                'transaction_type_id' => TransactionTypes::DEBIT,
                'transaction_category_id' => TransactionCategories::BOOKING_CONFIRMATION,
                'user_id' => $booking->user_id,
                // 'user_id' => $booking->user_id,
                'record_id' => $booking->id,
                'type' => 'booking',
                'amount' => $finalamount,
                'ref_json' => json_encode(['booking_id' => $booking->id]),
                'booking_id' => $booking->id,
                'wallet_id' => $newUserWallet->id,
            ];
            Transactions::create($transaction_data);

            $finalamount = 0;
            $walletBalance = 0;
            $this->webhoookBehaviour($booking);
            $newUserWallet->balance = $walletBalance;
            $newUserWallet->save();
            $walletData = ['finalamount' => (float)$finalamount, 'walletBalance' => $walletBalance, 'webhookDeduction' => false];
        }
        return $walletData;
    }

    public function payByWalletWebhook($booking)
    {
        $newUserWallet = NewUserWallet::where('user_id', $booking->user_id)->first();
        $finalamount = $booking->total_with_discount ? $booking->total_with_discount : $booking->total;
        $userWalletBalance = $newUserWallet->balance;

        $new_wallet_balance = 0;
        $transaction_amount = 0;
        $payByCard = 0;

        if ($finalamount > $userWalletBalance) {
            $transaction_amount = $userWalletBalance;
            $payByCard = $finalamount - $userWalletBalance;
        } else {
            $transaction_amount = $finalamount;
            $new_wallet_balance = $userWalletBalance - $finalamount;
        }

        Bookings::find($booking->id)->update([
            'pay_by_wallet' => $transaction_amount,
            'pay_by_card' => $payByCard
        ]);

        // Debit
        $transaction_data = [
            'payment_type_id' => PaymentTypeEnum::Wallet,
            'transaction_type_id' => TransactionTypes::DEBIT,
            'transaction_category_id' => TransactionCategories::BOOKING_CONFIRMATION,
            'user_id' => $booking->user_id,
            'amount' => $transaction_amount,
            'ref_json' => json_encode(['booking' => $booking]),
            'booking_id' => $booking->id,
            'wallet_id' => $newUserWallet,
        ];

        $transaction_crated = Transactions::create($transaction_data);

        NewUserWallet::where('user_id', $booking->user_id)->update(['balance' => $new_wallet_balance]);
        $walletData = ['finalamount' => $finalamount, 'walletBalance' => $new_wallet_balance, 'webhookDeduction' => true];
        FacadesLog::debug('walletData', [$walletData]);
        return $walletData;
    }

    public function webhoookBehaviour($booking, $completeWalletPayment = 0)
    {
        $bookingModal = new Bookings();
        $amount = $booking->total_with_discount ? $booking->total_with_discount : $booking->total; // WILL BE USED AFTER INTEGRATION OF NEW TRANSACTION STRUCTURE
        $invoice_id = rand(10000000, 99999999) . Bookings::WALLET_INVOICE_SUFFIX; // WILL BE USED AFTER INTEGRATION OF NEW TRANSACTION STRUCTURE
        $cus_userid = $booking->user_id; // WILL BE USED AFTER INTEGRATION OF NEW TRANSACTION STRUCTURE
        $bookingid = $booking->id;

        $bookingModal::where('id', $bookingid)->update(['status' => "Accepted", "is_payment" => 1, 'payment_type_id' => PaymentTypeEnum::Wallet]);

        BookingStatusLog::updateOrCreate(
            ['booking_id' => $bookingid],
            ['status' => "Accepted", 'changed_by' => FacadesAuth::id()]
        );
        $booking = $bookingModal::with('properties')->where('id', $bookingid)->first();

        $price_list = json_decode($this->getPrice($booking->property_id, $booking->start_date, $booking->end_date, $booking->guest, booking_id: $booking->id));

        $codeUsage = PromoCodeUsage::where(['booking_id' => $booking->id])->orderByDesc('id')->first();
        if ($codeUsage && !!$booking->total_with_discount) {
            $codeUsage->update(['is_used' => 1]);
        }

        if (isset($price_list->date_with_price)) {
            foreach ($price_list->date_with_price as $dp) {
                $tmp_date = setDateForDb($dp->date);
                $property_id = $booking->property_id;
                $property_data = [
                    'property_id' => $property_id,
                    'booking_id' => $booking->id,
                    'status' => 'Not available',
                    'price' => 0,
                    'date' => $tmp_date,
                ];
                $insertedDates = PropertyDates::updateOrCreate(['booking_id' => $booking->id,'property_id' => $property_id, 'date' => $tmp_date], $property_data);
            }
        }
        $guest = User::find($booking->user_id);
        $property = Properties::find($booking->property_id);
        FacadesNotification::send($guest, new UserNotify(
            'guest.booking.accepted.guest',
            url('guest/reservation/' . $booking->code . '?booking=incoming'),
            data: ['slug' => 'reservation']
        ));

        $is_elm_verified = ElmDocument::where('user_id', $booking->user_id)->where('verified_till', '>=', Carbon::now()->format('Y-m-d'))->first();
        $updateYaqeen = Bookings::where('id', $booking->id)->first();
        if (isset($is_elm_verified)) {
            $updateYaqeen->is_yaqeen_verified = '1';
        } else {
            $updateYaqeen->is_yaqeen_verified = '0';
        }
        $updateYaqeen->save();

        $notifyhostReservation = User::find($booking->host_id);

        FacadesNotification::send($notifyhostReservation, new UserNotify(
            'guest.booking.accepted.host',
            route('managehost.all_reservation'),
            [':property' => $property->property_code],
            ['slug' => 'booking']
        ));

        $bookingdetails = $this->payableToHost($booking);

        if (isset($booking->promo_code_usage_id)) {
            if ($codeUsage && !!$booking->total_with_discount) {
                $codeUsage->update(['is_used' => 1]);
            }
        }

        $promo_host_discount = 0;
        $promo_darent_discount = 0;
        $promocodeCodeUsage = PromoCodeUsage::where('booking_id', $bookingid)->first();

        if (isset($promocodeCodeUsage)) {
            $createdBy = $promocodeCodeUsage->promoCode->created_by;
            if ($createdBy == "User") {
                $promo_host_discount = $promocodeCodeUsage->discount_value;
            } elseif ($createdBy == "Admin") {
                $promo_darent_discount = $promocodeCodeUsage->discount_value;
            }
        }

        BookingPaymentDetails::create([
            'booking_id' => $bookingid,
            'guest_id' => $booking->user_id,
            'host_id' => $bookingdetails['hostid'],
            'payment_key' => 'PaymentId',
            'payment_value' => $completeWalletPayment == 1 ? "wallet payment" : null,
            'guest_paid' => $bookingdetails['guestpaid'],
            'vat' => $bookingdetails['VAT'],
            'commission' => $bookingdetails['commission'],
            'commission_in_percenatge' => $bookingdetails['commission_in_percenatge'],
            'insurance_fees_in_percentage' => $bookingdetails['insurance_fees_percentage'],
            'security_deposit' => $bookingdetails['security_deposit'],
            'host_pay' => $bookingdetails['host_pay'],
            'host_discount' => $promo_host_discount,
            'darent_discount' => $promo_darent_discount,
        ]);
    }

    public function transferCreditToWallet($amount, $userId)
    {
        $wallet = NewUserWallet::where('user_id', $userId)->first();
        $currentAmount = $wallet->balance + $amount; //Current balance + New Balance
        $wallet->update(['balance' => $currentAmount]);

        // $transaction_data = [
        //     'payment_type_id' => PaymentTypeEnum::Wallet,
        //     'transaction_type_id' => TransactionTypes::DEBIT,
        //     'transaction_category_id' => TransactionCategories::WALLET_DEPOSIT,
        //     'user_id' => $userId,
        //     'amount' => $amount,
        //     'ref_json' => json_encode([$wallet])
        // ];
        // Transactions::create($transaction_data);

        $transaction_data = [
            'payment_type_id' => PaymentTypeEnum::Wallet,
            'transaction_type_id' => TransactionTypes::CREDIT,
            'transaction_category_id' => TransactionCategories::CASHBACK,
            // 'payee' => PaymentTypeEnum::DARENT,
            'user_id' => $userId,
            'amount' => $amount,
            'ref_json' => json_encode([$wallet]),
        ];
        Transactions::create($transaction_data);
    }

    public function customRound($number, $threshold = 0.5)
    {
        $isNegative = $number < 0;

        $absNumber = abs($number);

        // Check the decimal part of the number
        $decimalPart = $absNumber - floor($absNumber);

        // Determine the threshold for rounding
        $roundThreshold = 1 - $threshold;

        // Perform the rounding based on the threshold
        if ($decimalPart >= $roundThreshold) {
            // Round up
            $roundedNumber = ceil($absNumber);
        } else {
            // Round down
            $roundedNumber = floor($absNumber);
        }

        // Restore the sign of the number
        return $isNegative ? -$roundedNumber : $roundedNumber;
    }

    public function Requestheader($request)
    {

        if ($request->action == 'ARR_info') {
            return $requestData = [
                'apikey' => 'fNVo-JfhU7-dJw8wZ-h2rXeb-MYIjs-Bg5emf',
                'propertyid' => 3821,
                'version' => 2,
                'room_id' => 3821,
                'rate_id' => 1,
                'from_date' => $request->from_date,
                'to_date' => $request->to_date,
                'action' => $request->action,

            ];
        } elseif ($request->action == 'year_info_ARR') {
            return $requestData = [
                'apikey' => 'fNVo-JfhU7-dJw8wZ-h2rXeb-MYIjs-Bg5emf',
                'propertyid' => 3821,
                'version' => 2,
                'room_id' => 3821,
                'rate_id' => 1,
                'action' => $request->action,

            ];
        } elseif ($request->action == 'roomrate_info' || $request->action == 'property_info') {
            return $requestData = [
                'apikey' => 'fNVo-JfhU7-dJw8wZ-h2rXeb-MYIjs-Bg5emf',
                'propertyid' => 3821,
                'version' => 2,
                'action' => $request->action,

            ];
        } elseif ($request->action == 'reservation_info') {
        }
    }

    public function updateUserWallet($userId, $previousBookingTotal, $newBookingTotal, $bookingId)
    {

        if ($previousBookingTotal > $newBookingTotal) {
            $userWallet = NewUserWallet::where('user_id', $userId)->first();
            $amountAddedToUserWallet = 0;
            $amountAddedToUserWallet = abs($previousBookingTotal - $newBookingTotal);
            $userWallet->balance += $amountAddedToUserWallet;

            $transaction_data = [
                'payment_type_id' => PaymentTypeEnum::Wallet,
                'transaction_type_id' => TransactionTypes::CREDIT,
                'transaction_category_id' => TransactionCategories::WALLET_DEPOSIT,
                // 'payee' => PaymentTypeEnum::DARENT,
                'user_id' => $userWallet->user_id,
                'amount' => $amountAddedToUserWallet,
                'ref_json' => json_encode([$userWallet]),
                'type' => 'booking',
                'record_id' => $bookingId,
            ];
            Transactions::create($transaction_data);

            (User::find($userId))->notify(new UserNotify(
                'admin.wallet.deposit',
                route('user.wallet'),
                [':amount' => $amountAddedToUserWallet],
                data: ['slug' => 'wallet_deposit', 'tab' => '0', 'booking_id' => 0]
            ));
            $userWallet->save();
        } else {
            $userWallet = NewUserWallet::where('user_id', $userId)->first();
            $amountToMinusFromUserWallet = abs(round($newBookingTotal - $previousBookingTotal, 2));
            $walletBalanceAfterDeduction = round($userWallet->balance - $amountToMinusFromUserWallet, 2);

            FacadeLog::debug([
                "Previous Booking" => $previousBookingTotal,
                "New Booking" => $newBookingTotal,
                "Wallet Balance" => $userWallet->balance,
                "Detuct From Wallet" => $previousBookingTotal . ' - ' . $newBookingTotal . ' = ' . $amountToMinusFromUserWallet,
                "Wallet Amount After Detection" => $userWallet->balance . " - " . $amountToMinusFromUserWallet . " = " . $walletBalanceAfterDeduction,
            ]);

            $userWallet->balance = $walletBalanceAfterDeduction;
            $userWallet->save();

            $transaction_data = [
                'payment_type_id' => PaymentTypeEnum::Wallet,
                'transaction_type_id' => TransactionTypes::DEBIT,
                'transaction_category_id' => TransactionCategories::BOOKING_CONFIRMATION,
                'user_id' => $userWallet->user_id,
                'amount' => $amountToMinusFromUserWallet,
                'ref_json' => json_encode([$userWallet]),
                'type' => 'booking',
                'record_id' => $bookingId,
            ];

            Transactions::create($transaction_data);
        }
    }

    public function logAttempt($phone, $ip, $visitorId, $type, $details = null, $fingerprintData = null, $allowDuplicate = false)
    {

        if ($allowDuplicate == false) {
            // Check for existing recent log to avoid duplicates
            $existingLog = \App\Models\OtpAttemptLog::where('phone', $phone)
                ->where('visitor_id', $visitorId)
                ->where('created_at', '>=', now()->subMinutes(1))
                ->first();

            if ($existingLog) {
                // Update existing log with additional information
                $existingLog->update([
                    'attempt_type' => $type,
                    'error_details' => array_merge((array)$existingLog->error_details, (array)$details),
                    'fingerprint_data' => $fingerprintData ?? $existingLog->fingerprint_data
                ]);
                return $existingLog;
            }
        }


        // Create new log if no recent entry exists
        return \App\Models\OtpAttemptLog::create([
            'phone' => $phone,
            'ip' => $ip,
            'visitor_id' => $visitorId,
            'attempt_type' => $type,
            'error_details' => $details,
            'fingerprint_data' => $fingerprintData
        ]);
    }

    function photos_delete_adjust_serial($photobyid, $property)
    {
        $deletedSerial = $photobyid->serial;

        // Update the serial numbers of remaining photos with higher serial numbers
        PropertyPhotos::where('property_id', $property->id)
            ->where('serial', '>', $deletedSerial)
            ->decrement('serial', 1);

        if ($photobyid->cover_photo) {
            $makeCoverPhoto = PropertyPhotos::where('property_id', $property->id)
                ->where('cover_photo', 0)
                ->orderBy('serial', 'desc')
                ->take(1)
                ->update(['cover_photo' => 1]);
        }

        //unlink($photobyid->photo);
        $photobyid->delete();
    }

    public function cancelReservation($id, $type = 'guest')
    {
        try {
            DB::beginTransaction();
            $bookings = Bookings::with('properties')->find($id);
            $now = new DateTime();
            $booking_end = new DateTime($bookings->end_date);


            $booking_start = new DateTime($bookings->start_date);
            $interval_diff = $now->diff($booking_start);
            $interval = $interval_diff->days;
            $result = $this->GetRefundAmount($bookings, $interval);
            if ($result['status'] == 1) {
                $this->refundAmount($bookings, $result['amount_to_refund']);
            }

            $payount = Payouts::where(['user_id' => $bookings->host_id, 'booking_id' => $id])->first();

            if (isset($payount->id)) {
                $payout_penalties = PayoutPenalties::where('payout_id', $payount->id)->get();
                if (!empty($payout_penalties)) {
                    foreach ($payout_penalties as $key => $payout_penalty) {
                        $prv_penalty = Penalty::where('id', $payout_penalty->penalty_id)->first();
                        $update_amount = $prv_penalty->remaining_penalty + $payout_penalty->amount;
                        Penalty::where('id', $payout_penalty->penalty_id)->update(['remaining_penalty' => $update_amount, 'status' => 'Pending']);
                    }
                }
            }
            $payouts_host_amount = Payouts::where('user_id', $bookings->host_id)->where('booking_id', $id)->delete();

            $days = $this->get_days($bookings->start_date, $bookings->end_date);

            for ($j = 0; $j < count($days) - 1; $j++) {
                PropertyDates::where('property_id', $bookings->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
            }

            $cancel = Bookings::find($id);
            $cancel->cancelled_by = $type == 'guest' ? "Guest" : "Host";
            $cancel->cancelled_at = date('Y-m-d H:i:s');
            $cancel->status = "Cancelled";

            if ($type == "guest") {
                $platformReservationService = new PlatformReservationService();
                $platformReservationService->cancelPlatformReservation($cancel);
                $cancel->save();
            }

            BookingStatusLog::updateOrCreate(
                ['booking_id' => $cancel->id],
                ['status' => $cancel->status, 'changed_by' => $type == 'guest' ? $cancel->user_id : $cancel->host_id]
            );

            $booking_details = new BookingDetails;
            $booking_details->booking_id = $id;
            $booking_details->field = 'cancelled_reason';
            $booking_details->value = "-";
            // $booking_details->value      = $request->cancel_reason;
            $booking_details->save();

            if ($type == "guest") {
                $this->cancelPolicyTawuniya($cancel);
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            return false;
        }
    }
}
