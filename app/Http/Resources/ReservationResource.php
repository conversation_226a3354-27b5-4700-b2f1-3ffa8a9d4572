<?php

namespace App\Http\Resources;

use App\Models\PropertyFees;
use Illuminate\Http\Resources\Json\JsonResource;

class ReservationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // $total=$this->total_with_discount ?? $this->total;
        $total = $this->total_with_discount == 0.0 ? $this->total : $this->total_with_discount;

        $insurance = PropertyFees::where('field', PropertyFees::DARENT_INSURANCE)->first();

        $feesPercentage = $insurance->value + $this->host_fee_percent;
        $bookingHostCommissionAmount = (($this->base_price + $this->cleaning_charge) * ($feesPercentage)) / 100;
        $bookingTotal  = (($this->base_price + $this->cleaning_charge) * (100 - $feesPercentage)) / 100;
        return [
            'id' => $this->id,
            'date_with_price' => json_decode($this->date_with_price),
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'checkin_time' => $this->checkin_time,
            'checkout_time' => $this->checkout_time,
            'property_data' => $this->property_data,
            'guest_adult' => $this->guest_adult,
            'guest_child' => $this->guest_child,
            'total_night' => $this->total_night,
            'per_night' => $this->per_night,
            'base_price' => $this->base_price,
            'discount_type' => $this->discount_type,
            'you_saved' => $this->host_discount_amount,
            'service_fee_percent' => $this->service_fee_percent,
            'service_charge' => $this->service_charge,
            'guest_fee_percent' => $this->guest_fee_percent,
            'guest_charge' => $this->guest_charge,
            'cleaning_charge' => $this->cleaning_charge,
            'security_money' => $this->security_money,
            'iva_tax' => $this->iva_tax,
            'accomodation_tax' => $this->accomodation_tax,
            'host_fee_percent' => $this->host_fee_percent,
            'host_fee' =>  round($bookingHostCommissionAmount, 2),
            // 'host_total' => $this->when($this->user_type == 'host', number_format($total - $this->host_fee, 2, '.', '')),
            'host_total' => $this->when($this->user_type == 'host', number_format($bookingTotal, 2, '.', '')),
            'flat_discount_expire_at' => $this->flat_discount_expire_at,
            'flat_discount_percent' => $this->flat_discount_percent,
            'sub_total' => $this->sub_total,
            'total' => $total,
            'booking_type' => $this->when($this->type == 'booking', $this->booking_type),
            'code' => $this->when(!!$this->code, $this->code),
            'type' => $this->type,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'total_discount' => $this->total_discount,
            'total_accomodation' => $this->sub_total + $this->cleaning_charge + $this->security_money,
            'user' => [
                'id' => $this->user->id,
                'first_name' => ucfirst($this->user->first_name),
                'name' => ucwords($this->user->first_name . ' ' . $this->user->last_name),
                'phone' => $this->user->formatted_phone,
                'image' => !file_exists($this->user->profile_image) ? 'icons/user.svg' : $this->user->profile_image,
                'location' => collect([$this->user->location ?: '', $this->user->default_country ?: ''])->filter()->implode(', '),
                'avg_rating' => number_format(round($this->user->avg_rating, 1), 1),
                'is_previous' => $this->when($this->user_type == 'host', $this->user->is_previous),
                'total_rating' => $this->user->total_rating,
                'created_at' => $this->user->year
            ]
        ];
    }
}
