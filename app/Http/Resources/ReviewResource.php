<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Check if the request is coming from API version 2 or 3
        if (request()->route()->getPrefix() == "api/v2" || request()->route()->getPrefix() == "api/v3") {
            // Build the response array for API v2 or v3
            $response = [
                'id' => $this->id,
                'rating' => $this->rating,
                'message' => $this->message,
                'is_public' => $this->ispublic,
                'created_at' => $this->created_at,
                'cleanliness' => $this->cleanliness,
                'communication' => $this->communication,
                'accuracy' => $this->accuracy,
                'location' => $this->location,
                'reviewer' => array_combine(
                    ['id', 'name', 'profile_image'],
                    array_map(function ($attribute) {
                        return $attribute == 'profile_image' && !$this->users_from->$attribute ? 'icons/user.svg' : $this->users_from->$attribute;
                    }, ['id', 'first_name', 'profile_image'])
                ),
            ];

            // Include property information if property_id exists
            if ($this->property_id) {
                $response['property'] = array_combine(
                    ['id', 'name', 'image'],
                    array_map(function ($attribute) {
                        return $attribute == 'image' && !$this->properties->$attribute ? 'icons/user.svg' : $this->properties->$attribute;
                    }, ['id', 'name', 'image'])
                );
            }

            return $response;
        }

        // Default response array
        $response = [
            'id' => $this->id,
            'rating' => $this->rating,
            'message' => $this->message,
            'is_public' => $this->is_public,
            'created_at' => $this->created_at,
            'cleanliness' => $this->cleanliness,
            'communication' => $this->communication,
            'accuracy' => $this->accuracy,
            'location' => $this->location,
            'reviewer' => array_combine(
                ['id', 'name', 'profile_image'],
                array_map(function ($attribute) {
                    return $attribute == 'profile_image' && !$this->{"reviewer_$attribute"} ? 'icons/user.svg' : $this->{"reviewer_$attribute"};
                }, ['id', 'name', 'profile_image'])
            ),
        ];

        // Include property information if property_id exists
        if ($this->property_id) {
            $response['property'] = array_combine(
                ['id', 'name', 'image'],
                array_map(function ($attribute) {
                    return $attribute == 'image' && !$this->{"property_$attribute"} ? 'icons/user.svg' : $this->{"property_$attribute"};
                }, ['id', 'name', 'image'])
            );
        }

        return $response;
    }

}
