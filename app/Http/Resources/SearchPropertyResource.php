<?php

namespace App\Http\Resources;

use App\Http\Helpers\Common;
use App\Models\Reviews;
use Illuminate\Http\Request;


use Illuminate\Http\Resources\Json\JsonResource;

class SearchPropertyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        if (auth()->guard('api')->check()) {
            $userLang = auth()->guard('api')->user()->lang;
        } else {
            $userLang = $request->lang ?? 'ar';
        }
        $add_ar = $userLang == 'ar' ? '_ar' : '';
        return [
            "id" => $this->id,
            "available" => (bool)$this->available,
            "name" => $this->name,
            "unit_code" => $this->property_code,
            "slug" => $this->slug,
            "photo" => $this->cover_photo,
            "images" => $this->property_photos,
            "summary" => $this->{"property_description_summary$add_ar"},
            "rating" => $this->average_rating,
            "location" => is_null($this->{"property_address_district$add_ar"}) ? $this->{"property_address_city$add_ar"} : $this->{"property_address_district$add_ar"} . ', ' . $this->{"property_address_city$add_ar"},
            "price" => $this->total_price,
            "wishlist" => $this->wishlist,
            "latitude" => $this->property_address_latitude,
            "longitude" => $this->property_address_longitude,
            "property_title" => $this->{"property_type_name$add_ar"} . ($userLang == 'ar' ? ' في ' : ' In ') . transStateCity($this->state, $userLang) . $this->{"property_address_city$add_ar"},
            'wishlist_added' => $this->wishlist_added,
            'views' => (string)$this->views_count,
            "discount" => null,
            "discounted_amount" => null,
            // "rating_count" => Reviews::where('property_id', $this->id)->count(),
            "rating_count" => isset($this->reviews_count) ? number_format($this->reviews_count) : 0,
            "host_profile" => null,
            "host_id" => (int)$this->host_id,
            "property_type_name" => $this->{"property_type_name$add_ar"},
            'days_discount' => $this->getMonthlyWeeklyDiscount(),
            'days_discount_value' => $this->getMonthlyWeeklyDiscountValue(),
            "number_of_days" => (int)$this->number_of_days,
            "day_price" => (float)$this->day_price,
            "total_price" => (float)$this->total_price ?? 0.00,
            "before_discount" => (float)$this->before_discount ?? 0.00,
        ];
    }
}
