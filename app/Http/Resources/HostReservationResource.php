<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
use App\Models\PropertyFees;
use App\Http\Helpers\Common;
use App\Http\Resources\PropertyPhotosResource;
use App\Models\BookingPaymentDetails;
use Illuminate\Support\Facades\Log;

class HostReservationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        // dd(request()->route()->uri);
        $message = '';
        $departure = '';

        if ($this->end_date == Carbon::now()->format('Y-m-d')) {
            $message = "Guest will be checking out today";
        } else {
            $message = "Guest will not checking out today";
        }

        if (Carbon::parse($this->start_date)->isToday()) {
            $departure = "Today";
        } elseif (Carbon::parse($this->start_date)->subDay()->isToday()) {
            $departure = "Tommorrow";
        } else {
            $departure = 'After 2+ Days' . Carbon::parse($this->start_date)->subDay();
        }

        $currentDate = Carbon::now();
        $startDate = Carbon::parse($this->start_date);

        $daysDifference = $currentDate->diffInDays($startDate);

        $payabletohost = Common::payableToHost($this)['host_pay'];
        $property_fees_vat = PropertyFees::where('field', 'vat')->first();
        $totalPrice = $this->total_with_discount != 0 ? $this->total_with_discount : $this->total;
        $basePrice = $this->base_price_with_discount != 0 ? $this->base_price_with_discount : $this->base_price;
        $hostFeeWithDarentComission = $basePrice * ($this->host->commission / 100);
        // $hostFeeWithVat = $totalPrice * ($property_fees_vat->value / 100);
        $host_fee = $hostFeeWithDarentComission;
        $totalAccomodation = $this->base_price + $this->cleaning_charge + $this->security_money;
        // return parent::toArray($request);

        $currentRoute = $request->route()->getName();

        if ($this->status === 'Accepted') {
            $phoneNumber = $this->users->formatted_phone;
        } else {
            // Mask the phone number
            // $phoneNumber = substr_replace($this->users->formatted_phone, '***', 3, -4);
            $phoneNumber = '******';

        }
        $bookingPaymentDetails = BookingPaymentDetails::where('booking_id', $this->id)->first();

        Log::debug('booking_guest', [$this->booking_guest]);

        if (request()->route()->getPrefix() == "api/v2") {
            $data = [
                "booking_code" => $this->code,
                "user_name" => $this->users->getFullNameAttribute() ?? "User",
                'insurancePercent' => $bookingPaymentDetails->insurance_fees_in_percentage ?? 0,
                "start_date" => $this->start_date,
                "end_date" => $this->end_date,
                "property_name" => $this->properties->name,
                "phone" => $phoneNumber ?? null,
                "country" => $this->properties?->property_address?->country,
                "cover_photo" => $this->properties->cover_photo,
                "latitude" => $this->properties?->property_address?->latitude ?? null,
                "longitude" => $this->properties?->property_address?->longitude ?? null,
                "address" => $this->properties?->property_address?->address_line_1 ?? null,
                "upcoming_days" => $daysDifference ?? 0,
                "chat_head_id" => $this->chat_head_id,
                "user_delete" => isset($this->users->deleted_at) ? true : false,
            ];
        } else {
            $data = [
                "id" => $this->id,
                "condition"   => $message,
                "departure"   => $departure,
                "property_id" => $this->property_id,
                "property_code" => $this->properties?->property_code ?? null,
                "booking_code" => $this->code,
                "user_name" => $this->booking_guest?->getFullNameAttribute() ?? "User",
                // "cancel_policy" => $this->host->cancel_policy,
                "cancel_policy" => $this->properties->cancellation,
                'discount_type' => $this->discount_type,
                "phone" => $phoneNumber,
                "profile" => $this->booking_guest?->profile_src,
                "joined" => $this->booking_guest?->created_at->year,
                "start_date" => $this->start_date,
                "end_date" => $this->end_date,
                "property_name" => $this->properties->name,
                "guest" => $this->guest,
                'adults' => $this->guest_adult,
                'children' => $this->guest_child,
                'user_address' => $this->booking_guest->location ?? 'N/A',
                "status" => $this->status,
                "user_rating" => $this->booking_guest->userRating,
                "checkin_time" => Carbon::parse($this->checkin_time)->format('h:i A'),
                "checkout_time" => Carbon::parse($this->checkout_time)->format('h:i A'),
                "total" => $totalPrice,
                "base_price" => $this->base_price,
                "cleaning_fee" => $this->cleaning_charge,
                "guest_fee" => $this->guest_charge,
                "host_fee" => $bookingPaymentDetails->commission ?? 0,
                "host_total" => $bookingPaymentDetails->host_pay ?? 0,
                'insurancePercent' => $bookingPaymentDetails->insurance_fees_in_percentage ?? 0,
                "chat_head_id" => $this->chat_head_id,
                "total_nights" => $this->total_night,
                "property_address" => $this->properties?->property_address?->address_line_1 ?? null,
                "latitude" => $this->properties?->property_address?->latitude ?? null,
                "longitude" => $this->properties?->property_address?->longitude ?? null,
                "photos" => PropertyPhotosResource::collection($this->properties->property_photos),
                "user_delete" => isset($this->users->deleted_at) ? true : false,
                "service_charge" => $this->service_charge,
                "security_fee" => $this->security_money,
                "host_commission" => $this->host_fee_percent ?? 0,
                "total_discount"    =>  $this->total_discount,
                'total_accomodation' => $totalAccomodation,
                'id_achieved' => $this->booking_guest?->is_elm_verified,
                'you_saved' => $this->host_discount_amount
                // "photos" => $this->properties->property_photos
            ];
        }


        return $data;
    }
}
