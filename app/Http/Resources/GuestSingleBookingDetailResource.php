<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
use App\Models\PropertyFees;
use App\Http\Helpers\Common;
use App\Http\Resources\PropertyPhotosResource;
use App\Models\Amenities;
use App\Models\NewUserWallet;
use App\Models\Reviews;
use App\Models\Transactions;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class GuestSingleBookingDetailResource extends JsonResource
{


        public $weekly_monthly_discount;

    public function __construct($resource, $weekly_monthly_discount)
    {
        parent::__construct($resource);
        $this->weekly_monthly_discount = $weekly_monthly_discount;
    }
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $message = '';
        $departure = '';

        if ($this->end_date == Carbon::now()->format('Y-m-d')) {
            $message = "Guest will be checking out today";
        } else {
            $message = "Guest will not checking out today";
        }

        if (Carbon::parse($this->start_date)->isToday()) {
            $departure = "Today";
        } elseif (Carbon::parse($this->start_date)->subDay()->isToday()) {
            $departure = "Tommorrow";
        } else {
            $departure = 'After 2+ Days' . Carbon::parse($this->start_date)->subDay();
        }
        $propertyFees = PropertyFees::pluck('value', 'field');
        $guest_service_charge = $propertyFees['guest_service_charge'];

        $service_fee_on_cleaning = $this->cleaning_charge * $guest_service_charge / 100;
        $service_fee_on_security_money = $this->security_money * $guest_service_charge / 100;

        $currentDate = Carbon::now();
        $startDate = Carbon::parse($this->start_date);

        $daysDifference = $currentDate->diffInDays($startDate);

        $payabletohost = Common::payableToHost($this)['host_pay'];
        $property_fees_vat = PropertyFees::where('field', 'vat')->first();
        $totalPrice = $this->total_with_discount != 0 ? $this->total_with_discount : $this->total;
        $basePrice = $this->base_price_with_discount != 0 ? $this->base_price_with_discount : $this->base_price;
        $hostFeeWithDarentComission = $totalPrice * ($this->host->commission / 100);
        $hostFeeWithVat = $totalPrice * ($property_fees_vat->value / 100);
        $host_fee = $hostFeeWithDarentComission + $hostFeeWithVat;
        $sub_total = $this->base_price + $this->cleaning_charge + $this->security_money;
        $per_night = $sub_total / $this->total_night;

        $newUserWallet = NewUserWallet::where('user_id', $this->user_id)->first();
        $transactions = Transactions::where('booking_id', $this->id)->where('wallet_id', $newUserWallet->id)->first();

        if ($this->status == "Accepted") {
            $phoneNumber = $this->users->formatted_phone;
        } else {
            // Mask the phone number
            // $phoneNumber = substr_replace($this->users->formatted_phone, '***', 3, -4);
            $phoneNumber = '******';
        }

        $rated = false;
        $reviews = Reviews::where('property_id', $this->property_id)->where('booking_id', $this->id)->where('sender_id', Auth::guard('api')->user()->id)->first();
        if ($reviews) {
            $rated = true;
        }

        $data = [
            "id" => $this->id,
            "unit_code" => $this->properties->property_code,
            "slug" => $this->properties->slug,
            "payment_type" => $this->payment_type?->title,
            "condition"   => $message,
            "departure"   => $departure,
            "property_id" => $this->property_id,
            "booking_code" => $this->code,
            "user_name" => $this->host->getFullNameAttribute() ?? "User",
            // "cancel_policy" => $this->host->cancel_policy,
            "cancel_policy" => $this->properties->cancellation,
            "phone" => $phoneNumber,
            "profile" => $this->users->profile_src,
            "joined" => $this->users->created_at->year,
            "start_date" => $this->start_date,
            "end_date" => $this->end_date,
            "property_name" => $this->properties->name,
            "guest" => $this->guest,
            "adults" => isset($this->guest_adult) ? $this->guest_adult : 0,
            "children" => isset($this->guest_child) ? $this->guest_child : 0,
            'user_address' => $this->users->location ?? 'N/A',
            "status" => $this->status,
            "user_rating" => $this->users->userRating,
            "checkin_time" => Carbon::parse($this->checkin_time)->format('h:i A'),
            "checkout_time" => Carbon::parse($this->checkout_time)->format('h:i A'),
            "total" => $totalPrice,
            "base_price" => $basePrice,
            "base_price_with_discount" => $this->sub_total,
            "service_fee" => $this->service_charge,
            'security_fee' => $this->security_money,
            "cleaning_fee" => $this->cleaning_charge,
            "per_night" => $per_night,
            "sub_total" => $sub_total,
            "guest_fee" => $this->guest_charge,
            "host_fee" => $host_fee,
            "host_total" => $payabletohost,
            'discount_type' => $this->discount_type,
            "chat_head_id" => $this->chat_head_id,
            "total_nights" => $this->total_night,
            "property_address" => $this->properties->property_address->address_line_1 ?? null,
            "latitude" => $this->properties->property_address->latitude ?? null,
            "longitude" => $this->properties->property_address->longitude ?? null,
            "city" => $this->properties->property_address->city ?? null,
            "description" => $this->properties->property_description->summary ?? null,
            "photos" => PropertyPhotosResource::collection($this->properties->property_photos),
            "houserule_amenities" => Amenities::house_rule($this->property_id),
            "has_discount" => $this->properties->property_discount ? true : false,
            "host_number" => $this->host->formatted_phone,
            "additional_guest" => $this->guest_charge ?? 0,
            "iva_tax" => $this->iva_tax ?? 0,
            "accommodation_tax" => $this->accommodation_tax ?? 0,
            // "service_fee_on_cleaning" => ($this->properties->platform_id != 4 ? $service_fee_on_cleaning ?? 0 : 0),
            "service_fee_on_cleaning" => $service_fee_on_cleaning ?? 0,
            "service_fee_security" => $service_fee_on_security_money ?? 0,
            // "rated" => Common::CheckReview($this->property_id),
            "rated" => $rated,
            "has_promo" => $this->promoCodeUsage ? true : false,
            "property_type" => $this->properties->property_type_name ?? Null,
            "total_discount" => $this->total_discount,
            "property_status" => $this->property_status,
            "wallet_deduction" =>  $this->pay_by_wallet ?? 0.0,
            'you_saved' => $this->weekly_monthly_discount,                                         //$transactions ? $transactions->amount : null,
            "payment_getway" => app('PAYMENT_METHOD'),
            // "photos" => $this->properties->property_photos
        ];
        // Log::debug('booking_guest_details', [$data]);
        return
            [
                "message" => "Success",
                "data" => [
                    "guestbooking" => $data,
                ],

            ];
    }
}
