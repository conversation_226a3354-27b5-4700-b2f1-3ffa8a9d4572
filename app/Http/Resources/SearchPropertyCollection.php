<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;
use App\Http\Resources\SearchPropertyResource;

class SearchPropertyCollection extends ResourceCollection
{
    private $pagination;
    private $districts;
    private $location;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function __construct($resource, $location, $districtsMulti)
    {
        $this->location = $location; // SEARCHED LOACTION LAT/LONG
        $this->districts = $districtsMulti; // Districts of selected Location
        $accept = request()->route()->getPrefix();
        if (str_contains($accept, 'v5')) {
            $this->pagination = [
                'total' => $resource->total(),
                'count' => $resource->count(),
                'per_page' => $resource->perPage(),
                'current_page' => $resource->currentPage(),
                'total_pages' => $resource->lastPage()
            ];
        } else {
        $this->pagination = [
            'next_page_url' =>$resource->nextPageUrl(),
            'current_page' => $resource->currentPage(),
            'prev_page_url' => $resource->nextPageUrl(),
            'has_more_pages' => $resource->hasMorePages(),
        ];
        }

        $resource = $resource->getCollection();

        parent::__construct($resource);
    }
    public function toArray($request)
    {
        return [
            "message" => "Success",
            "data" => [
                "result" => SearchPropertyResource::collection($this->collection),
                'pagination'    => $this->pagination,
                'location'      => $this->location,
                'districts'     => $this->districts,
            ]
        ];
    }
}
