<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ReviewPaginatedResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $nextPageUrl = str_contains(($this->nextPageUrl() ?? ''), 'https') ? $this->nextPageUrl() : str_replace('http', 'https', ($this->nextPageUrl() ?? ''));

        return [
            'list' => ReviewResource::collection($this->getCollection()),
            'has_more' => !!$nextPageUrl,
            'next_page' => $nextPageUrl,
            'per_page' => $this->perPage(),
            'total'=>$this->total()
        ];
    }
}
