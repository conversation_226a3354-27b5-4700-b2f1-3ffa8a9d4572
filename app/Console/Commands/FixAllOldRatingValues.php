<?php

namespace App\Console\Commands;

use App\Models\Bookings;
use App\Models\PropertyDates;
use App\Models\Reviews;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FixAllOldRatingValues extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix-all-old-rating-values';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $reviews = Reviews::query()->get();

        foreach ($reviews as $review) {
            if ($review->darent_service == null || $review->darent_service > 5) {
                $review->darent_service = 5;
            }

            if ($review->darent_recomended == null || $review->darent_recomended > 5) {
                $review->darent_recomended = 5;
            }
            if ($review->location == null || $review->location > 5) {
                $review->location = 5;
            }
            $review->accuracy = max(round($review->accuracy / 2), 1);
            $review->communication = max(round($review->communication / 2), 1);
            $review->cleanliness = max(round($review->cleanliness / 2), 1);
            $review->location = max(round($review->location / 2), 1);
            echo $review->id . "\n";
            
            // $review->darent_service +
            // $review->darent_recomended
            $total = (
                    $review->accuracy +
                    $review->communication +
                    $review->cleanliness +
                    $review->location 
                ) / 4;
            $review->rating = $total;
            $review->save();
        }
    }
}
