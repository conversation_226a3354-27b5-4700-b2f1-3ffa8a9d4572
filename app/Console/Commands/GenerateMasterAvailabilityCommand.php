<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Properties;
use App\Http\Helpers\Common;

class GenerateMasterAvailabilityCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'availability:generate
                            {--period=365 : Period in days to generate data for}
                            {--property_id= : Specific property ID to generate data for}
                            {--chunk=100 : Number of properties to process in each chunk}
                            {--queue : Process in background queue instead of synchronously}
                            {--from-date= : Start date for data generation (format: Y-m-d)}
                            {--only-missing : Only generate data for dates that don\'t exist yet}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate master availability data for properties (optimized for large datasets)';

    protected $helper;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $startTime = microtime(true);
        $this->info('Starting master availability data generation...');

        // Get options from command
        $period = (int) $this->option('period');
        $specificPropertyId = $this->option('property_id');
        $chunkSize = (int) $this->option('chunk');
        $useQueue = $this->option('queue');
        $fromDateStr = $this->option('from-date');
        $onlyMissing = $this->option('only-missing');
        $this->helper = new Common;
        // Validate and set start date
        $startDate = $fromDateStr ? Carbon::createFromFormat('Y-m-d', $fromDateStr) : Carbon::today();
        if (!$startDate) {
            $this->error('Invalid from-date format. Please use Y-m-d format.');
            return Command::FAILURE;
        }

        $endDate = $startDate->copy()->addDays($period);

        $this->info("Generating availability data from {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}" .
            ($specificPropertyId ? " for property ID: {$specificPropertyId}" : ""));

        if ($onlyMissing) {
            $this->info("Only generating data for dates that don't exist yet");
        }

        // Check if the table exists, if not run the migration
        if (!Schema::hasTable('master_availability')) {
            $this->info('Master availability table does not exist. Creating it...');
            $this->call('migrate', [
                '--path' => 'database/migrations/2023_06_10_000000_create_master_availability_table.php'
            ]);
        }

        // Build query for properties to process
        $query = Properties::where('status', 'Listed')
            ->where('visibility', 1);

        if ($specificPropertyId) {
            $query->where('id', $specificPropertyId);
        }

        // Get total count for progress tracking
        $totalProperties = $query->count();
        $this->info("Found {$totalProperties} properties to process in chunks of {$chunkSize}");

        if ($useQueue) {
            $this->processInQueue($query, $chunkSize, $startDate, $endDate, $onlyMissing, $totalProperties);
        } else {
            $this->processChunked($query, $chunkSize, $startDate, $endDate, $onlyMissing, $totalProperties, $startTime);
        }

        $totalTime = round(microtime(true) - $startTime, 2);
        $this->info("\nMaster availability data generation " .
            ($useQueue ? "jobs dispatched" : "completed") .
            " in {$totalTime} seconds!");

        return Command::SUCCESS;
    }

    /**
     * Process properties in chunks synchronously
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $chunkSize
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param bool $onlyMissing
     * @param int $totalProperties
     * @param float $startTime
     * @return void
     */
    private function processChunked($query, $chunkSize, $startDate, $endDate, $onlyMissing, $totalProperties, $startTime)
    {
        // Create progress bar
        $bar = $this->output->createProgressBar($totalProperties);
        $bar->start();

        $processedCount = 0;

        // Process in chunks to reduce memory usage
        $query->chunk($chunkSize, function ($properties) use ($startDate, $endDate, $onlyMissing, &$processedCount, &$bar, $totalProperties, $startTime) {
            foreach ($properties as $property) {
                $this->processProperty($property, $startDate, $endDate, $onlyMissing);
                $bar->advance();
                $processedCount++;

                // Show stats periodically
                if ($processedCount % 100 === 0 || $processedCount === $totalProperties) {
                    $elapsed = round(microtime(true) - $startTime, 2);
                    $this->info("\nProcessed {$processedCount} properties in {$elapsed} seconds");
                }
            }
        });

        $bar->finish();
    }

    /**
     * Process properties by dispatching queue jobs
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $chunkSize
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param bool $onlyMissing
     * @param int $totalProperties
     * @return void
     */
    private function processInQueue($query, $chunkSize, $startDate, $endDate, $onlyMissing, $totalProperties)
    {
        $jobCount = 0;
        $propertyIds = [];

        // Collect property IDs in chunks and dispatch jobs
        $query->chunk($chunkSize, function ($properties) use (&$jobCount, &$propertyIds, $startDate, $endDate, $onlyMissing) {
            $chunkIds = $properties->pluck('id')->toArray();
            $propertyIds = array_merge($propertyIds, $chunkIds);

            // Dispatch job for this chunk
            dispatch(new \App\Jobs\GenerateMasterAvailabilityJob(
                $chunkIds,
                $startDate->format('Y-m-d'),
                $endDate->format('Y-m-d'),
                $onlyMissing
            ));

            $jobCount++;
            $this->info("Dispatched job #{$jobCount} for " . count($chunkIds) . " properties");
        });

        $this->info("Total: Dispatched {$jobCount} jobs for {$totalProperties} properties");
        $this->info("Property IDs will be processed in the queue: " . count($propertyIds));
    }

    /**
     * Process a single property and generate availability data
     *
     * @param Properties $property
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return void
     */
    private function processProperty($property, $startDate, $endDate, $onlyMissing = false)
    {
        // If only processing missing dates, get existing dates for this property
        $existingDates = [];
        if ($onlyMissing) {
            $existingDates = DB::table('master_availability')
                ->where('property_id', $property->id)
                ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                ->pluck('date')
                ->toArray();
        }

        // Get existing bookings for this property in the date range using direct DB query
        // Ordered by created_at desc to prioritize latest bookings
        $bookings = DB::table('bookings')
            ->where('property_id', $property->id)
            ->where(function($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                    ->orWhereBetween('end_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                    ->orWhere(function($q) use ($startDate, $endDate) {
                        $q->where('start_date', '<', $startDate->format('Y-m-d'))
                          ->where('end_date', '>', $endDate->format('Y-m-d'));
                    });
            })
            ->whereNull('deleted_at') // Ensure we only get non-deleted bookings
            ->orderBy('created_at', 'desc')
            ->get();

        // Get custom pricing data for this property using direct DB query
        $customPricingResults = DB::table('custom_pricing')
            ->where('property_id', $property->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->orderBy('date')
            ->get();

        // Convert to collection and key by date for easier lookup
        $customPricing = collect();
        foreach ($customPricingResults as $item) {
            $customPricing[$item->date] = $item;
        }

        // Get property dates data for this property using direct DB query
        $propertyDatesResults = DB::table('property_dates')
            ->where('property_id', $property->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->orderBy('date')
            ->get();

        // Convert to collection and key by date for easier lookup
        $propertyDates = collect();
        foreach ($propertyDatesResults as $item) {
            $propertyDates[$item->date] = $item;
        }

        // Calculate successful bookings in last 30 days for performance tier using direct DB query
        $successfulBookingsLast30Days = DB::table('bookings')
            ->where('property_id', $property->id)
            ->where('status', 'Accepted')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->whereNull('deleted_at')
            ->count();

        // Get property location data from property_address
        $propertyAddress = DB::table('property_address')->where('property_id', $property->id)->first();

        // Determine performance tier once for all dates
        $performanceTier = $this->calculatePerformanceTier($property, $successfulBookingsLast30Days);

        // Generate data for each day in the period
        $currentDate = clone $startDate;
        $batchData = [];
        $batchSize = 50; // Increased batch size for better performance with updateOrInsert
        $processedDates = 0;
        $skippedDates = 0;

        while ($currentDate <= $endDate) {
            $dateString = $currentDate->format('Y-m-d');
            $newDate = $currentDate;
            // Skip if we're only processing missing dates and this date already exists
            if ($onlyMissing && in_array($dateString, $existingDates)) {
                $skippedDates++;
                $currentDate->addDay();
                continue;
            }
            $price = [];
            // Determine availability status from property_dates and custom_pricing
            $status = $this->determineAvailabilityStatus($dateString, $propertyDates, $customPricing);
            if($status == 'available'){
                $price = $this->helper->getPrice2($property,$newDate);
            }
            // Find booking for this date if exists
            $booking = $this->findBookingForDate($currentDate, $bookings);



            // Create data record
            $record = [
                'property_id' => $property->id,
                'date' => $dateString,
                'status' => $status,
                'determine_price' => $price['price'] ?? 0,
                'weekly_discount_amount' => $price['weekly_discount_amount'] ?? 0,
                'monthly_discount_amount' => $price['monthly_discount_amount'] ?? 0,
                'host_id' => $property->host_id,
                'property_type' => $property->property_type,
                'country' => $propertyAddress ? $propertyAddress->country : null,
                'city_id' => $propertyAddress ? $propertyAddress->city_id : null,
                'district_id' => $propertyAddress ? $propertyAddress->district_id : null,
                'platform_id' => $property->platform_id ?? 0,
                'is_peak_day' => 'No', // Default, would need peak calendar data to determine
                'peak_day_details' => null,
                'performance_tier' => $performanceTier,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Add booking data if exists
            if ($booking) {
                $record['booking_id'] = $booking->id;
                $record['booking_status'] = $booking->status;
                $record['booking_created_at'] = $booking->created_at;
                $record['booking_cancelled_at'] = $booking->status === 'Cancelled' ? $booking->updated_at : null;
                $record['checkin_date'] = $booking->start_date;
                $record['checkout_date'] = $booking->end_date;
                $record['user_id'] = $booking->user_id;
                if($booking->status === 'Accepted'){
                    $record['source_type'] = 'booking';
                }
                else{
                    $record['source_type'] = $this->determineSourceType($dateString, $propertyDates, $customPricing);
                }
            } else {
                // Determine source type based on availability data
                $record['source_type'] = $this->determineSourceType($dateString, $propertyDates, $customPricing);
            }

            $batchData[] = $record;
            $processedDates++;

            // Insert in batches to improve performance
            if (count($batchData) >= $batchSize) {
                $this->insertBatch($batchData);
                $batchData = [];
            }

            $currentDate->addDay();
        }

        // Insert any remaining records
        if (!empty($batchData)) {
            $this->insertBatch($batchData);
        }

        return [
            'processed' => $processedDates,
            'skipped' => $skippedDates
        ];
    }

    /**
     * Insert a batch of records using upsert to handle duplicates
     *
     * @param array $records
     * @return void
     */
    private function insertBatch($records)
    {
        // Use insert or ignore approach instead of upsert to avoid column order issues
        foreach ($records as $record) {
            DB::table('master_availability')->updateOrInsert(
                [
                    'property_id' => $record['property_id'],
                    'date' => $record['date']
                ],
                $record
            );
        }
    }

    /**
     * Determine availability status from property_dates and custom_pricing
     *
     * @param string $dateString
     * @param \Illuminate\Support\Collection $propertyDates
     * @param \Illuminate\Support\Collection $customPricing
     * @return string
     */
    private function determineAvailabilityStatus($dateString, $propertyDates, $customPricing)
    {
        // Check property_dates first
        if (isset($propertyDates[$dateString])) {
            return $propertyDates[$dateString]->status === 'Available' ? 'available' : 'unavailable';
        }

        // Then check custom_pricing
        if (isset($customPricing[$dateString])) {
            return $customPricing[$dateString]->status === 'Available' ? 'available' : 'unavailable';
        }

        // Default to available
        return 'available';
    }

    /**
     * Determine source type based on availability data
     *
     * @param string $dateString
     * @param \Illuminate\Support\Collection $propertyDates
     * @param \Illuminate\Support\Collection $customPricing
     * @return string
     */
    private function determineSourceType($dateString, $propertyDates, $customPricing)
    {
        // Check property_dates first and use its type value
        if (isset($propertyDates[$dateString])) {
            // Map the type from property_dates to source_type in master_availability
            $type = $propertyDates[$dateString]->type;

            if ($type === 'calendar') {
                return 'host_calendar';
            } else if ($type === 'normal') {
                // For normal type, check if it's from admin or system
                // You might need to add more logic here based on your business rules
                return 'Gest';
            } else {
                // For any other type value
                return 'system';
            }
        }

        // Check custom_pricing
        if (isset($customPricing[$dateString])) {
            $type = $customPricing[$dateString]->type;

            if ($type === 'calendar') {
                return 'host_calendar';
            } else {
                return 'system';
            }
        }

        // Default to system
        return 'system';
    }

    /**
     * Find the latest booking for a specific date
     *
     * @param Carbon $date
     * @param \Illuminate\Support\Collection $bookings
     * @return object|null
     */
    private function findBookingForDate($date, $bookings)
    {
        // Since we're already ordering by created_at desc in the query,
        // we can just return the first matching booking
        foreach ($bookings as $booking) {
            $startDate = Carbon::parse($booking->start_date);
            $endDate = Carbon::parse($booking->end_date);

            if ($date->between($startDate, $endDate->subDay())) {
                return $booking; // This will be the latest booking due to our ordering
            }
        }

        return null;
    }

    /**
     * Calculate performance tier based on property status and booking history
     *
     * @param Properties $property
     * @param int $successfulBookingsLast30Days
     * @return string
     */
    private function calculatePerformanceTier($property, $successfulBookingsLast30Days)
    {
        // Check if property is available (Listed and visible)
        $isAvailable = $property->status === 'Listed' && $property->visibility == 1;

        if (!$isAvailable) {
            return 'Low Performer';
        }

        if ($successfulBookingsLast30Days >= 2) {
            return 'High Performer';
        } else if ($successfulBookingsLast30Days > 0) {
            return 'Moderate Performer';
        } else {
            return 'Low Performer';
        }
    }
}