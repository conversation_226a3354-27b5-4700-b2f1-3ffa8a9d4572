<?php

namespace App\Jobs;

use App\Models\Bookings;
use App\Models\CustomPricing;
use App\Models\Properties;
use App\Models\PropertyDates;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Helpers\Common;

class GenerateMasterAvailabilityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The property IDs to process
     *
     * @var array
     */
    protected $propertyIds;

    /**
     * The start date for data generation
     *
     * @var string
     */
    protected $startDate;

    /**
     * The end date for data generation
     *
     * @var string
     */
    protected $endDate;

    /**
     * Whether to only generate data for missing dates
     *
     * @var bool
     */
    protected $onlyMissing;

    protected $helper;

    /**
     * Create a new job instance.
     *
     * @param array $propertyIds
     * @param string $startDate
     * @param string $endDate
     * @param bool $onlyMissing
     * @return void
     */
    public function __construct(array $propertyIds, string $startDate, string $endDate, bool $onlyMissing = false)
    {
        $this->propertyIds = $propertyIds;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->onlyMissing = $onlyMissing;
        $this->helper = new Common;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $startTime = microtime(true);
        $startDate = Carbon::createFromFormat('Y-m-d', $this->startDate);
        $endDate = Carbon::createFromFormat('Y-m-d', $this->endDate);

        Log::info("Processing availability job for " . count($this->propertyIds) . " properties from {$this->startDate} to {$this->endDate}");

        $properties = Properties::whereIn('id', $this->propertyIds)->get();
        $processedCount = 0;
        $totalProcessed = 0;
        $totalSkipped = 0;

        foreach ($properties as $property) {
            try {
                $result = $this->processProperty($property, $startDate, $endDate, $this->onlyMissing);
                $totalProcessed += $result['processed'];
                $totalSkipped += $result['skipped'];
                $processedCount++;
            } catch (\Exception $e) {
                Log::error("Error processing property {$property->id}: " . $e->getMessage());
            }
        }

        $totalTime = round(microtime(true) - $startTime, 2);
        Log::info("Completed availability job for {$processedCount} properties in {$totalTime} seconds. Processed: {$totalProcessed}, Skipped: {$totalSkipped}");
    }

    /**
     * Process a single property and generate availability data
     *
     * @param Properties $property
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param bool $onlyMissing
     * @return array
     */
    private function processProperty($property, $startDate, $endDate, $onlyMissing = false)
    {
        // If only processing missing dates, get existing dates for this property
        $existingDates = [];
        if ($onlyMissing) {
            $existingDates = DB::table('master_availability')
                ->where('property_id', $property->id)
                ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                ->pluck('date')
                ->toArray();
        }

        // Get existing bookings for this property in the date range
        $bookings = Bookings::where('property_id', $property->id)
            ->where(function($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                    ->orWhereBetween('end_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                    ->orWhere(function($q) use ($startDate, $endDate) {
                        $q->where('start_date', '<', $startDate->format('Y-m-d'))
                          ->where('end_date', '>', $endDate->format('Y-m-d'));
                    });
            })
            ->get();

        // Get custom pricing data for this property
        $customPricing = CustomPricing::where('property_id', $property->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // Get property dates data for this property
        $propertyDates = PropertyDates::where('property_id', $property->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // Calculate successful bookings in last 30 days for performance tier
        $successfulBookingsLast30Days = Bookings::where('property_id', $property->id)
            ->where('status', 'Accepted')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->count();

        // Determine performance tier
        $performanceTier = $this->calculatePerformanceTier($property, $successfulBookingsLast30Days);

        // Generate data for each day in the period
        $currentDate = clone $startDate;
        $batchData = [];
        $batchSize = 100; // Larger batch size for better performance
        $processedDates = 0;
        $skippedDates = 0;

        while ($currentDate <= $endDate) {
            $dateString = $currentDate->format('Y-m-d');
            $newDate = $currentDate;

            // Skip if we're only processing missing dates and this date already exists
            if ($onlyMissing && in_array($dateString, $existingDates)) {
                $skippedDates++;
                $currentDate->addDay();
                continue;
            }
            $price = [];
            // Determine availability status from property_dates and custom_pricing
            $status = $this->determineAvailabilityStatus($dateString, $propertyDates, $customPricing);
            if($status == 'available'){
                $price = $this->helper->getPrice2($property,$newDate);
            }
            // Find booking for this date if exists
            $booking = $this->findBookingForDate($currentDate, $bookings);

            // Get property location data from property_address
            $propertyAddress = DB::table('property_address')->where('property_id', $property->id)->first();

            // Create data record
            $record = [
                'property_id' => $property->id,
                'date' => $dateString,
                'status' => $status,
                'determine_price' => $price['price'] ?? 0,
                'weekly_discount_amount' => $price['weekly_discount_amount'] ?? 0,
                'monthly_discount_amount' => $price['monthly_discount_amount'] ?? 0,
                'host_id' => $property->host_id,
                'property_type' => $property->property_type,
                'country' => $propertyAddress ? $propertyAddress->country : null,
                'city_id' => $propertyAddress ? $propertyAddress->city_id : null,
                'district_id' => $propertyAddress ? $propertyAddress->district_id : null,
                'platform_id' => $property->platform_id ?? 0,
                'is_peak_day' => 'No', // Default, would need peak calendar data to determine
                'peak_day_details' => null,
                'performance_tier' => $performanceTier,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Add booking data if exists
            if ($booking) {
                $record['booking_id'] = $booking->id;
                $record['booking_status'] = $booking->status;
                $record['booking_created_at'] = $booking->created_at;
                $record['booking_cancelled_at'] = $booking->status === 'Cancelled' ? $booking->updated_at : null;
                $record['checkin_date'] = $booking->start_date;
                $record['checkout_date'] = $booking->end_date;
                $record['user_id'] = $booking->user_id;
                if($booking->status === 'Accepted'){
                    $record['source_type'] = 'booking';
                }
                else{
                    $record['source_type'] = $this->determineSourceType($dateString, $propertyDates, $customPricing);
                }
            } else {
                // Determine source type based on availability data
                $record['source_type'] = $this->determineSourceType($dateString, $propertyDates, $customPricing);
            }

            $batchData[] = $record;
            $processedDates++;

            // Insert in batches to improve performance
            if (count($batchData) >= $batchSize) {
                $this->insertBatch($batchData);
                $batchData = [];
            }

            $currentDate->addDay();
        }

        // Insert any remaining records
        if (!empty($batchData)) {
            $this->insertBatch($batchData);
        }

        return [
            'processed' => $processedDates,
            'skipped' => $skippedDates
        ];
    }

    /**
     * Insert a batch of records using updateOrInsert to handle duplicates
     *
     * @param array $records
     * @return void
     */
    private function insertBatch($records)
    {
        foreach ($records as $record) {
            DB::table('master_availability')->updateOrInsert(
                [
                    'property_id' => $record['property_id'],
                    'date' => $record['date']
                ],
                $record
            );
        }
    }

    /**
     * Determine availability status from property_dates and custom_pricing
     *
     * @param string $dateString
     * @param \Illuminate\Support\Collection $propertyDates
     * @param \Illuminate\Support\Collection $customPricing
     * @return string
     */
    private function determineAvailabilityStatus($dateString, $propertyDates, $customPricing)
    {
        // Check property_dates first
        if (isset($propertyDates[$dateString])) {
            return $propertyDates[$dateString]->status === 'Available' ? 'available' : 'unavailable';
        }

        // Then check custom_pricing
        if (isset($customPricing[$dateString])) {
            return $customPricing[$dateString]->status === 'Available' ? 'available' : 'unavailable';
        }

        // Default to available
        return 'available';
    }

    /**
     * Determine source type based on availability data
     *
     * @param string $dateString
     * @param \Illuminate\Support\Collection $propertyDates
     * @param \Illuminate\Support\Collection $customPricing
     * @return string
     */
    private function determineSourceType($dateString, $propertyDates, $customPricing)
    {
        // Check property_dates first and use its type value
        if (isset($propertyDates[$dateString])) {
            // Map the type from property_dates to source_type in master_availability
            $type = $propertyDates[$dateString]->type;

            if ($type === 'calendar') {
                return 'host_calendar';
            } else if ($type === 'normal') {
                // For normal type, check if it's from admin or system
                // You might need to add more logic here based on your business rules
                return 'system';
            } else {
                // For any other type value
                return 'system';
            }
        }

        // Check custom_pricing
        if (isset($customPricing[$dateString])) {
            $type = $customPricing[$dateString]->type;

            if ($type === 'calendar') {
                return 'host_calendar';
            } else {
                return 'system';
            }
        }

        // Default to system
        return 'system';
    }

    /**
     * Find booking for a specific date
     *
     * @param Carbon $date
     * @param \Illuminate\Support\Collection $bookings
     * @return Bookings|null
     */
    private function findBookingForDate($date, $bookings)
    {
        foreach ($bookings as $booking) {
            $startDate = Carbon::parse($booking->start_date);
            $endDate = Carbon::parse($booking->end_date);

            if ($date->between($startDate, $endDate->subDay())) {
                return $booking;
            }
        }

        return null;
    }

    /**
     * Calculate performance tier based on property status and booking history
     *
     * @param Properties $property
     * @param int $successfulBookingsLast30Days
     * @return string
     */
    private function calculatePerformanceTier($property, $successfulBookingsLast30Days)
    {
        // Check if property is available (Listed and visible)
        $isAvailable = $property->status === 'Listed' && $property->visibility == 1;

        if (!$isAvailable) {
            return 'Low Performer';
        }

        if ($successfulBookingsLast30Days >= 2) {
            return 'High Performer';
        } else if ($successfulBookingsLast30Days > 0) {
            return 'Moderate Performer';
        } else {
            return 'Low Performer';
        }
    }
}