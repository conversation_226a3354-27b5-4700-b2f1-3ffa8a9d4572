<?php

/**
 * Bookings Model
 *
 * Bookings Model manages Bookings operation.
 *
 * @category   Bookings
 * @package    darent
 * <AUTHOR> Dev Team
 * @copyright Darent
 * @license
 * @version    2.7
 * @link       http://darent.com
 * @since      Version 1.3
 *
 */

namespace App\Models;

use App\Http\Helpers\Common;
use Illuminate\Database\Eloquent\Model;
use Session;
use DB;
use DateTime;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Facades\Log;
use App\Models\Payouts;
use App\Models\Penalty;
use App\Models\Currency;
use App\Models\Accounts;
use App\Models\PaymentTypes;
use OwenIt\Auditing\Contracts\Auditable;

class Bookings extends Model implements Auditable
{
    protected $table = 'bookings';
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    const ACCEPTED = 'Accepted';
    const CANCELLED = 'Cancelled';
    const PROCESSING = 'Processing';
    const DECLINED = 'Declined';
    const EXPIRED = 'Expired';
    const ADMIN = 'Admin';
    const GUEST = 'Guest';
    const HOST = 'Host';
    const WALLET_INVOICE_SUFFIX = '-wallet';
    const PAYMENT_STATUS_ERROR = 0;
    const PAYMENT_STATUS_SUCCESS = 1;
    const PAYMENT_STATUS_WALLET = 2;
    const PAY_BY_MOYASAR = 'moyasar';
    const PAY_BY_FATOORAH = 'Fatoorah';
    const PAY_BY_HYPERPAY = 'hyperpay';
    const NO_REFERAL_CODE = 'No Referal Code Applied';
    const NO_CASH_BACK_APPPLICABLE = 'No Cash Back Applicable';
    const CASH_BACK_SENT = 'Cash Back has been sent';


    protected $appends = ['host_payout', 'label_color', 'date_range', 'expiration_time', 'startdate_dmy', 'enddate_dmy', 'start_date_st', 'end_date_st', 'booking_period', 'with_or_without_discount', 'base_price_with_or_without_discount'];
    protected $guarded = [];

    protected $auditInclude = [
        'start_date',
        'end_date',
        'total',
        'total_with_discount',
        'use_forced_total',
        'forced_total',
    ];


    public function users()
    {
        return $this->belongsTo('App\Models\User', 'user_id', 'id')->withTrashed();
    }

    public function guest()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    public function booking_guest()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    public function host()
    {
        return $this->belongsTo('App\Models\User', 'host_id', 'id');
    }

    public function properties()
    {
        return $this->belongsTo('App\Models\Properties', 'property_id', 'id');
    }

    public function insurance()
    {
        return $this->hasOne('App\Models\BookingInsurance', 'booking_id', 'id');
    }

    public function payment_methods()
    {
        return $this->belongsTo('App\Models\PaymentMethods', 'payment_method_id', 'id');
    }

    public function booking_details()
    {
        return $this->hasMany('App\Models\BookingDetails', 'booking_id', 'id');
    }

    public function penalty()
    {
        return $this->hasMany('App\Models\Penalty', 'booking_id', 'id');
    }

    public function payouts()
    {
        return $this->hasMany('App\Models\Payouts', 'booking_id', 'id');
    }

    public function reviews()
    {
        return $this->hasMany('App\Models\Reviews', 'booking_id', 'id');
    }

    public function paymentDetails()
    {
        return $this->hasOne('App\Models\BookingPaymentDetails', 'booking_id', 'id');
    }

    public function review()
    {
        return $this->hasOne('App\Models\Reviews', 'booking_id', 'id');
    }

    public function messages()
    {
        return $this->hasMany('App\Models\Messages', 'booking_id', 'id');
    }

    public function transactions()
    {
        return $this->hasMany('App\Models\Transactions', 'record_id', 'id');
    }

    public function promoCodeUsage()
    {
        return $this->hasOne('App\Models\PromoCodeUsage', 'booking_id', 'id');
    }

    public function platform()
    {
        return $this->belongsTo('App\Models\Platform', 'platform_id', 'id');
    }

    public function travelAgent()
    {
        return $this->belongsTo('App\Models\TravelAgent', 'platform_by_id', 'id');
    }

    public function currency()
    {
        return $this->belongsTo('App\Models\Currency', 'currency_code', 'code');
    }

    public function bank()
    {
        return $this->belongsTo('App\Models\Bank');
    }

    public function tawuniya()
    {
        return $this->belongsTo(Tawuniya::class, 'id', 'booking_id');
    }


    public function paymentType()
    {
        return $this->belongsTo(PaymentTypes::class, 'payment_method_id');
    }

    public function getHostPenaltyAmountAttribute()
    {
        $amount = Penalty::where('booking_id', $this->attributes['id'])->where('user_type', 'Host')->sum('amount');
        return $this->currency_convert($amount);
    }

    public function getGuestPenaltyAmountAttribute()
    {
        $amount = Penalty::where('booking_id', $this->attributes['id'])->where('user_type', 'Guest')->sum('amount');
        return $this->currency_convert($amount);
    }

    //Host/Guest original Payouts
    public function getOriginalHostPayoutAttribute()
    {
        $payout = Payouts::where('user_id', $this->attributes['host_id'])->where('booking_id', $this->attributes['id'])->first();
        if (isset($payout->original_amount)) {
            return $payout->original_amount;
        } else {
            return $this->attributes['total'] - $this->attributes['service_charge'] - $this->attributes['host_fee'] - $this->attributes['iva_tax'] - $this->attributes['accomodation_tax'];
        }
    }

    public function getReferalCodeAttribute()
    {
        if ($this->attributes['referal_code']) {
            $property = Properties::find($this->attributes['property_id']);
            if (isset($property)) {
                $httpResponse = HostReferalCode::generateReferalLink($property);
                $jsonStart = strpos($httpResponse, '{');
                $jsonString = substr($httpResponse, $jsonStart);
                $responseData = json_decode($jsonString, true);
                if ($responseData && isset($responseData['data']['link'])) {
                    $link = $responseData['data']['link'];
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http";
                    $host = $_SERVER['HTTP_HOST']??'';
                    $base_url = $protocol . "://" . $host;

                    return $base_url . $link;
                }
            }
        }
    }

    public function getOriginalGuestPayoutAttribute()
    {
        $payout = Payouts::where('user_id', $this->attributes['user_id'])->where('booking_id', $this->attributes['id'])->first();
        if (isset($payout->original_amount)) {
            return $payout->original_amount;
        } else {
            return $this->attributes['total'] - $this->attributes['service_charge'];
        }
    }

    //Host/Guest payout
    public function getHostPayoutAttribute()
    {
        $payout = Payouts::where('user_id', $this->attributes['host_id'])->where('booking_id', $this->attributes['id'])->first();

        if (isset($payout->amount)) {
            return $payout->amount;
        } else {
            return $this->currency_adjust('total') - $this->currency_adjust('service_charge') - $this->currency_adjust('host_fee') - $this->currency_adjust('iva_tax') - $this->currency_adjust('accomodation_tax');
        }
    }

    public function getGuestPayoutAttribute()
    {
        $payout = Payouts::where('user_id', $this->attributes['user_id'])->where('booking_id', $this->attributes['id'])->first();

        if (isset($payout->amount)) {
            return $payout->amount;
        } else {
            return $this->currency_adjust('total');
        }
    }

    public function getIsCompletedAttribute()
    {
        return !$this->cancelled_at && !$this->expired_at && Carbon::parse($this->ended_at)->lte(now());
    }

    public function currency_adjust($field)
    {
        $default_currency = Currency::getAll()->where('default', 1)->first()->code;
        $rate = Currency::getAll()->where('code', $this->attributes['currency_code'] ?? $default_currency)->first()?->rate;


        $base_amount = $this->attributes[$field] / ($rate ?? 1);


        $session_rate = Currency::getAll()->where('code', $default_currency)->first()?->rate;

        return round($base_amount * ($session_rate ?? 1), 2);
    }

    public function currency_adjust_reports($field)
    {
        $rate = Currency::whereCode($this->attributes['currency_code'])->first()->rate;

        $base_amount = $this->attributes[$field] / $rate;

        $default_currency_rate = Currency::where('default', 1)->first()->rate;
        return round($base_amount * $default_currency_rate);
    }

    public function getCheckinCrossAttribute()
    {
        $date1 = date_create($this->attributes['start_date']);
        $date2 = date_create(date('Y-m-d'));
        if ($date2 < $date1) {
            return 1;
        } else {
            return 0;
        }
    }

    public function getCheckoutCrossAttribute()
    {
        $date1 = date_create($this->attributes['end_date']);
        $date2 = date_create(date('Y-m-d'));
        if ($date2 < $date1) {
            return 0;
        } else {
            return 1;
        }
    }


    public function getOriginalPerNightAttribute()
    {
        return $this->attributes['per_night'];
    }

    public function getOriginalCustomPriceDatesAttribute()
    {
        if ($this->attributes['custom_price_dates'] != null) {
            return json_decode($this->attributes['custom_price_dates'], true);
        } else {
            return [];
        }
    }

    public function getOriginalBasePriceAttribute()
    {
        return $this->attributes['base_price'];
    }

    public function getOriginalCleaningChargeAttribute()
    {
        return $this->attributes['cleaning_charge'];
    }

    public function getOriginalAccomodationTaxAttribute()
    {
        return $this->attributes['accomodation_tax'];
    }

    public function getOriginalIvaTaxAttribute()
    {
        return $this->attributes['iva_tax'];
    }

    public function getOriginalGuestChargeAttribute()
    {
        return $this->attributes['guest_charge'];
    }

    public function getOriginalServiceChargeAttribute()
    {
        return $this->attributes['service_charge'];
    }

    public function getOriginalSecurityMoneyAttribute()
    {
        return $this->attributes['security_money'];
    }

    public function getOriginalHostFeeAttribute()
    {
        return $this->attributes['host_fee'];
    }

    public function getOriginalTotalAttribute()
    {
        if ($this->hasForcedTotal()) {
            return $this->attributes['forced_total'];
        }
        return $this->attributes['total'];
    }

    public function getPerNightAttribute()
    {
        return $this->currency_adjust('per_night');
    }

    public function getBasePriceAttribute()
    {
        return $this->currency_adjust('base_price');
    }

    public function getCleaningChargeAttribute()
    {
        return $this->currency_adjust('cleaning_charge');
    }

    public function getGuestChargeAttribute()
    {
        return $this->currency_adjust('guest_charge');
    }

    public function getServiceChargeAttribute()
    {
        return $this->currency_adjust('service_charge');
    }

    public function getIvaTaxAttribute()
    {
        return $this->currency_adjust('iva_tax');
    }

    public function getAccomodationTaxAttribute()
    {
        return $this->currency_adjust('accomodation_tax');
    }

    public function getSecurityMoneyAttribute()
    {
        return $this->currency_adjust('security_money');
    }

    public function getHostFeeAttribute()
    {
        return $this->currency_adjust('host_fee');
    }


    public function getTotalAttribute()
    {
        $field = 'total';
        if ($this->hasForcedTotal()) {
            $field = 'forced_total';
        }
        return $this->currency_adjust($field);
    }

    public function hasForcedTotal(): bool
    {
        return $this->use_forced_total && $this->forced_total > 0;
    }

    public function getAmountsAttribute()
    {
        return $this->currency_adjust_reports('total');
    }

    public function getLabelColorAttribute()
    {
        if ($this->attributes['status'] == 'Accepted') {
            return 'success';
        } elseif ($this->attributes['status'] == 'Expired') {
            return 'info';
        } elseif ($this->attributes['status'] == 'Declined') {
            return 'info';
        } elseif ($this->attributes['status'] == 'Pending') {
            return 'warning';
        } elseif ($this->attributes['status'] == 'Cancelled') {
            return 'info';
        } elseif ($this->attributes['status'] == 'processing') {
            return 'secondary';
        } elseif ($this->attributes['status'] == '') {
            return 'inquiry';
        }

        return '';
    }

    public function getHostAccountAttribute()
    {
        $payout = Accounts::where('user_id', $this->attributes['host_id'])->where('selected', 'yes')->first();

        return (isset($payout->account) ? $payout->account : '');
    }

    public function getGuestAccountAttribute()
    {
        $payout = Accounts::where('user_id', $this->attributes['user_id'])->where('selected', 'yes')->first();
        return (isset($payout->account) ? $payout->account : '');
    }

    public function getCheckHostPayoutAttribute()
    {
        $exist = Payouts::where('booking_id', $this->attributes['id'])->where('user_type', 'Host')->where('status', 'Completed')->get();

        if ($exist->count()) {
            return 'yes';
        } else {
            return 'no';
        }
    }

    public function getGuestPayoutIdAttribute()
    {
        $payout = Payouts::where('user_id', $this->attributes['user_id'])->where('booking_id', $this->attributes['id'])->first();
        return $payout->id;
    }

    public function getHostPayoutIdAttribute()
    {
        $payout = Payouts::where('user_id', $this->attributes['host_id'])->where('booking_id', $this->attributes['id'])->first();
        return $payout->id;
    }

    public function getCheckGuestPayoutAttribute()
    {
        $exist = Payouts::where('booking_id', $this->attributes['id'])->where('user_type', 'Guest')->where('status', 'Completed')->get();

        if ($exist->count()) {
            return 'yes';
        } else {
            return 'no';
        }
    }

    public function getOriginalAdminHostPaymentAttribute()
    {
        $exist = Payouts::where('user_id', $this->attributes['host_id'])->where('booking_id', $this->attributes['id'])->get();

        if ($exist->count()) {
            return $exist[0]->original_amount;
        } else {
            return 0;
        }
    }

    public function getOriginalAdminGuestPaymentAttribute()
    {
        $exist = Payouts::where('user_id', $this->attributes['user_id'])->where('booking_id', $this->attributes['id'])->get();

        if ($exist->count()) {
            return $exist[0]->original_amount;
        } else {
            return 0;
        }
    }

    public function getAdminHostPaymentAttribute()
    {
        $exist = Payouts::where('user_id', $this->attributes['host_id'])->where('booking_id', $this->attributes['id'])->get();

        if ($exist->count()) {
            return $exist[0]->amount;
        } else {
            return 0;
        }
    }

    public function getAdminGuestPaymentAttribute()
    {
        $exist = Payouts::where('user_id', $this->attributes['user_id'])->where('booking_id', $this->attributes['id'])->get();

        if ($exist->count()) {
            return $exist[0]->amount;
        } else {
            return 0;
        }
    }

    public function currency_convert($amount)
    {
        $rate = Currency::whereCode($this->attributes['currency_code'])->first()->rate;

        $base_amount = $amount / $rate;

        $default_currency = Currency::where('default', 1)->first()->code;

        $session_rate = Currency::whereCode((Session::get('currency')) ? Session::get('currency') : $default_currency)->first()->rate;

        return round($base_amount * $session_rate);
    }


    public function getStartdateDmyAttribute()
    {
        $start_date = date('D, F d, Y', strtotime($this->attributes['start_date']));
        return $start_date;
    }

    public function getStartDateStAttribute()
    {
        return date('d M Y', strtotime($this->attributes['start_date']));
    }

    public function getEndDateStAttribute()
    {
        return date('d M Y', strtotime($this->attributes['end_date']));
    }


    public function getEnddateDmyAttribute()
    {
        $end_date = date('D, F d, Y', strtotime($this->attributes['end_date']));
        return $end_date;
    }

    public function getStartdateMdAttribute()
    {
        $start_date = date('M d', strtotime($this->attributes['start_date']));
        return $start_date;
    }


    public function getEnddateMdAttribute()
    {
        $end_date = date('M d', strtotime($this->attributes['end_date']));
        return $end_date;
    }

    public function getDateRangeAttribute()
    {
        return date('M d', strtotime($this->attributes['start_date'])) . ' - ' . date('d, Y', strtotime($this->attributes['end_date']));
    }


    public function getBookingPeriodAttribute()
    {

        $startDate = Carbon::createFromFormat('Y-m-d', $this->attributes['start_date']);
        $endDate = Carbon::createFromFormat('Y-m-d', $this->attributes['end_date']);

        $formattedDateRange = $startDate->format('M d') . ' - ' . $endDate->format('M d');

        return $formattedDateRange; // Output: Sep 01 - Sep 06
    }

    public function getExpirationTimeAttribute()
    {
        // $expired_at =  date('Y/m/d H:i:s', strtotime(str_replace('-', '/', $this->attributes['created_at']) . ' +5 hour'));
        // return $expired_at;
        $createdAt = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $this->attributes['created_at']);;

        $userTimezone = $this->users->timezone;

        // Add 30 minutes to the created_at attribute
        $expiredAt = $createdAt->addMinutes(30)->timezone($userTimezone);
        // dd($expiredAt->format('Y/m/d H:i:s'));
        // Return the expiration time in the desired format
        return $expiredAt->format('Y/m/d H:i:s');
    }

    public function getBookingLists()
    {
        $excludedReceiverIds = [5869, 6659];
        $bookings = Bookings::join('properties', function ($join) {
            $join->on('properties.id', '=', 'bookings.property_id');
        })
            ->join('users', function ($join) {
                $join->on('users.id', '=', 'bookings.user_id');
            })
            ->join('currency', 'currency.code', '=', 'bookings.currency_code')
            ->leftJoin('users as u', function ($join) {
                $join->on('u.id', '=', 'bookings.host_id');
            })
            ->select(['bookings.id as id', 'u.first_name as host_name', 'users.first_name as guest_name', 'bookings.property_id as property_id', 'properties.name as property_name', \DB::raw('CONCAT(bookings.total) AS total_amount'), 'bookings.status', 'bookings.created_at as created_at', 'bookings.updated_at as updated_at', 'bookings.start_date', 'bookings.end_date', 'bookings.guest', 'bookings.host_id', 'bookings.user_id', 'bookings.total', 'bookings.currency_code', 'currency.symbol', 'bookings.service_charge', 'bookings.host_fee'])
            ->latest('bookings.created_at')
            ->whereNotIn('bookings.user_id', $excludedReceiverIds)
            ->take(5)
            ->get();


        // $bookings->whereNotIn('bookings.user_id', $excludedReceiverIds);

        return $bookings;
    }

    public function getReviewDaysAttribute()
    {
        $start_date = $this->attributes['end_date'];
        $end_date = date('Y-m-d', strtotime($this->attributes['end_date'] . ' +14 days'));

        $datetime1 = new DateTime(date('Y-m-d'));
        $datetime2 = new DateTime($end_date);
        $interval = $datetime1->diff($datetime2);
        $days = $interval->format('%R%a');
        return $days + 1;
    }

    public function review_user($id)
    {
        if ($this->attributes['user_id'] == $id) {
            $user_id = $this->attributes['host_id'];
        } else {
            $user_id = $this->attributes['user_id'];
        }

        return User::find($user_id);
    }

    public function review_details($id)
    {
        return Reviews::find($id);
    }

    public function getAttachmentAttribute()
    {
        if (!$this->attributes['attachment']) {
            return null;
        }
        return asset('uploads/booking/' . $this->attributes['attachment']);
    }


    public function discount_usage()
    {
        return $this->hasOne(DiscountUsage::class, 'booking_id', 'id');
    }


    public function payment_type()
    {
        return $this->hasOne(PaymentTypes::class, 'id', 'payment_type_id');
    }

    public function getAllHostBookings()
    {
        $booking = Bookings::where('host_id', Auth::guard('api')->user()->id);
        return $booking;
    }

    public function getCheckOutHostBookings()
    {
        $booking = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->where('status', 'Accepted')
            ->where('end_date', '=', Carbon::now()->format('Y-m-d'));
        return $booking;
    }

    public function getOnGoingHostBookings()
    {
        $booking = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
            ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
            ->where('status', 'Accepted');
        return $booking;
    }

    public function getArivingHostBookings()
    {
        $booking = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->where(function ($query) {
                $query->where('start_date', Carbon::now()->addDays(1)->format('Y-m-d'))
                    ->orWhere('start_date', Carbon::now()->addDays(2)->format('Y-m-d'));
            })
            ->where('status', 'Accepted');
        return $booking;
    }

    public function getPendingReviewsHostBookings()
    {
        $booking = Bookings::where('host_id', Auth::user()->id)
            ->where('end_date', '<', Carbon::now()->format('Y-m-d'))
            ->where('status', 'Accepted')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('reviews')
                    ->whereColumn('reviews.booking_id', 'bookings.id')
                    ->where('reviews.sender_id', Auth::user()->id); // Adjust the sender_id as needed
            })->WhereExists(function ($booking) {
                $booking->select(DB::raw(1))
                    ->from('reviews')
                    ->whereColumn('reviews.sender_id', 'bookings.user_id');
            });
        return $booking;
    }

    public function getUpComingHostBookings()
    {
        $booking = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where('end_date', '>', Carbon::now()->format('Y-m-d'))
            ->where('status', 'Accepted');
        return $booking;
    }

    public function getHistoryHostBookings()
    {
        $booking = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')));
        return $booking;
    }

    public function getExpiredHostBookings()
    {
        $booking = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where('status', 'Expired');
        return $booking;
    }

    public function getCancelledHostBookings()
    {
        $booking = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->whereIn('status', ['Cancelled', 'Declined']);
        return $booking;
    }

    public function getWithOrWithoutDiscountAttribute()
    {
        // return $this->attributes['total'];
        return $this->attributes['total_with_discount'] ? $this->attributes['total_with_discount'] : $this->attributes['total'];
    }

    public function getBasePriceWithOrWithoutDiscountAttribute()
    {
        // return $this->attributes['base_price'];
        return $this->attributes['base_price_with_discount'] != 0 ? $this->attributes['base_price_with_discount'] : $this->attributes['base_price'];
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', self::ACCEPTED)->get();
    }


    protected static function booted()
    {
        parent::booted();
        if (app()->environment('prod') && strpos(request()->getRequestUri(), '/admin') === 0) {
            // if (2==1) {
            // Check if "debug" is present in the URL

            $debugAdmin = request()->query('debug') !== null;
            static::addGlobalScope('excludeReceivers', function ($query) use ($debugAdmin) {
                $excludedReceiverIds = [
                    42, 501, 517, 5344, 5432, 5434, 5435, 5534, 5551,
                    5603, 465,
                    59, 68, 70, 84, 173, 252, 457, 5351, 5394, 5418, 5493, 5553, 5557, 5632, 5675,
                    507, 508, 5303, 5776, 5431, 5745, 5775, 5836, 5841,
                    5569, 5588, 5614, 5747, 5815, 5819, 5820, 5833, 5569, 50, 5867, 5846, 5700, 5538, 5672, 5564, 5657,
                    5835, 5700, 5849, 5860, 5650, 5831,
                    5856, 9896,
                    5869, 6698, 6693, 12502, 13256,
                    6659
                    // 5828
                    //  5829
                    // Abdullah LOCAL Ids
                    // 37, 487, 521, 538, 515, 5831,5828,9893
                    //5828
                ];

                $excludedRnewIds = [
                    42, 501, 517, 5344, 5432, 5434, 5435, 5534, 5551,
                    5603, 465,
                    59, 68, 70, 84, 173, 252, 457, 5351, 5394, 5418, 5493, 5553, 5557, 5632, 5675,
                    507, 508, 5303, 5776, 5431, 5745, 5775, 5836, 5841,
                    5569, 5588, 5614, 5747, 5815, 5819, 5820, 5833, 5569, 50, 5867, 5846, 5700, 5538, 5672, 5564, 5657,
                    5835, 5700, 5849, 5860, 5650, 5831,

                    // 5828
                    //  5829
                    // Abdullah LOCAL Ids
                    // 37, 487, 521, 538, 515, 5831,5828
                    //5828
                ];


                //    $adminId = 1;
                if (!$debugAdmin) {
                    $query->whereNotIn('bookings.user_id', $excludedReceiverIds);
                }
                // $query->whereNotIn('bookings.user_id', $excludedReceiverIds);
                // ->whereNotIn('bookings.host_id', $excludedRnewIds);
            });


        }
        // static::created(function ($booking) {
        //     if ($booking->booking_type == 'request') {
        //         (new Common())->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
        //     }
        // });
        static::updated(function ($booking) {
            $changedAttributes = $booking->getDirty();
            $booking->syncChanges();
            if (($booking->booking_type == 'instant' && $booking->attributes['status'] == 'Accepted') || $booking->booking_type == 'request') {
                (new Common())->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
            }
            // $changedAttributesWithValues = [];
            // foreach ($changedAttributes as $attribute => $value) {
            //     $changedAttributesWithValues[$attribute] = [
            //         'old' => $booking->getOriginal($attribute),
            //         'new' => $booking->getAttribute($attribute),
            //     ];
            // }
            // $oldValues = $booking->getOriginal();
            // $newValues = $booking->getAttributes();
            // $log = new Log();
            // // $log->user_id = auth()->id();
            // if (Auth::guard('admin')->check()) {
            //     $log->user_id = Auth::guard('admin')->user()->id;
            //     $log->change_by = Auth::guard('admin')->user()->user_name;
            // } elseif (Auth::guard('web')->check()) {
            //     $log->user_id = Auth::guard('web')->user()->id;
            //     $log->change_by =  Auth::guard('web')->user()->first_name . ' ' . Auth::guard('web')->user()->last_name;
            // } elseif (Auth::guard('api')->check()) {
            //     $log->user_id = Auth::guard('api')->user()->id;
            //     $log->change_by =  Auth::guard('api')->user()->first_name . ' ' . Auth::guard('api')->user()->last_name;
            // }
            // // $log->booking_id = $booking->id;
            // // $log->action_type = 'updated';
            // $log->old = json_encode($oldValues);
            // $log->new = json_encode($newValues);
            // $log->change_values = json_encode($changedAttributesWithValues);
            // // $log->change_values = json_encode(array_keys($changedAttributes));
            // $booking->logs()->save($log);
        });
    }


    public function propertyPrice()
    {
        return $this->belongsTo(PropertyPrice::class, 'property_id', 'property_id');
    }
}
