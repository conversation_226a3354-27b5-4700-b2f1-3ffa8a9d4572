<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PropertyManagementRequest extends Model
{
    protected $fillable = [
        'user_id',
        'owner_name',
        'email',
        'property_name',
        'units',
        'location',
        'latitude',
        'longitude',
        'street',
        'route',
        'country',
        'state',
        'postal_code',
        'city_id',
        'contact_no',
    ];

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function city(){
        return $this->belongsTo(City::class);
    }
}
