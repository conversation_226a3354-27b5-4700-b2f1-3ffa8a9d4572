<?php

/**
 * User Model
 *
 * User Model manages User operation.
 *
 * @category   User
 * @package    darent
 * <AUTHOR> Dev Team
 * @copyright Darent
 * @license
 * @version    2.7
 * @link       http://darent.com
 * @since      Version 1.3
 *
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\UserDetails;
use App\Models\AccountDeleteRequest;
use App\Models\UserDocument;
use App\Models\Reviews;
use Carbon\Carbon;
use Laravel\Passport\HasApiTokens;
use Illuminate\Support\Facades\DB;

class User extends Authenticatable
{
    use HasFactory;

    const GUEST = 1;
    const HOST = 2;
    use HasApiTokens, Notifiable, SoftDeletes;

    protected $guarded = ['id'];


    protected $hidden = [
        'password',
        'remember_token',
    ];
    public $timestamps = true;

    protected $appends = [
        'profile_src',
        'is_host',
        'date_of_birth',
        'default_card',
        'verified_cards',
        'user_rating',
        'is_elm_verified',
        'wallet_id'
    ];

    public static function permission_user($id)
    {
        return DB::table('permission_users')->where('user_id', $id)->pluck('permission_id');
    }

    public function users_verification()
    {
        return $this->hasOne('App\Models\UsersVerification', 'user_id', 'id');
    }

    public function payouts()
    {
        return $this->hasMany('App\Models\Payouts', 'user_id', 'id');
    }

    public function accounts()
    {
        return $this->hasMany('App\Models\Accounts', 'user_id', 'id');
    }

    public function bookings()
    {
        return $this->hasMany('App\Models\Bookings', 'user_id', 'id');
    }

    public function reports()
    {
        return $this->hasMany('App\Models\Reports', 'user_id', 'id');
    }

    public function user_details()
    {
        return $this->hasMany('App\Models\UserDetails', 'user_id', 'id');
    }

    public function user_cards()
    {
        return $this->hasMany('App\Models\UserCards', 'user_id', 'id');
    }

    public function user_transactions()
    {
        return $this->hasMany('App\Models\Transactions', 'user_id', 'id');
    }

    public function payments()
    {
        return $this->hasMany('App\Models\Payment', 'user_id', 'id');
    }

    public function withdraw()
    {
        return $this->hasMany('App\Models\Withdrawal', 'user_id', 'id');
    }

    public function properties()
    {
        return $this->hasMany('App\Models\Properties', 'host_id', 'id');
    }

    public function propertiesCount()
    {
        return $this->hasMany('App\Models\Properties', 'host_id', 'id')->count();
    }

    public function propertyview()
    {
        return $this->hasMany('App\Models\PropertyView', 'host_id', 'id');
    }

    public function customersupport()
    {
        return $this->hasMany('App\Models\CustomerSupport', 'user_id', 'id');
    }

    public function logs()
    {
        return $this->morphMany(Log::class, 'loggable');
    }

    public function reviews()
    {
        $excludedReceiverIds = [
            42,
            501,
            517,
            5344,
            5432,
            5434,
            5435,
            5534,
            5551,
            404,
            5284,
            5455,
            5603,
            5637,
            465,
            59,
            68,
            70,
            84,
            173,
            252,
            457,
            5351,
            5394,
            5418,
            5493,
            5553,
            5557,
            5632,
            5675,
            507,
            508,
            5303,
            5776,
            5431,
            5745,
            5775,
            5836,
            5841,
            5569,
            5588,
            5614,
            5747,
            5815,
            5819,
            5820,
            5829,
            5833,
            5569,
            50
            // Abdullah LOCAL Ids
            // 37, 487, 521, 538, 515
        ];

        return $this->hasMany('App\Models\Reviews', 'sender_id', 'id')
            ->whereNotIn('receiver_id', $excludedReceiverIds);


        // return $this->hasMany('App\Models\Reviews', 'sender_id', 'id');
    }
    // public function hostreviews()
    // {
    //     return $this->hasMany('App\Models\Reviews', 'receiver_id', 'id');
    // }

    public function hostreviews()
    {
        $excludedReceiverIds = [
            42,
            501,
            517,
            5344,
            5432,
            5434,
            5435,
            5534,
            5551,
            404,
            5284,
            5455,
            5603,
            5637,
            465,
            59,
            68,
            70,
            84,
            173,
            252,
            457,
            5351,
            5394,
            5418,
            5493,
            5553,
            5557,
            5632,
            5675,
            507,
            508,
            5303,
            5776,
            5431,
            5745,
            5775,
            5836,
            5841,
            5569,
            5588,
            5614,
            5747,
            5815,
            5819,
            5820,
            5829,
            5833,
            5569,
            50
            // Abdullah LOCAL Ids
            // 37, 487, 521, 538, 515

        ];

        return $this->hasMany('App\Models\Reviews', 'receiver_id', 'id')
            ->whereNotIn('sender_id', $excludedReceiverIds);
    }


    public function otps()
    {
        return $this->hasMany('App\Models\Otp');
    }

    public function documents()
    {
        return $this->hasMany('App\Models\UserDocument', 'user_id');
    }

    public function getIsHostAttribute()
    {
        $isHost = $this->properties->count() > 0 ? true : false;
        unset($this->properties);
        return $isHost;
    }

    public function newUserWallet()
    {
        return $this->hasOne(NewUserWallet::class, 'user_id');
    }


    public function getWalletIdAttribute()
    {
        if (!$this->relationLoaded('newUserWallet')) {
            $this->load('newUserWallet');
        }
        return $this->newUserWallet ? $this->newUserWallet->id : null;
    }

    public function generateOtp()
    {
        $otp = $this->otps();
        $otp->where('is_used', 0)->where('is_closed', 0)->update(['is_closed' => 1]);
        return $otp->create([
            'code' => $this->generateCode(),
            'expires_at' => Carbon::now()->addMinutes(60)
        ]);
    }

    function generateCode()
    {
        $code = mt_rand(1000, 9999);
        if ($this->otps()->where('code', $code)->where('is_used', false)->where('is_closed', false)->exists()) {
            return $this->generateCode();
        }
        return $code;
    }


    public function getProfileSrcAttribute()
    {
        $profileImage = $this->attributes['profile_image'] ?? null;
        $image = 'icons/user.svg';

        if ($profileImage != null) $image = 'storage/public/images/profile/' . $profileImage;
        if (app()->environment() == 'prod') if ($profileImage != null) $image = 'public/images/profile/' . $profileImage;

        return $image;
    }

    public function details_key_value()
    {
        $details = UserDetails::where('user_id', $this->attributes['id'])->pluck('value', 'field');
        return $details;
    }

    public function getAccountSinceAttribute()
    {

        $month = date('F', strtotime($this->attributes['created_at']));
        $year = date('Y', strtotime($this->attributes['created_at']));

        if (app()->getLocale() == 'ar') {
            $ar_year = digitsToArabic($year);
            $ar_month = arabicMonth(strtotime($this->attributes['created_at']));
            return $ar_month . ' ' . $ar_year;
        } else {
            return date('F Y', strtotime($this->attributes['created_at']));
        }
    }

    public function getFullNameAttribute()
    {
        $full_name = ucfirst($this->attributes['first_name']) . ' ' . ucfirst($this->attributes['last_name']);
        return ($this->attributes['first_name'] && $this->attributes['last_name']) == '' ? 'User' : $full_name;
    }


    public function getGenderAttribute()
    {
        $gender = $this->attributes['gender'];
        // $gender = $this->user_details->where('field', 'gender')->first()->value ?? null;
        // unset($this->user_details);
        return $gender;
    }

    public function getDefaultCardAttribute()
    {
        $defaultCard = $this->user_cards->where('is_default', 1)->first() ?? null;
        return $defaultCard;
    }

    public function getVerifiedCardsAttribute()
    {
        $verifiedCards = $this->user_cards->where('is_verified', 1) ?? null;
        return $verifiedCards;
    }

    public function getLocationAttribute()
    {
        $location = $this->attributes['location'];
        // $location = $this->user_details->where('field', 'live')->first()->value ?? null;
        // unset($this->user_details);
        return $location;
    }

    public function getAboutAttribute()
    {
        $about = $this->attributes['about'];
        // $about = $this->user_details->where('field', 'about')->first()->value ?? null;
        // unset($this->user_details);
        return $about;
    }

    public function getDateOfBirthAttribute()
    {
        $dob = isset($this->attributes['date_of_birth']) ? $this->attributes['date_of_birth'] : null;
        // $dob = $this->user_details->where('field', 'date_of_birth')->first()->value ?? null;
        // unset($this->user_details);
        return $dob;
    }

    public function getUserRatingAttribute()
    {
        return Reviews::where('receiver_id', $this->attributes['id'])->selectRaw('CAST(AVG(rating) AS DECIMAL(10,2)) as avgRating')->first()->avgRating ?? 0;
    }

    function elmDocument()
    {
        return $this->hasOne(ElmDocument::class);
    }

    public function AccountDeleteRequest()
    {
        return $this->hasOne(AccountDeleteRequest::class);
    }

    public function wallet()
    {
        return $this->hasOne(NewUserWallet::class);
    }

    function getIsElmVerifiedAttribute()
    {
        if (!$this->relationLoaded('elmDocument')) {
            $this->load('elmDocument');
        }
        return !!$this->elmDocument()->whereDate('verified_till', '>', now())->exists();
    }

    public function getRelationsList(): array
    {
        return [
            'users_verification' => 'hasOne',
            'payouts' => 'hasMany',
            'accounts' => 'hasMany',
            'bookings' => 'hasMany',
            // 'reports' => 'hasMany',
            'user_details' => 'hasMany',
            'user_cards' => 'hasMany',
            'user_transactions' => 'hasMany',
            // 'payments' => 'hasMany',
            // 'withdraw' => 'hasMany',
            'customersupport' => 'hasMany',
            'otps' => 'hasMany',
            'documents' => 'hasMany',
            'elmDocument' => 'hasOne',
            'AccountDeleteRequest' => 'hasOne',
            'wallet' => 'hasOne',
            'hostreviews' => 'hasMany',
            'reviews' => 'hasMany',
            'newUserWallet' => 'hasOne',
            // Add any other relations you want to check/delete
        ];
    }
}
