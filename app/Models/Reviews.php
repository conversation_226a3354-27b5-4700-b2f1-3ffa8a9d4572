<?php

/**
 * Reviews Model
 *
 * Reviews Model manages Reviews operation.
 *
 * @category   Reviews
 * @package    darent
 * <AUTHOR> Dev Team
 * @copyright Darent
 * @license
 * @version    2.7
 * @link       http://darent.com
 * @since      Version 1.3
 *
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Reviews extends Model
{
    protected $table = 'reviews';
    protected $guarded = ['id'];
    protected $casts = [
        'cleanliness_message' => 'array',
        'communication_message' => 'array'
    ];

    public function users()
    {
        return $this->belongsTo('App\Models\User', 'sender_id', 'id');
    }


    public function users_from()
    {
        return $this->belongsTo('App\Models\User', 'receiver_id', 'id');
    }

    public function properties()
    {
        return $this->belongsTo('App\Models\Properties', 'property_id', 'id');
    }

    public function bookings()
    {
        return $this->belongsTo('App\Models\Bookings', 'booking_id', 'id');
    }

    public function getDateFyAttribute()
    {
        return date('F Y', strtotime($this->attributes['updated_at']));
    }


    public function getHiddenReviewAttribute()
    {
        $booking_id = $this->attributes['booking_id'];
        $sender_id = $this->attributes['sender_id'];
        $receiver_id = $this->attributes['receiver_id'];
        $check = Reviews::where(['sender_id' => $receiver_id, 'receiver_id' => $sender_id, 'booking_id' => $booking_id])->get();
        if ($check->count()) {
            return false;
        } else {
            return true;
        }
    }

    function scopeReviewsList($query, $type, $property_id = null)
    {
        $isGuest = $type == 'guest';
        $by = $isGuest ? 'sender_id' : 'receiver_id';
        return $query->selectRaw('
        reviews.id,
        rating,
        review_date,
        reviews.message,
        reviews.darent_message,
        reviews.cleanliness,
        reviews.location,
        reviews.communication,
        reviews.accuracy,
        reviews.darent_service,
        reviews.darent_recomended,
        reviews.isPublic AS is_public,
        DATE_FORMAT(reviews.created_at, "%d %M %Y") AS created_at,
        us.id AS reviewer_id,
        CONCAT(us.first_name," ",us.last_name) AS reviewer_name,
        us.profile_image as reviewer_image' . (!$property_id ? ' , ps.id AS property_id, ps.name as property_name,
        pps.photo AS property_image' : ''))
            ->join('users AS us', 'us.id', $by)
            ->where('reviewer', $type)->when(
                !!$property_id,
                fn($q) => $q->where('property_id', $property_id),
                fn($q) => $q->join('properties AS ps', 'ps.id', 'reviews.property_id')
                    ->join('property_photos AS pps', fn($q1) => $q1->on('pps.property_id', 'ps.id')->where('cover_photo', 1))
            )
            ->when($isGuest, fn($q) => $q->where('ispublic', true));
    }

    // function scopeAvgs($query, $type, $property_id = null, $auth_id = null)
    // {
    //     if (!$property_id && !$auth_id) {
    //         throw new \Exception("Both property and auth id cannot be null.");
    //     }
    //     $entity = 'property_id';
    //     $entity_id = $property_id;
    //     if (!$property_id) {
    //         $entity = 'receiver_id';
    //         $entity_id = $auth_id;
    //     }
    //     $isGuest = $type == 'guest';
    //     return $query->selectRaw('IFNULL(ROUND(AVG(cleanliness)/2,1),0) AS cleanliness, IFNULL(ROUND(AVG(location)/2,1),0) AS location, IFNULL(ROUND((AVG(cleanliness)/2 + AVG(location)/2)/2, 1),0) AS avg_total , COUNT(1) as total_reviews')
    //         ->where('reviewer', $type)
    //         ->when($isGuest, fn ($q) => $q->where('ispublic', true))
    //         ->where($entity, $entity_id)
    //         ->first();
    // }

    function scopeAvgs($query, $type, $property_id = null, $auth_id = null)
    {
        if (!$property_id && !$auth_id) {
            throw new \Exception("Both property and auth id cannot be null.");
        }
        $entity = 'property_id';
        $entity_id = $property_id;
        if (!$property_id) {
            $entity = 'receiver_id';
            $entity_id = $auth_id;
        }
        $isGuest = $type == 'guest';
        // IFNULL(ROUND(AVG(darent_service),1),0) AS darent_service,
        // IFNULL(ROUND(AVG(darent_recomended),1),0) AS darent_recomended,
         return $query->selectRaw('
        IFNULL(ROUND(AVG(cleanliness),1),0) AS cleanliness,
        IFNULL(ROUND(AVG(location),1),0) AS location,
        IFNULL(ROUND(AVG(accuracy),1),0) AS accuracy,
        IFNULL(ROUND(AVG(communication),1),0) AS communication,
        IFNULL(ROUND(AVG(rating),1), 0) AS avg_total,
        COUNT(*) as total_reviews')
            ->where('reviewer', $type)
            ->when($isGuest, fn($q) => $q->where('ispublic', true))
            ->where($entity, $entity_id)
            ->first();

    }

    public function logs()
    {
        return $this->morphMany(Log::class, 'loggable');
    }
}
