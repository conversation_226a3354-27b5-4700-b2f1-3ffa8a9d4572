<?php

/**
 * Properties Model
 *
 * Properties Model manages Properties operation.
 *
 * @category   Properties
 * @package    darent
 * <AUTHOR> Dev Team
 * @copyright Darent
 * @license
 * @version    2.7
 * @link       http://darent.com
 * @since      Version 1.3
 *
 */

namespace App\Models;

use App\Helpers\ImageHelper;
use App\Http\Helpers\Common;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\PropertyPhotos;
use App\Models\PropertyDates;
use App\Models\MasterAvailability;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class Properties extends Model
{
    use HasFactory;

    protected $table = 'properties';
    public $timestamps = true;
    use SoftDeletes;

    protected $fillable = ['license_no','cr_no','license_is_company','license_expiry','license_company_name','license_verified_at','bedrooms',
    'beds',
    'single_beds',
    'double_beds',
    'bathrooms',
    'bed_type',
    'property_type',
    'space_type',
    'accommodates',
    'adult_guest',
    'children_guest',
    'priority',
    'recomended',
    'no_of_appartment'];
    const COMPLETE_PROPERTY_STEP = 0;
    const DISTANCE_IN_KM = 0.1;
    const BEARING_IN_DEGREES = 45;
    const PROPERTY_NOT_FOUNT = 'Property does not exist';
    const DEAFULT_COUNTRY = 'saudi arabia';
    const DEAFULT_COUNTRY_CODE = 'SA';
    const SAUDIA_ARAB_LAT = 23.8859;
    const SAUDIA_ARAB_LONG = 45.0792;
    const LAT_LONG_INCREMENT = 0.35;
    const LAT_LONG_INCREMENT_FIXED = 1100;
    const PER_PAGE = 8;
    const LISTED = 'Listed';
    const UNLISTED = 'Unlisted';
    const APPARTMENT_TYPE_ID = 1;
    const VILLA_TYPE_ID = 9;
    const VISIBLE = 1;
    const HIDDEN = 0;
    const CREATE_IN_DB = 1;
    const CREATE_IN_CACHE = 0;
    const SEARCH_COLUMNS_WEB = [
        'properties.*',
        'property_description.id as property_description_id',
        'property_description.summary as property_description_summary',
        'property_description.summary_ar as property_description_summary_ar',
        'property_description.place_is_great_for as property_description_place_is_great_for',
        'property_description.about_place as property_description_about_place',
        'property_description.guest_can_access as property_description_guest_can_access',
        'property_address.id as property_address_id',
        'property_address.address_line_1 as property_address_address_line_1',
        'property_address.address_line_2 as property_address_address_line_2',
        'property_address.latitude as property_address_latitude',
        'property_address.longitude as property_address_longitude',
        'property_address.city as property_address_city',
        'property_address.city_ar as property_address_city_ar',
        'property_address.district as property_address_district',
        'property_address.district_ar as property_address_district_ar',
        'property_address.state as property_address_state',
        'property_address.country as property_address_country',
        'cities.name as city_name',
        'cities.name_ar as city_name_ar',
        'property_address.postal_code as property_address_postal_code',
        'users.id as user_id',
        'property_price.id as property_price_id',
        'property_price.cleaning_fee as property_price_cleaning_fee',
        'property_price.guest_after as property_price_guest_after',
        'property_price.guest_fee as property_price_guest_fee',
        'property_price.security_fee as property_price_security_fee',
        'property_price.price as property_price_price',
        'property_price.weekend_price as property_price_weekend_price',
        'property_price.weekly_discount as property_price_weekly_discount',
        'property_price.monthly_discount as property_price_monthly_discount',
        'property_price.special_days_price as property_price_special_days_price',
        'property_price.currency_code as property_price_currency_code',
        'property_type.id as property_type_id',
        'property_type.name as property_type_name',
        'property_type.name_ar as property_type_name_ar',
        'property_type.description as property_type_description',
        'property_type.icon_image as property_type_icon_image',
        'property_type.status as property_type_status',
        'property_discounts.discount as property_discounts_discount',
        'scoring_system.total_score as property_score_total_points'
    ];
    const SEARCH_COLUMNS_WEB_OPTIMIZED = [
        'properties.*',
        'master_availability.determine_price as determine_price',
        'property_description.id as property_description_id',
        'property_description.summary as property_description_summary',
        'property_description.summary_ar as property_description_summary_ar',
        'property_description.place_is_great_for as property_description_place_is_great_for',
        'property_description.about_place as property_description_about_place',
        'property_description.guest_can_access as property_description_guest_can_access',
        'property_address.id as property_address_id',
        'property_address.address_line_1 as property_address_address_line_1',
        'property_address.address_line_2 as property_address_address_line_2',
        'property_address.latitude as property_address_latitude',
        'property_address.longitude as property_address_longitude',
        'property_address.city as property_address_city',
        'property_address.city_ar as property_address_city_ar',
        'property_address.district as property_address_district',
        'property_address.district_ar as property_address_district_ar',
        'property_address.state as property_address_state',
        'property_address.country as property_address_country',
        'cities.name as city_name',
        'cities.name_ar as city_name_ar',
        'property_address.postal_code as property_address_postal_code',
        'users.id as user_id',
        'property_type.id as property_type_id',
        'property_type.name as property_type_name',
        'property_type.name_ar as property_type_name_ar',
        'property_type.description as property_type_description',
        'property_type.icon_image as property_type_icon_image',
        'property_type.status as property_type_status',
        'scoring_system.total_score as property_score_total_points'
    ];
    const SEARCH_COLUMNS_API = [
        'properties.*',
        'property_description.id as property_description_id',
        'property_description.summary as property_description_summary',
        'property_description.summary_ar as property_description_summary_ar',
        'property_address.latitude as property_address_latitude',
        'property_address.longitude as property_address_longitude',
        'property_address.city as property_address_city',
        'property_address.city_ar as property_address_city_ar',
        'property_address.district as property_address_district',
        'property_address.state as property_address_state',
        'property_type.name as property_type_name',
        'property_type.name_ar as property_type_name_ar',
        'users.id as user_id',
        'property_price.price as property_price_price',
        'property_discounts.discount as property_discounts_discount',
        'scoring_system.total_score as property_score_total_points'
    ];

    protected $guarded = [];

    protected $appends = [
        'steps_completed',
        'images',
        'space_type_name',
        'property_type_name',
        'property_photo',
        'host_name',
        'book_mark',
        'wishlist',
        'reviews_count',
        'overall_rating',
        'cover_photo',
        'wishlist',
        'average_rating',
        // missedstep
        'can_edit',
        'missed_step',
    ];
    //     propertyview query
    //     Create View propertyview AS
    // SELECT
    //     p.id,
    //     p.property_code,
    //     p.rating_avg,
    //     p.amenities,
    //     p.bedrooms,
    //     p.beds,
    //     p.bed_type,
    //     p.adult_guest,
    //     p.children_guest,
    //     p.accommodates,
    //     p.booking_type,
    //     p.min_nights,
    //     p.max_nights,
    //     p.bathrooms,
    //     p.space_type,
    //     p.name,
    //     p.slug,
    //     p.host_id,
    //     p.status,
    //     p.visibility,
    //     p.recomended,
    //     p.created_at,
    //     ps.price,
    //     pa.address_line_1,
    //     pa.latitude,
    //     pa.longitude,
    //     pa.state,
    //     pa.city,
    //     pa.country,
    //     pd.summary,
    //     pd.summary_ar,
    //     c.code,
    //     c.rate,
    //     c.symbol,
    //     pt.name AS property_type,
    //     pt.id AS property_type_id
    // FROM
    //     darent.properties p
    //     JOIN darent.property_price ps ON p.id = ps.property_id
    //     JOIN darent.property_address pa ON p.id = pa.property_id
    //     JOIN darent.property_description pd ON p.id = pd.property_id
    //     JOIN darent.currency c ON c.code = ps.currency_code
    //     JOIN darent.property_type pt ON p.property_type = pt.id
    // WHERE
    //     p.status = 'Listed'
    //     AND p.visibility = 1;
    //      AND p.slug IS NOT NULL


    public static function recommendedHome()
    {
        Cache::forget(config('cache.prefix') . 'recommendedHome');
        return Cache::remember(config('cache.prefix') . 'recommendedHome', now()->addDay(), function () {
            $defaultIds = ['317', '584', '590', '697', '1025', '1385', '1560'];
            $helper = new Common;
            $checkin = Carbon::now()->format('Y-m-d');
            $checkout = Carbon::now()->addDay()->format('Y-m-d');

            // Get dates excluding checkout
            $days = $helper->get_days($checkin, $checkout);
            array_pop($days);

            // Get unavailable property IDs
            $notAvailableIds = DB::table('property_dates')
                ->whereIn('date', $days)
                ->where('status', 'Not available')
                ->distinct()
                ->pluck('property_id');

            // Get pricing data
            $propertiesPricing = $helper->getPropertiesPricing($checkin, $checkout);

            // Base query
            $query = parent::where('properties.status', 'listed')
                ->whereNotIn('properties.id', $notAvailableIds)
                ->where('properties.visibility', '1')
                ->joinSub($propertiesPricing, 'properties_pricing', function (JoinClause $join) {
                    $join->on('properties.id', '=', 'properties_pricing.property_id');
                })
                ->join('property_type', 'properties.property_type', '=', 'property_type.id')
                ->join('property_price', 'properties.id', '=', 'property_price.property_id');

            if (app()->environment('prod')) {
                $query->where('properties.recomended', '1')
                    ->select([
                        'properties.*',
                        'property_type.name as property_type_name',
                        'property_type.name_ar as property_type_name_ar',
                        'property_price.price as property_price_price',
                        'properties_pricing.number_of_days',
                        'properties_pricing.day_price',
                        'properties_pricing.total_price',
                        'properties_pricing.before_discount',
                    ])
                    ->with([
                        'users',
                        'property_price',
                        'property_address',
                        'property_description',
                        'propertyType'
                    ])
                    ->withCount([
                        'reviews as guest_review' => fn($q) => $q->where('reviewer', 'guest'),
                        'reviews' => fn($q) => $q->where('reviewer', 'guest'),
                    ])
                    ->inRandomOrder()
                    ->take(10);

                return $query->get()->transform(function ($item) {
                    $item->book_mark = $item->book_mark == 1;
                    return $item;
                });
            }

            // Non-prod (e.g. local, staging) specific IDs
            return $query->whereIn('properties.id', $defaultIds)
                ->select([
                    'properties.*',
                    'property_type.name as property_type_name',
                    'property_type.name_ar as property_type_name_ar',
                    'property_price.price as property_price_price',
                ])
                ->get();
        });
    }


    public static function recommendedForHomeApi($perPage = 8)
    {
        cache::forget(config('cache.prefix') . 'recommendedHome_api');
        return Cache::remember(config('cache.prefix') . 'recommendedHome_api', now()->addDay(), function () use ($perPage) {
            $defaultIds = ['317', '584', '590', '697', '1025', '1385', '1560'];
            $helper = new Common;
            $checkin = Carbon::now()->format('Y-m-d');
            $checkout = Carbon::now()->addDay()->format('Y-m-d');

            $days = $helper->get_days($checkin, $checkout);
            array_pop($days); // Remove checkout

            $notAvailableIds = DB::table('property_dates')
                ->whereIn('date', $days)
                ->where('status', 'Not available')
                ->distinct()
                ->pluck('property_id');

            $propertiesPricing = $helper->getPropertiesPricing($checkin, $checkout);

            // Base query
            $query = parent::where('properties.status', 'listed')
                ->whereNotIn('properties.id', $notAvailableIds)
                ->where('properties.visibility', '1')
                ->joinSub($propertiesPricing, 'properties_pricing', function (JoinClause $join) {
                    $join->on('properties.id', '=', 'properties_pricing.property_id');
                })
                ->join('property_type', 'properties.property_type', '=', 'property_type.id')
                ->join('property_price', 'properties.id', '=', 'property_price.property_id');

            if (app()->environment('prod') || app()->environment('local')) {
                $query->where('properties.recomended', '1')
                    ->select([
                        'properties.*',
                        'property_type.name as property_type_name',
                        'property_type.name_ar as property_type_name_ar',
                        'property_price.price as property_price_price',
                        'properties_pricing.number_of_days',
                        'properties_pricing.day_price',
                        'properties_pricing.total_price',
                        'properties_pricing.before_discount',
                    ])
                    ->with([
                        'users',
                        'property_price',
                        'property_address',
                        'property_description',
                        'propertyType'
                    ])
                    ->withCount([
                        'reviews as guest_review' => fn($q) => $q->where('reviewer', 'guest'),
                        'reviews' => fn($q) => $q->where('reviewer', 'guest'),
                    ]);

                return $query->paginate($perPage);
            }

            // For non-prod environments
            return $query
                ->whereIn('properties.id', $defaultIds)
                ->select([
                    'properties.*',
                    'property_type.name as property_type_name',
                    'property_type.name_ar as property_type_name_ar',
                    'property_price.price as property_price_price',
                ])
                ->paginate($perPage)->shuffle();
        });
    }
    public static function recommendedForHome($per_page = 8, $page = 1)
    {
        // Cache::forget(config('cache.prefix') . 'recommendedHome_all');
        $allRecommended = Cache::remember(config('cache.prefix') . 'recommendedHome_all', now()->addDay(), function () {
            $defaultIds = ['317', '584', '590', '697', '1025', '1385', '1560'];
            $helper = new Common;
            $checkin = Carbon::now()->format('Y-m-d');
            $checkout = Carbon::now()->addDay()->format('Y-m-d');

            $days = $helper->get_days($checkin, $checkout);
            array_pop($days); // Remove checkout

            $notAvailableIds = DB::table('property_dates')
                ->whereIn('date', $days)
                ->where('status', 'Not available')
                ->distinct()
                ->pluck('property_id');

            $propertiesPricing = $helper->getPropertiesPricing($checkin, $checkout);

            // Base query
            $query = parent::where('properties.status', 'listed')
                ->whereNotIn('properties.id', $notAvailableIds)
                ->where('properties.visibility', '1')
                ->joinSub($propertiesPricing, 'properties_pricing', function (JoinClause $join) {
                    $join->on('properties.id', '=', 'properties_pricing.property_id');
                })
                ->join('property_type', 'properties.property_type', '=', 'property_type.id')
                ->join('property_price', 'properties.id', '=', 'property_price.property_id');

                if (app()->environment('prod')||app()->environment('local')) {
                $query->where('properties.recomended', '1')
                    ->select([
                        'properties.*',
                        'property_type.name as property_type_name',
                        'property_type.name_ar as property_type_name_ar',
                        'property_price.price as property_price_price',
                        'properties_pricing.number_of_days',
                        'properties_pricing.day_price',
                        'properties_pricing.total_price',
                        'properties_pricing.before_discount',
                    ])
                    ->with([
                        'users',
                        'property_price',
                        'property_address',
                        'property_description',
                        'propertyType'
                    ])
                    ->withCount([
                        'reviews as guest_review' => fn($q) => $q->where('reviewer', 'guest'),
                        'reviews' => fn($q) => $q->where('reviewer', 'guest'),
                    ]);

                return $query->get()->transform(function ($item) {
                    $item->book_mark = $item->book_mark == 1;
                    return $item;
                })->shuffle();
            }

            // For non-prod environments
            return $query
            ->whereIn('properties.id', $defaultIds)
                ->select([
                    'properties.*',
                    'property_type.name as property_type_name',
                    'property_type.name_ar as property_type_name_ar',
                    'property_price.price as property_price_price',
                ])
                ->get()->shuffle();

        });
        // Shuffle and paginate the cached data (local PHP)
        $paginated = $allRecommended->forPage($page, $per_page)->values(); // Reset keys

        return [
            'data' => $paginated,
            'total' => $allRecommended->count(),
            'per_page' => $per_page,
            'current_page' => $page,
            'last_page' => ceil($allRecommended->count() / $per_page)
        ];
    }


    public static function landingPageProperties()
    {
        $slugs = ['-305', '306', '-308-1', '-301', '307', '302', '303', '304'];

        $data = Cache::get(config('cache.prefix') . 'landingPageProperties');
        if (empty($data)) {
            $data = parent::whereIn('slug', $slugs)->get();
            Cache::put(config('cache.prefix') . 'landingPageProperties', $data, 10 * 86400);
        }
        return $data;
    }


    public function getCanEditAttribute()
    {
        return true;
        $futureBooking = 0;
        $futureBooking = PropertyDates::where('property_id', $this->id)->where('status', 'Not available')
            ->whereDate('date', Carbon::now()->format('Y-m-d'))->count();
        return $futureBooking > 0 ? false : true;
    }

    public function getHostNameAttribute()
    {
        $result = User::where('id', $this->attributes['host_id'])->first();
        return $result ? $result->first_name : null;
    }

    public function getHostImageAttribute()
    {
        $result = User::where('id', $this->attributes['host_id'])->first();
        return $result ? $result->profile_image : null;
    }

    public function space()
    {
        return $this->belongsTo(SpaceType::class);
    }

    public function getPropertyPhotoAttribute()
    {
        return $this->cover_photo ?? null;
//        $result = PropertyPhotos::where('property_id', $this->attributes['id'])->first();
//        $url1 = 'https://darent.com/';
//        if (isset($result->photo)) {
//            $url1 = $url1 . 'images/property/' . $result->property_id . '/' . $result->photo;
//            if (app()->environment('local')) {
//                return $url1;
//            }
//            $url = file_exists($url1) ? $url1 : 'images/default-image-not-exist.png';
//        } else {
//            $url = 'images/default-image.png';
//        }
//
//        return $url;
    }


    public function getSpaceTypeNameAttribute()
    {
        $type = $this->attributes['space_type'] ?? null;
        $spacetype = SpaceType::getAll()->where('id', $type)->first();
        return $spacetype != null ? $spacetype->name : '';
    }

    public function getPropertyTypeNameAttribute()
    {
        $propertytype = PropertyType::getAll()->where('id', $this->attributes['property_type'])->first();
        return $propertytype != null ? $propertytype->name : '';
    }

    public function getStepsCompletedAttribute()
    {
        $result = PropertySteps::where('property_id', $this->attributes['id'])->first();
        $type = $this->attributes['amenities'] ?? null;
        if ($type) {
            $amenities = 0;
        } else {
            $amenities = 1;
        }

        // return 9 - ($result->title + $result->spacetype + $result->basics + $result->description + $result->location + $result->photos + $result->pricing + $result->booking + $amenities);
        return 9 - ($result?->title + $result?->spacetype + $result?->basics + $result?->description + $result?->location + $result?->photos + $result?->pricing + $result?->booking + $amenities);
    }

    public function getMissedStepAttribute()
    {
        $result = PropertySteps::where('property_id', $this->attributes['id'])->first()?->toArray();
        $fl = '';
        if ($result) {
            $discard = ['id', 'property_id'];
            foreach ($result as $key => $value) {
                if (!in_array($key, $discard) && $value == 0) {
                    $fl = $key;
                    break;
                }
            }
        }
        return $fl;
    }

    public function bookings()
    {
        return $this->hasMany('App\Models\Bookings', 'property_id', 'id');
    }

    public function messages()
    {
        return $this->hasMany('App\Models\Messages', 'property_id', 'id');
    }

    public function users()
    {
        return $this->belongsTo('App\Models\User', 'host_id', 'id');
    }

    public function reviews()
    {
        return $this->hasMany('App\Models\Reviews', 'property_id', 'id');
    }

    public function propertyType()
    {
        return $this->belongsTo('App\Models\PropertyType', 'property_type', 'id');
    }

    public function property_type()
    {
        return $this->belongsTo('App\Models\PropertyType', 'property_type', 'id');
    }

    public function property_address()
    {
        return $this->hasOne('App\Models\PropertyAddress', 'property_id', 'id');
    }

    public function property_beds()
    {
        return $this->hasMany('App\Models\PropertyBeds', 'property_id', 'id');
    }

    public function property_dates()
    {
        return $this->hasMany('App\Models\PropertyDates', 'property_id', 'id');
    }

    public function property_description()
    {
        return $this->hasOne('App\Models\PropertyDescription', 'property_id', 'id');
    }

    public function property_details()
    {
        return $this->hasMany('App\Models\PropertyDetails', 'property_id', 'id');
    }

    public function property_price()
    {
        return $this->hasOne('App\Models\PropertyPrice', 'property_id', 'id');
    }

    public function property_photos()
    {
        return $this->hasMany('App\Models\PropertyPhotos', 'property_id', 'id')->orderBy('serial', 'asc');
    }

    public function photos_temp()
    {
        return $this->hasMany('App\Models\PhotosTemp', 'property_id', 'id');
    }

    public function properties_temp()
    {
        return $this->hasOne('App\Models\PropertiesTemp', 'property_id', 'id');
    }

    public function propertyPromoCode()
    {
        return $this->hasOne('App\Models\PromoCodeProperty', 'property_id', 'id');
    }

    public function property_cohost()
    {
        return $this->hasMany('App\Models\CoHostRequest', 'property_id', 'id');
    }

    public function co_hosts()
    {
        return $this->belongsToMany('App\Models\User', 'properties_cohosts', 'property_id', 'co_host_id');
    }


    public function favourite()
    {
        return $this->hasOne(Favourite::class, 'property_id', 'id');
    }

    function admins()
    {
        return $this->belongsToMany(Admin::class, 'admin_property', 'property_id', 'admin_id')->withTimestamps();
    }

    public function getImagesAttribute()
    {
        $total = $this->property_photos()->count();
        return $this->property_photos()->skip(1)->take($total)->pluck('photo')->map(function ($i) {
            return ImageHelper::getPropertyImgUrl($i, $this->attributes['id']);
        })->toArray();
    }

    public function property_rules()
    {
        return $this->hasMany('App\Models\PropertyRules', 'property_id', 'id');
    }

    public function reports()
    {
        return $this->hasMany('App\Models\Reports', 'property_id', 'id');
    }

    public function steps()
    {
        // return $this->hasOne('App\Models\PropertySteps', 'property_id', 'id');
        return $this->hasOne(PropertySteps::class, 'property_id', 'id');
    }

    public function bed_types()
    {
        return $this->belongsTo('App\Models\BedType', 'bed_type', 'id');
    }

    public function property_steps()
    {
        // return $this->hasMany('App\Models\PropertyDetails', 'property_id', 'id');
        return $this->hasOne(StepsCompleted::class, 'property_id', 'id');
    }

    public function stepsCompleted()
    {
        return $this->belongsTo(StepsCompleted::class, 'id', 'property_id');
    }

    // In the Property model
    public function coverPhoto()
    {
        return $this->hasOne(PropertyPhotos::class, 'property_id')->where('cover_photo', 1);
    }

    public function getCoverPhotoAttribute()
    {
        $cacheKey = "property_{$this->id}_cover_photo";

        // $CoverPhoto = PropertyPhotos::where('property_id',$this->attributes['id'])->where('cover_photo', 1)->first();

        // Cache the photo for 1 hour (or any desired duration)
        // return cache()->remember($cacheKey, now()->addHours(10), function ($CoverPhoto) {
        //     return $CoverPhoto->photo ;
        // });
        $cover = PropertyPhotos::where('property_id', $this->attributes['id'])->where('cover_photo', 1)->first();
        return $cover->photo ?? asset('images/property-default-image.png');
    }


    public function getLatestProperties()
    {
        $query = Properties::join('users', function ($join) {
            $join->on('users.id', '=', 'properties.host_id');
        })
            ->join('space_type', function ($join) {
                $join->on('space_type.id', '=', 'properties.space_type');
            })
            ->join('property_type', function ($join) {
                $join->on('property_type.id', '=', 'properties.property_type');
            })
            ->select(['properties.id as properties_id', 'properties.name as property_name', 'properties.status as property_status', 'properties.created_at as property_created_at', 'properties.updated_at as property_updated_at', 'properties.*', 'users.*', 'property_type.*', 'space_type.*'])
            ->take(5)
            ->orderBy('properties.created_at', 'desc')
            ->get();

        return $query;

    }


    public function getReviewsCountAttribute()
    {
        return Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')->where('ispublic', 1)->count();
    }

    // public function getGuestReviewAttribute()
    // {
    //     return Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')->count();
    // }

    public function getOverallRatingAttribute()
    {
        $reviews = Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')->get();
        if ($reviews->count() == 0) {
            return $avg = 0;
        } else {
            $avg = @($reviews->sum('rating') / $reviews->count());

            if ($avg) {
                $sum = 0;
                $whle = floor($avg);
                $fract = $avg - $whle;
                for ($i = 0; $i < $whle; $i++) {
                    $sum += 25;
                }

                if ($fract >= 0.5) {
                    $sum += 12;
                }

                return $sum;
            } else {
                return 0;
            }
        }
    }

    public function getAverageRatingAttribute()
    {
        $review = Reviews::avgs('guest', $this->attributes['id']); 
        // $review->darent_service + $review->darent_recomended +
        $rating = ($review->cleanliness + $review->accuracy + $review->location + $review->communication) / 4 ?? 0;
        // $rating = fmod($rating, 1) == 0 ? number_format($rating, 0) : number_format($rating, 1);

        // Truncate to 1 decimal place without rounding
        $rating = floor($rating * 10) / 10;

        // Format: if whole number, no decimal; if not, 1 digit
        $rating = fmod($rating, 1) == 0
            ? number_format($rating, 0, '.', '')
            : number_format($rating, 1, '.', '');

        return $rating;
        // ($review->cleanliness + $review->darent_service + $review->darent_recomended + $review->accuracy + $review->communication) / 5;
        // return Reviews::where('property_id', $this->attributes['id'])->where('ispublic', 1)
        //     ->selectRaw('CAST(AVG(rating) AS DECIMAL(10,1)) as avgRating')->first()->avgRating ?? 0;
    }

    public function getAccuracyRatingAttribute()
    {
        $reviews = Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest');
        $avg = ($reviews->sum('accuracy') / $reviews->count());

        if ($avg) {
            $sum = 0;

            $whle = floor($avg);
            $fract = $avg - $whle;

            for ($i = 0; $i < $whle; $i++) {
                $sum += 25;
            }

            if ($fract >= 0.5) {
                $sum += 12;
            }

            return $sum;
        } else {
            return 0;
        }
    }


    public function getAccuracyAvgRatingAttribute()
    {
        return Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')
            ->selectRaw('CAST(AVG(accuracy) AS DECIMAL(10,2)) as avgRating')->first()->avgRating ?? 0;
    }


    public function getLocationRatingAttribute()
    {
        $reviews = Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest');
        $avg = ($reviews->sum('location') / $reviews->count());

        if ($avg) {
            $sum = 0;
            $whle = floor($avg);
            $fract = $avg - $whle;

            for ($i = 0; $i < $whle; $i++) {
                $sum += 25;
            }

            if ($fract >= 0.5) {
                $sum += 12;
            }


            return $sum;
        } else {
            return 0;
        }
    }

    public function getLocationAvgRatingAttribute()
    {
        return Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')
            ->selectRaw('CAST(AVG(location) AS DECIMAL(10,2)) as avgRating')->first()->avgRating ?? 0;
    }

    public function getCommunicationRatingAttribute()
    {
        $reviews = Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest');
        $avg = ($reviews->sum('communication') / $reviews->count());

        if ($avg) {
            $sum = 0;

            $whle = floor($avg);
            $fract = $avg - $whle;

            for ($i = 0; $i < $whle; $i++) {
                $sum += 25;
            }

            if ($fract >= 0.5) {
                $sum += 12;
            }


            return $sum;
        } else {
            return 0;
        }
    }

    public function getCommunicationAvgRatingAttribute()
    {
        return Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')
            ->selectRaw('CAST(AVG(communication) AS DECIMAL(10,2)) as avgRating')->first()->avgRating ?? 0;
    }

    public function getCheckinRatingAttribute()
    {
        $reviews = Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest');

        $avg = ($reviews->sum('checkin') / $reviews->count());

        if ($avg) {
            $sum = 0;
            $whle = floor($avg);
            $fract = $avg - $whle;

            for ($i = 0; $i < $whle; $i++) {
                $sum += 25;
            }

            if ($fract >= 0.5) {
                $sum += 12;
            }

            return $sum;
        } else {
            return 0;
        }
    }

    public function getCheckinAvgRatingAttribute()
    {
        return Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')
            ->selectRaw('CAST(AVG(checkin) AS DECIMAL(10,2)) as avgRating')->first()->avgRating ?? 0;
    }

    public function getCleanlinessRatingAttribute()
    {
        $reviews = Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest');
        $avg = ($reviews->sum('cleanliness') / $reviews->count());

        if ($avg) {
            $sum = 0;
            $whle = floor($avg);
            $fract = $avg - $whle;

            for ($i = 0; $i < $whle; $i++) {
                $sum += 25;
            }

            if ($fract >= 0.5) {
                $sum += 12;
            }

            return $sum;
        } else {
            return 0;
        }
    }

    public function getCleanlinessAvgRatingAttribute()
    {
        return Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')
            ->selectRaw('CAST(AVG(cleanliness) AS DECIMAL(10,2)) as avgRating')->first()->avgRating ?? 0;
    }

    public function getValueRatingAttribute()
    {
        $reviews = Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest');

        $avg = ($reviews->sum('value') / $reviews->count());

        if ($avg) {
            $sum = 0;
            $whle = floor($avg);
            $fract = $avg - $whle;

            for ($i = 0; $i < $whle; $i++) {
                $sum += 25;
            }

            if ($fract >= 0.5) {
                $sum += 12;
            }

            return $sum;
        } else {
            return 0;
        }
    }

    public function getValueAvgRatingAttribute()
    {
        return Reviews::where('property_id', $this->attributes['id'])->where('reviewer', 'guest')
            ->selectRaw('CAST(AVG(value) AS DECIMAL(10,2)) as avgRating')->first()->avgRating ?? 0;
    }

    public function getBookMarkAttribute()
    {

        //     $this->load('favourite');
        //     return $this->relationLoaded('favourite') && !is_null($this->favourite) && $this->favourite->status === 'Active';

        // // $data = Cache::get(config('cache.prefix') . 'getBookMarkAttribute');
        // // if (empty($data)) {
        //     $data = $this->favourite()->where('status', 'Active')->first();
        //     // $data = Cache::put(config('cache.prefix') . 'getBookMarkAttribute', $data, 1 * 86400);
        // // }
        //     return !empty($data);

        if (Auth::guard('api')->check()) {

            $favourite = Favourite::where('property_id', $this->attributes['id'])->where('user_id', Auth::guard('api')->id())->where('status', 'Active')->first();
            if (empty($favourite)) {
                return false;
            }
            return true;
        }

        if (Auth::guard('web')->check()) {

            $favourite = Favourite::where('property_id', $this->attributes['id'])->where('user_id', Auth::guard('web')->id())->where('status', 'Active')->first();
            if (empty($favourite)) {
                return false;
            }
            return true;
        } else {
            return false;
        }
    }

    public function wishlistProperties()
    {
        return $this->hasMany(WishlistProperties::class, 'property_id', 'id');
    }

    public function getWishlistAttribute()
    {
        if ($this->relationLoaded("wishlistProperties")) {
            $this->load("wishlistProperties");
        }
        if (Auth::guard('api')->check()) {

            $favourite = $this->wishlistProperties->where('property_id', $this->attributes['id'])->where('user_id', Auth::guard('api')->id())->where('status', '1')->first();
            if (empty($favourite)) {
                return false;
            }
            return true;
        }

        if (Auth::guard('web')->check()) {

            $favourite = $this->wishlistProperties->where('property_id', $this->attributes['id'])->where('user_id', Auth::guard('web')->id())->where('status', '1')->first();
            if (empty($favourite)) {
                return false;
            }
            return true;
        } else {
            return false;
        }
    }

    public function property_discount()
    {
        return $this->hasOne('App\Models\PropertyDiscount', 'property_id', 'id');
    }

    public function getdiscountedAmountAttribute()
    {
        $propDiscount = PropertyDiscount::where('property_id', $this->attributes['id'])->first();
        $amount = null;
        if ($propDiscount) {
            $propPrice = PropertyPrice::where('property_id', $this->attributes['id'])->select('price')->first();

            $amount = $propPrice->price * $propDiscount->discount / 100;
            $amount = $propPrice->price - $amount;
        }
        return $amount;
    }

    public function userPropertyView()
    {
        return $this->hasMany(UserPropertyView::class, 'property_id');
    }


    protected static function booted()
    {
        // parent::boot();
        if (app()->environment('prod') && strpos(request()->getRequestUri(), '/admin') === 0) {
            // if (2==1) {
            // Apply the global scope if the time zone is not a specific value
            static::addGlobalScope('excludeReceivers', function ($query) {
                $excludedReceiverIds = [
                    42, 501, 517, 5344, 5432, 5434, 5435, 5534, 5551,
                    5603, 465,
                    59, 68, 70, 84, 173, 252, 457, 5351, 5394, 5418, 5493, 5553, 5557, 5632, 5675,
                    507, 508, 5303, 5776, 5431, 5745, 5775, 5836, 5841,
                    5569, 5588, 5614, 5747, 5815, 5833, 5569, 50, 5867, 5846, 5700, 5538, 5672, 5564, 5657, 5828,
                    5835, 5700, 5849, 5860, 5831, 5650, 5829,
                    5856
                    , 6693, 12502,
                    // Abdullah LOCAL Ids
                    // 37, 487, 521, 538, 515, 5819, 5820,5869, 9893
                    //5828
                ];
                $query->whereNotIn('properties.host_id', $excludedReceiverIds);
            });
        }
    }


    public static function findByCode($code)
    {
        return Properties::where('property_code', $code)->first();
    }

    public static function systemGeoCode($city)
    {
        return DB::table('cities')->where('name', $city)->first();
    }

    public static function storeCustomAmenities($propertyId, $amenities)
    {
        $custom_amenities = [];
        $property = self::find($propertyId);
        if (is_null($property->custom_amenities)) {
            $custom_amenities = $amenities;
        } else {
            $custom_amenities = array_merge(json_decode($property->custom_amenities), $amenities);
        }
        return self::where('id', $propertyId)->update(['custom_amenities' => $custom_amenities]);
    }

    public static function storeCustomAmenitiesApi($propertyId, $amenities)
    {
        return self::where('id', $propertyId)->update(['custom_amenities' => $amenities]);
    }

    public static function getCampaignData($properties)
    {
        return parent::whereIn('property_code', $properties)->paginate(10);
    }

    public static function cacheForNextSearch($key, $lat, $long, $action)
    {
        $currentTime = Carbon::now();
        $cityArray = [
            'name' => $key,
            'latitude' => $lat,
            'longitude' => $long,
            'created_at' => $currentTime,
            'updated_at' => $currentTime,
        ];

        Cache::forever($key, $cityArray);

        if ($action == self::CREATE_IN_DB) {
            DB::table('search_cities')
                ->updateOrInsert($cityArray);
        }
    }

    public function icalImport()
    {
        return $this->hasMany('App\Models\PropertyIcalimport', 'property_id', 'id');
    }

    public function getLicenseDocumentAttribute($value)
    {
        if ($value == null) return null;

        if (app()->environment() == 'prod') return 'public/license/' . $value;
        return 'storage/license/' . $value;
    }

    public function getMonthlyWeeklyDiscount(){
        if($this->property_price?->weekly_discount != 0 && $this->property_price?->monthly_discount != 0){
            return 'monthly_and_weekly_discount';
        } else if($this->property_price?->monthly_discount != 0){
            return 'monthly_discount_available';
        } else if($this->property_price?->weekly_discount){
            return 'weekly_discount_available';
        } else {
            return null;
        }
    }

    public function getMonthlyWeeklyDiscountValue(){
        if($this->property_price?->weekly_discount != 0 && $this->property_price?->monthly_discount != 0){
            return null;
        } else if($this->property_price?->monthly_discount != 0){
            return $this->property_price?->monthly_discount;
        } else if($this->property_price?->weekly_discount){
            return $this->property_price?->weekly_discount;
        } else {
            return null;
        }
    }

    public function getDeterminPrices(array $dates)
    {
        return MasterAvailability::where('property_id', $this->id)
        ->whereIn('date', $dates)
        ->get(['date', 'determine_price', 'monthly_discount_amount', 'weekly_discount_amount'])
        ->keyBy('date')
        ->map(function ($item) {
            return [
                'price' => $item->determine_price,
                'monthly_discount' => $item->monthly_discount_amount,
                'weekly_discount' => $item->weekly_discount_amount,
            ];
        })
        ->toArray();
    }  
}
