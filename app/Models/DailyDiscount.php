<?php

/**
 * DailyDiscount Model
 *
 * DailyDiscount Model manages DailyDiscount operation.
 *
 * @category   DailyDiscount
 * @package    darent
 * <AUTHOR> Dev Team
 * @copyright Darent
 * @license
 * @version    2.7
 * @link       http://darent.com
 * @since      Version 1.3
 *
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DailyDiscount extends Model
{
    protected $table = 'daily_discounts';
    protected $fillable = [
        'property_id',
        'date',
        'status',
        'discount',
    ];

    public function properties()
    {
        return $this->belongsTo('App\Models\Properties', 'property_id', 'id');
    }
}
