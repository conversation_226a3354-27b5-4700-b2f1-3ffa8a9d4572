<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MasterAvailability extends Model
{
    use HasFactory;

    protected $table = 'master_availability';

    protected $fillable = [
        'property_id',
        'date',
        'status',
        'booking_id',
        'booking_status',
        'booking_created_at',
        'booking_cancelled_at',
        'checkin_date',
        'checkout_date',
        'host_id',
        'user_id',
        'source_type',
        'country',
        'city_id',
        'district_id',
        'property_type',
        'platform_id',
        'is_peak_day',
        'peak_day_details',
        'performance_tier',
        'determine_price',
        'weekly_discount_amount',
        'monthly_discount_amount'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date' => 'date',
        'booking_created_at' => 'datetime',
        'booking_cancelled_at' => 'datetime',
        'checkin_date' => 'date',
        'checkout_date' => 'date',
    ];

    /**
     * Get the property that owns the availability record.
     */
    public function property()
    {
        return $this->belongsTo(Properties::class, 'property_id');
    }

    /**
     * Get the booking associated with the availability record.
     */
    public function booking()
    {
        return $this->belongsTo(Bookings::class, 'booking_id');
    }

    /**
     * Get the host associated with the availability record.
     */
    public function host()
    {
        return $this->belongsTo(User::class, 'host_id');
    }

    /**
     * Get the user associated with the availability record.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
