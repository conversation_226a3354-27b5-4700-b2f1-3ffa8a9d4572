<?php

namespace App\DataTables;

use App\Models\PropertyManagementRequest;
use Yajra\DataTables\Services\DataTable;
use Carbon\Carbon;

class PropertyManagementDataTable extends DataTable
{
    public function ajax(): \Illuminate\Http\JsonResponse
    {
        $i = 0;
        return datatables()
            ->eloquent($this->query())
            ->addColumn('action', function ($request) use ($i) {

                $edit = '<a href="' . route('admin.property-management-requests.edit', $request->id) . '" class="btn btn-xs btn-primary" data-toggle="tooltip" data-placement="top" title="Edit"><i class="glyphicon glyphicon-edit"></i></a>&nbsp;';
                 $delete = '<a href="' . route('admin.property-management-requests.destroy', $request->id) . '" class="btn btn-xs btn-danger delete-warning" data-toggle="tooltip" data-placement="top" title="Delete"><i class="glyphicon glyphicon-trash"></i></a>';
                return $edit. ' ' .  $delete;
            })
            ->addColumn('city', function ($request) use ($i) {
                return $request->city->name;
            })
            ->addColumn('created_at', function ($request) use ($i) {
                return date('Y-m-d',strtotime($request->created_at));
            })
            ->rawColumns([
                'city',
                'created_at',
                'action',
            ])
            ->make(true);
    }

    public function query()
    {
        $from = isset(request()->from) ? Carbon::createFromFormat('m-d-Y', request()->from)->format('Y-m-d') : null;
        $to = isset(request()->to) ? Carbon::createFromFormat('m-d-Y', request()->to)->format('Y-m-d') : null;
        $request = PropertyManagementRequest::query();
        if (!empty($from)) {
            $request->whereDate('property_management_requests.created_at', '>=', $from);
        }
        if (!empty($to)) {
            $request->whereDate('property_management_requests.created_at', '<=', $to);
        }
        return $this->applyScopes($request);
    }

    public function html()
    {
        return $this->builder()
            ->addColumn(['data' => 'id', 'name' => 'property_management_requests.id', 'title' => 'S.no', 'searchable' => false])
            ->addColumn(['data' => 'owner_name', 'name' => 'property_management_requests.owner_name', 'title' => 'Owner Name'])
            ->addColumn(['data' => 'property_name', 'name' => 'property_management_requests.property_name', 'title' => 'Property Name'])
            ->addColumn(['data' => 'units', 'name' => 'property_management_requests.units', 'title' => 'No. Of Units'])
            ->addColumn(['data' => 'city', 'name' => 'city', 'title' => 'City'])
            ->addColumn(['data' => 'location', 'name' => 'property_management_requests.location', 'title' => 'Location'])
            ->addColumn(['data' => 'email', 'name' => 'property_management_requests.email', 'title' => 'Email'])
            ->addColumn(['data' => 'created_at', 'name' => 'created_at', 'title' => 'Submission Date', 'orderable' => true, 'searchable' => true])
            ->addColumn(['data' => 'user_id', 'name' => 'property_management_requests.user_id', 'title' => 'Host ID'])
            ->addColumn(['data' => 'action', 'name' => 'action', 'title' => 'Action', 'orderable' => false, 'searchable' => false])
            ->parameters();
    }


    protected function filename(): string
    {
        return 'propertymanagementdatatables_' . time();
    }
}
