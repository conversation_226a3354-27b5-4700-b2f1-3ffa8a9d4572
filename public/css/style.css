/* Global css */
@font-face {
    font-family: 'dubai-font';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('../fonts/dubai-font/Dubai-Light.ttf')
}

@font-face {
    font-family: 'dubai-font';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/dubai-font/Dubai-Regular.ttf')
}

@font-face {
    font-family: 'dubai-font';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url('../fonts/dubai-font/Dubai-Medium.ttf')
}

@font-face {
    font-family: 'dubai-font';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/dubai-font/Dubai-Bold.ttf')
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
a,
span,
strong,
td,
th {
    font-family: 'dubai-font';
}

body.sp-ontop {
    margin-top: 87px;
}


:root {
    --theme-primary: #f5c33e;
    --theme-middle: #000;
    --them-secondary: #909090;
    /* --them-secondary: #717171; */
    --grey-one: #E2E2E2;
    --grey-two: #EDEDED;
    --grey-three: #8A8A8A;
    --green-color: #13CB99;
    --dr-shade-gray: #181818;
    --dim-gray: #707070;
    --success-green: #23BC4C;
    --warning-red: #FA3838;
    --loader-background-color: #EEEEEE;
    --loader-highlight-color: #DEDEDE;
}

* {
    margin: 0;
    padding: 0
}

.ellipsis-oneline {
    display: -webkit-box !important;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

a {
    color: black;
}

.ellipsis-twoline {
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
.ellipsis-fourline {
    display: -webkit-box !important;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
p {
    color: var(--them-secondary)
}

a {
    text-decoration: none
}

.theme-btn {
    background-color: var(--theme-primary);
    border: unset;
    border-radius: 7px;
    line-height: 1.6;
    font-weight: 400;
    text-transform: capitalize;
}

.bg-transparent-btn {
    background-color: transparent;
    border: 1px solid var(--grey-two);
    color: var(--theme-middle);
    line-height: 1;
    font-weight: 400;
    border-radius: 7px
}

.grey-btn {
    background-color: var(--grey-two);
    border: unset;
    border-radius: 7px;
    line-height: 1
}

.transparent-btn {
    border: unset
}

.black-transparent-btn {
    border: 1px solid var(--theme-middle);
    color: var(--theme-middle);
    border-radius: 25px
}
.theme-link-btn{
    background: transparent;
    border: none;
    text-decoration: underline;
}
/* grey tabing  use globaly this class grey-tabs*/
.grey-tabs .nav-pills .nav-link.active,
.grey-tabs .nav-pills .show>.nav-link {
    background: #f4f4f4;
    color: #000
}

.grey-tabs .nav-link {
    color: #000;
}

/* grey tabing  end*/
.grey-scroller::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 0;
}

.grey-scroller::-webkit-scrollbar-track {
    border-radius: 0;
    background-color: #f1f1f1;
}

.light-grey-scroller::-webkit-scrollbar {
    width: 7px;
}

.light-grey-scroller::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 25px;
}

.light-grey-scroller::-webkit-scrollbar-track {
    border-radius: 0;
    background-color: transparent;
}

.light-grey-scroller::-webkit-scrollbar-track {
    box-shadow: none;
    border-radius: none;
}

/* global css end */
.bg-transparent-btn,
.grey-btn,
.theme-btn {
    padding: 8px 30px;
    font-size: 18px;
    height: 47px;
}

.btn-position,
.error-btn {
    position: absolute;
    top: 6px;
}

.underline-btn,
p.comp a {
    text-decoration: underline;
}

.ntf-user h6,
.product-detail .title h4,
.title h4 {
    -webkit-box-orient: vertical;
}

.chat-img img,
.ls-img img,
.media-photo-badge img,
.pop-img img,
.pro-img img,
img {
    object-position: center;
}

.alert-modal h3,
.carddefault-bgtheme,
.cust-btn,
.left-arrow-title h6,
.listing-checkbox-wrapper,
.listing-counter-main h4,
.no-booking h5,
.product-category li,
.text-cap {
    text-transform: capitalize;
}

.black-color,
.host-help .content1 button a,
.product-detail .product-price,
.property-detail .head-content p,
h1,
h2,
h3,
h4,
h6,
header .nav .header-btn a {
    color: var(--theme-middle)
}

.host-banner .content button a,
.prim-btn a,
.services .services-btn .filter-btn .inner-btn a,
.sync a,
.theme-btn,
.theme-btn a {
    color: #fff
}

.dropdown-item:focus,
.dropdown-item:hover {
    background-color: #e9ecef;
    color: #000
}

.daterangepicker td.in-range,
.dropdown-item.active,
.dropdown-item:active {
    background-color: #e9e9e9
}

.chat-lst:hover .chat-name p,
.fc .fc-daygrid-day-number,
.grey-btn,
.grey-btn a,
.product-category li:hover p,
.rcpt-btn i,
.rcpt-btn:hover,
.services .services-btn .discover-btn button a,
input#front-search-field {
    color: #000
}

.black-transparent-btn,
.transparent-btn {
    background-color: transparent;
    line-height: 1
}

.btn-close,
.custom-modal-header .btn-close {
    border-radius: 50%;
    border: 1px solid;
    font-size: 9px;
    padding: 6px !important;
    opacity: 1
}

.back-btn,
.custom-small-modal-header .back-btn {
    position: absolute;
    left: 12px;
    top: 10px
}

.btn-position {
    right: 7px;
    display: flex
}

.error-btn {
    right: 6px;
    padding: 4px;
    font-size: 7px
}

.bd-right {
    border-right: 1px solid var(--grey-one)
}

.bd-left {
    border-left: 1px solid var(--grey-one)
}

.wait-approval {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: #fff2e1;
    max-width: max-content;
    margin-left: auto;
    padding: 6px 10px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    color: #000;
    margin-bottom: 0
}

.wait-approval i {
    width: 18px;
    height: 18px;
    background: #fccb83;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #fff;
    font-size: 10px;
    margin-right: 8px;
    margin-bottom: 0
}

.product-category,
.product-category-2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
    padding: 0
}

.product-category li {
    text-align: center;
    border: 1px solid transparent;
    width: 85px;
    height: 75px;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    transition: .3s;
    cursor: pointer;
    padding: 10px !important;
    margin: 5px
}

.product-category li:hover {
    border: 1px solid #e5e5e5;
    background: #e5e5e500;
    border-radius: 10px
}

.product-category li:hover img {
    filter: brightness(0);
    transform: scale(1.2)
}

.product-category li img {
    width: 26px;
    transform: scale(1);
    transition: .3s;
    object-fit: fill
}

.listing-checkbox-wrapper p,
.product-category li p {
    color: #909090;
    font-size: 14px;
    margin: 0px 0 0
}

.listing-question,
.property .product,
.services .head {
    margin-bottom: 15px
}

.acc-inner ul li,
.currancy-dropdown,
.custom-dropdown-menu li,
.host-calendar-container,
.js-subtotal-container,
.ls-item .row,
.pro-img,
.product,
.property,
.property .product .image,
.reserv-date,
.single-check {
    position: relative
}

.property .product .image .product-img {
    width: 100%;
    border-radius: 10px;
    height: 280px;
    object-fit: cover
}

.fav-icon {
    position: absolute;
    top: 10px;
    width: 5px;
    z-index: 12;
    cursor: pointer;
    right: 0;
}

.fav-icon.inr {
    right: auto;
    left: 0;
    top: -4px;
}

.confirm-location,
.product-detail,
.property .product .product-detail,
.rcpt-btn-main,
.reserv-detail-descrip {
    margin-top: 10px
}

.product-detail .title,
.property .product .product-detail .title,
.title,
.wall-cred {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.property .product .product-detail .title h4 {
    font-weight: 500;
    font-size: 16px;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 0;
    display: -webkit-box;
    overflow: hidden;
    margin-right: 10px;
}

.property .product .product-detail .title .product-rate {
    font-size: 15px;
    flex: 0 0 30%;
    max-width: 30%;
    text-align: end;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    justify-content: end;
}

.page-arrow,
.product-detail .title .product-rate img,
.property .product .product-detail .title .product-rate img {
    width: 11px;
    margin-right: 5px;
}

html[dir="rtl"] .product-rate {
    flex-direction: row-reverse;
    text-align: right;
    align-items: center;
    align-content: center;
}

.product-rate img {
    width: 11px;
    margin-right: 5px;
}

.property .product .product-detail .product-content {
    line-height: 1.3;
    font-size: 16px;
    margin: 5px 0;
    overflow: hidden;
    color: #717171;
    font-weight: 400;
}

.location-product {
    width: 250px;
    padding: 10px;
    background-color: #fff;
    border-radius: 8px
}

.location-product .image img {
    width: 100%;
    border-radius: 8px
}

.product-detail .title .product-rate,
.title .product-rate {
    font-size: 15px;
    flex: 0 0 22%;
    max-width: 22%;
    text-align: end;
    margin-bottom: 0
}

.product-detail .product-price {
    font-size: 16px;
    margin-bottom: 0;
    color: var(--theme-middle);
    margin-top: 8px;
}

.custom-modal-content {
    border-radius: 22px;
    border: unset
}

.custom-small-modal-header {
    position: relative;
    border: unset
}

.custom-modal-header {
    border-bottom: unset;
    padding: 10px 20px
}

.custom-small-modal-width {
    width: 380px
}

.custom-small-modal-header h5 {
    margin-top: 15px;
    font-weight: 400;
    font-size: 25px
}

.custom-small-modal-header .btn-close {
    border-radius: 50%;
    border: 1px solid;
    font-size: 9px;
    padding: 7px;
    position: absolute;
    right: 22px;
    top: 22px;
    opacity: 1
}

.custom-small-modal-header .back-btn img {
    width: 35px
}

.custom-modal-body {
    padding: 30px 40px
}

.form-control {
    display: block;
    width: 100%;
    padding: 10px 25px;
    font-size: 14px;
    color: var(--theme-middle);
    background-color: #fff;
    border: 1px solid var(--grey-one);
    border-radius: 17px;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    height: 44px;
}

.form-control::placeholder {
    color: var(--them-secondary)
}

.form-control:focus,
[contenteditable].form-control:focus,
[type=email].form-control:focus,
[type=password].form-control:focus,
[type=tel].form-control:focus,
[type=text].form-control:focus,
input.form-control:focus,
input[type=email]:focus,
input[type=number]:focus,
input[type=password]:focus,
input[type=text]:focus,
select.form-control:focus,
textarea.form-control:focus,
textarea:focus {
    outline: #000 solid 1px;
    border: 1px solid #000;
    box-shadow: unset
}

.cust-form-check-input {
    width: 20px;
    height: 20px;
    border-radius: 7px !important
}

.cust-form-check-input:checked[type=checkbox] {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zMjkiIGhlaWdodD0iOS41MDIiIHZpZXdCb3g9IjAgMCAxMy4zMjkgOS41MDIiPg0KICA8cGF0aCBpZD0iUGF0aF8xMDgiIGRhdGEtbmFtZT0iUGF0aCAxMDgiIGQ9Ik01NDk2LjQ2MywzODQ3LjE1NGwtNS4yNC01LjI0LDEuNDE0LTEuNDE0LDMuODI2LDMuODI2LDYuNjc0LTYuNjc0LDEuNDE0LDEuNDE0WiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTU0OTEuMjIzIC0zODM3LjY1MSkiLz4NCjwvc3ZnPg0K) !important;
    background-size: 12px
}

.cust-check {
    display: flex !important;
    align-items: center !important
}

.cust-check label {
    margin-left: 10px;
    cursor: pointer;
}

.cust-form-check-radio-input:checked {
    background-color: transparent !important;
    border-color: transparent !important
}

.cust-form-check-input:checked {
    background-color: transparent !important;
    border-color: var(--them-secondary) !important
}

.cust-form-check-input:focus {
    border-color: var(--them-secondary) !important;
    outline: 0 !important;
    box-shadow: none !important
}

.form-green-check-input:checked[type=checkbox] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='13.329' height='9.502' viewBox='0 0 13.329 9.502'%3E%3Cpath id='Path_108' data-name='Path 108' d='M5496.463,3847.154l-5.24-5.24,1.414-1.414,3.826,3.826,6.674-6.674,1.414,1.414Z' transform='translate(-5491.223 -3837.651)' fill='%2323bc4c'/%3E%3C/svg%3E%0A") !important;
    background-size: 65%
}

.form-green-check-input:checked {
    background-color: transparent;
    border-color: #23bc4c
}

.feature .bg,
.host-help::before {
    background-color: var(--theme-primary)
}

.show {
    display: block !important
}

.footer-logo img,
header .logo {
    width: auto
}

header .header-inner {
    padding: 12px 0 8px
}

header .search-reservation {
    display: flex;
    border: 1px solid #e2e2e2;
    border-radius: 25px;
    justify-content: center;
    text-align: center;
    padding-bottom: 5px;
    cursor: pointer;
    position: relative;
    margin-bottom: 0;
    align-items: center;
    width: 100%
}

.blkd {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99
}

.search-reservation li {
    padding: 0 20px;
    position: relative;
    width: 28%
}

.search-reservation li:nth-child(2) {
    width: 34%
}

.search-reservation li:last-child {
    padding: 0;
    width: 10%
}

/* .search-reservation li::before {
  position: absolute;
  content: "";
  width: 2px;
  height: 20px;
  background-color: #ededed;
  top: 50%;
  right: 0;
  transform: translateY(-50%)
} */

.search-reservation li:last-child::before {
    content: unset
}

.filter-main .container {
    position: relative;
}

.search-reservation li span {
    text-align: center;
    line-height: 1.3;
    display: block;
    font-size: 16px;
    color: #909090;
    margin-bottom: 0
}



.search-reservation p {
    color: #000;
    font-weight: 500;
    margin-bottom: 0;
    font-size: 14px;
    height: 100%
}

.no-guest {
    margin-right: 5px
}

.popup-main {
    width: 59%;
    display: none;
    position: absolute;
    background: #fff;
    padding: 40px 50px;
    border-radius: 25px;
    box-sizing: border-box;
    top: 145px;
    box-shadow: 1px 4px 10px 0 #00000059;
    z-index: 40;

}

.for-filter {
    transform: translate(0px, 31px);
}

.popup-main .search-location-popup {
    top: 25px;
    left: 20%
}

.guest-popup {
    right: 30%
}

.filter-check,
.popup form,
.text-left,
.total-guest .content {
    text-align: left
}

.popup input {
    width: 100%;
    padding: 7px 10px;
    border-radius: 25px;
    border: 1px solid #ededed
}

.popup input::placeholder {
    color: #e2e2e2;
    font-size: 14px
}

.popup .search-result-main {
    margin-top: 15px;
    overflow: auto;
    height: 240px
}

.popup .search-result-main .search-result {
    display: flex;
    align-items: baseline;
    border-bottom: 1px solid #ededed;
    padding: 10px 0
}

.acc-inner ul li:last-child .ac-dt,
.popup .search-result-main .search-result:last-child {
    border: unset
}

.popup .search-result-main .search-result img {
    padding-right: 15px;
    width: 22px
}

.popup .search-result-main .search-result .content {
    text-align: start
}

.popup .search-result-main .search-result .content h4 {
    font-weight: 400;
    font-size: 15px;
    margin-bottom: 0
}

.popup .search-result-main .search-result .content p,
header .popup .main .inner-main p {
    color: #909090;
    font-size: 14px;
    margin-bottom: 0
}

.total-guest {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border-bottom: 1px solid #ededed;
    padding: 10px 0
}

.total-guest:last-child {
    border-bottom: unset
}

.listing-question p,
.total-guest .content h4 {
    margin-bottom: 0;
    font-size: 18px
}

.total-guest .content p {
    color: #909090;
    margin-bottom: 0;
    font-size: 15px
}

.guest-counter {
    display: flex;
    align-items: center;
    justify-content: center
}

.guest-counter span {
    cursor: pointer
}

.fea-btn a,
.guest-counter .minus,
.plus {
    display: inline-block
}

.fav-in img,
.guest-counter img {
    width: 30px
}

.guest-counter input {
    height: 34px;
    width: 40px;
    text-align: center;
    font-size: 16px;
    border: unset;
    font-weight: 500
}

.gm-style-iw-d,
.no-scroll {
    overflow: hidden !important;

}

.no-scroll {
    padding-right: 10px !important;
}

.main-logo a,
.pricing-inner label,
.wish-icons span,
body.no-scroll .blkd {
    display: block
}

.blkd {
    top: 0px;
    width: 100%;
    height: 100%;
    background-color: rgb(0 0 0 / 40%);
    opacity: 1;
    visibility: visible;
    transition: visibility 0s linear .25s, opacity .25s, transform .25s;
    display: none
}

.popup h3 {
    text-align: left;
    margin-bottom: 20px;
    font-weight: 400;
    font-size: 20px
}

.popup .main {
    column-gap: 15px;
    display: grid;
    flex-wrap: wrap;
    grid-template-columns: repeat(3, min-content);
    row-gap: 15px;
    text-align: center
}

.popup .main .inner-main img {
    height: 85px;
    border-radius: 10px
}

header .nav {
    height: 100%;
    display: flex;

    align-items: center
}

header .nav .header-btn {
    background: 0 0;
    border: none;
    /* padding-right: 15px; */
    font-size: 15px;
    color: var(--theme-middle);
}

/*
header .nav .user .nav-menu {
  line-height: 0;
  border: 1px solid #e2e2e2;
  padding: 4px 15px 4px 6px;
  border-radius: 25px;
  background: #ffff
} */

header .nav .currancy-dropdown button img {
    width: 8px
}

/* header .nav .nav-menu {
  align-items: center;
  line-height: 0;
  border: 1px solid #e2e2e2;
  padding: 4px 15px 4px 6px;
  border-radius: 25px
} */

header .nav .nav-menu .user-profile {
    margin-left: 8px;
    margin-right: 8px;

    width: 40px;
    height: 40px;
}

header .nav .nav-menu img {
    width: 13px
}

.dropdown-toggle::after,
.fav-in img.fav-fill,
.for-mobile,
.pac-logo:after,
.slider.slider-horizontal:first-child,
.slider:before,
.sm\:flex-1,
.sm\:hidden,
.star-rating input[type=radio],
.switch input {
    display: none
}

.dropdown-shadow {
    box-shadow: rgb(0 0 0 / 15%) 0 0 7px
}

.custom-dropdown-menu,
.navmenu {
    box-shadow: rgb(0 0 0 / 30%) 0 0 4px
}

.navmenu {
    width: 245px;
    border-radius: 10px;
    background: #fff;
    position: absolute;
    top: 60px;
    z-index: 10;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: .3s;
    transition: .3s;
    padding: 15px;
}

.banner,
.feature .bg,
.host-help,
.nav-inner {
    position: relative
}

.opened {
    visibility: visible;
    opacity: 1;
    z-index: 99;
}

.nav-inner {
    display: flex;
    align-items: flex-start
}

/* .property .product .image {
  height: 280px !important;
} */
.navmenu .nav-inner::before {
    content: "";
    position: absolute;
    height: 45px;
    width: 1px;
    background-color: #ededed;
    top: 48%;
    left: 50%;
    transform: translate(-50%, -50%)
}

.navmenu ul.text-list {
    text-align: center;
    width: 100%;
    margin: 0;
    padding: 10px 0
}

.navmenu ul.text-list li {
    flex: 0 0 50%;
    padding-bottom: 10px;
    position: relative;
    padding-left: 7px;
    padding-right: 15px;
    text-align: center;
}

.navmenu ul.text-list li .tick-icon {
    border: 1px solid #dbd9d9;
    height: 25px;
    width: 25px;
    border-radius: 3px;
    margin-right: 10px;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}

.navmenu ul.text-list li a {
    text-decoration: none;
    color: #000;
    display: block;
    transition: .2s;
    font-size: 14px;
    text-align: left;
    width: 80px;
    margin-right: 20px
}

.navmenu ul.text-list li img {
    width: 14px;

}

.custom-dropdown-menu {
    border: unset !important;
    z-index: 1111
}

.user-name {
    font-weight: 500;
    padding-bottom: 5px
}

.user-name a {
    font-size: 15px !important
}

.custom-dropdown-menu li:nth-child(3n+1) a::before {
    content: "";
    position: absolute;
    bottom: 0;
    width: 80%;
    background-color: var(--grey-two);
    height: 1px
}

.custom-dropdown-menu li a {
    color: var(--theme-middle);
    font-size: 14px;
    padding: 5px 0 5px 15px
}

.banner {
    padding-top: 15px;
    margin: 0 0 100px
}

.banner .banner-img {
    width: 100%;
    height: 100vh
}

.banner-content h1 {
    font-size: 45px;
    line-height: 1.1;
    color: #fff;
    font-weight: 400
}

.banner-content button {
    background-color: #fff;
    border: unset;
    padding: 12px 50px;
    border-radius: 5px;
    line-height: 1;
    display: block;
    font-size: 15px;
    margin-top: 15px
}

.services .head {
    margin: 40px 0;
}

.services .head h1 {
    font-weight: 400;
    font-size: 30px;
    line-height: 1.1
}

.services .services-btn {
    text-align: end
}

.services .services-btn .discover-btn button {
    padding: 9px 22px;
    line-height: 1;
    font-size: 14px;
    height: 47px
}

.services .services-btn .filter-btn {
    display: flex;
    justify-content: end
}

.services .services-btn .filter-btn .inner-btn:first-child {
    display: unset
}

.services .services-btn .filter-btn .inner-btn:first-child img {
    padding-right: 0;
    width: 12px
}

.services .services-btn .filter-btn .inner-btn:nth-child(2) img {
    width: 20px
}

.services .services-btn .filter-btn .inner-btn:nth-child(3) img {
    width: 16px
}

.list-icon {
    width: 24px !important
}

.services .services-btn .filter-btn .inner-btn {
    padding: 8px 16px;
    line-height: 1;
    font-size: 14px;
    margin-right: 20px
}

.product-category-2 li:last-child .listing-checkbox-wrapper.fsrch,
.services .services-btn .filter-btn .inner-btn:nth-child(3) {
    margin-right: 0
}

.services .services-btn .filter-btn .inner-btn img {
    width: 16px;
    padding-right: 10px
}

.feature {
    margin-bottom: 70px
}

.feature .bg {
    height: 220px;
    margin-bottom: 20px;
    padding: 0 100px;
    border-radius: 10px
}


.fea-btn button {
    background: 0 0;
    border: transparent;
    border-radius: 9px;
    line-height: 1;
    height: 50px;
    width: 130px;
}

.fea-btn a:last-child button {
    margin-left: 20px;

}

.fea-btn button img {
    width: 100%;
    position: relative;
    bottom: 6px;
}





.feature .bg .d img {
    position: absolute;
    width: 285px;
    top: -43px;
    right: 100px
}

footer .footer-inner {
    padding-top: 60px
}

footer .foot-nav li a {
    text-decoration: none;
    color: #000
}

footer .social-icon {
    justify-content: space-around;
}



.dlt-data a i,
footer .social-icon li a i {
    font-size: 26px;
    color: #000
}

footer .copy-right {
    text-align: center;
    padding: 20px 0;
    font-size: 12px;
    margin-bottom: 0
}

.bd-top {
    border-top: 1px solid #ededed
}

.bd-bottom {
    border-bottom: 1px solid #ededed
}

.phone-number {
    border: 1px solid #E2E2E2;
    border-radius: 17px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    height: 55px;
}

.phone-number select {
    border: unset;
    background: 0 0;
    margin-right: 8px;
    flex: 0 0 35%;
    max-width: 40%;
    font-size: 17px;
    outline: 0;
    cursor: pointer;
    color: #000;
}

.phone-number input {
    border: unset;
    background: 0 0;
    padding-left: 10px;
    border-left: 1px solid #e2e2e2;
    flex: 0 0 70%;
    max-width: 70%;
}

.phone-number input:focus {
    border: unset;
    outline: unset;
    box-shadow: unset;
    border-left: 1px solid #e2e2e2
}

.phone-number input::placeholder {
    color: #D1D1D1;
}

#signup .phone-number {
    height: 45px;
}

#signup .phone-number select {
    font-size: 14px;
}

.phone-number input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.phone-btn {
    padding: 10px 0;
    line-height: 1;
    font-size: 18px
}

.phone-verification {
    /* padding: 10px 40px 35px */
}

.phone-verification p {
    font-size: 14px;
    color: #909090;
    line-height: 1.4;
    margin: 15px 0;
}

.social-btn-login {
    padding-top: 20px;
    display: flex;
    flex-flow: wrap
}

.host-banner .host-image img,
.listing-counter img,
.listing-place-name,
.listing-upload-file,
.new-listing .left-side .image-listing img,
.pay-img img,
.social-btn-login a,
ul#v-pills-tab li a {
    width: 100%
}

.social-btn-login a button {
    display: flex;
    align-items: center;
    border-radius: 17px;
    padding: 0 12px;
    text-align: left;
    background: 0 0;
    border: 1px solid var(--grey-one);
    height: 47px;
    width: 100%
}

.social-btn-login a button img {
    margin-right: 12px;
    width: 22px;
    object-fit: fill;
    height: 22px
}

.log-user-wd {
    width: 27px !important;
    height: 27px !important
}

.social-btn-login .content {
    flex: 1;
    text-align: center
}

.social-btn-login .content h6 {
    color: #000;
    font-weight: 400;
    font-size: 15px;
    margin-bottom: 0
}

.social-btn-login button i {
    margin-right: 12px;
    font-size: 22px;
    color: #000
}

.prim-btn,
p.comp a,
p.uncomp {
    font-size: 18px
}

.btn-close:hover {
    color: unset !important;
    opacity: unset
}

.btn-close:focus {
    box-shadow: unset
}

.verification-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: none;
    width: 100%;
    height: 100%;
    border: 1px solid #e2e2e2;
    border-radius: 25px;
    padding: 4px 20px
}

.verification-input {
    border: none;
    transition: .2s ease-out;
    width: 30px;
    height: 30px;
    text-align: center
}

.verifi.new-listing .left-side .cont-listing img,
.pay-img img,
.social-btn-login a,
ul#v-pills-tab li a {
    width: 100%
}

.social-btn-login a button {
    display: flex;
    align-items: center;
    border-radius: 17px;
    padding: 0 12px;
    text-align: left;
    background: 0 0;
    border: 1px solid var(--grey-one);
    height: 47px;
    width: 100%
}

.social-btn-login a button img {
    margin-right: 12px;
    width: 22px;
    object-fit: fill;
    height: 22px
}

.log-user-wd {
    width: 27px !important;
    height: 27px !important
}

.social-btn-login .content {
    flex: 1;
    text-align: center
}

.social-btn-login .content h6 {
    color: #000;
    font-weight: 400;
    font-size: 15px;
    margin-bottom: 0
}

.social-btn-login button i {
    margin-right: 12px;
    font-size: 22px;
    color: #000
}

.prim-btn,
p.comp a,
p.uncomp {
    font-size: 18px
}

.btn-close:hover {
    color: unset !important;
    opacity: unset
}

.btn-close:focus {
    box-shadow: unset
}

.verification-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: none;
    width: 100%;
    height: 100%;
    border: 1px solid #e2e2e2;
    border-radius: 25px;
    padding: 4px 20px
}

.verification-input {
    border: none;
    transition: .2s ease-out;
    width: 30px;
    height: 30px;
    text-align: center
}

.verification-field input[type=text]:focus {
    border: unset;
    outline: unset
}

.card-detail .payment-btn,
.margin-top30 {
    margin-top: 30px
}

.host-banner .content p {
    color: var(--theme-middle);
    font-size: 25px;
    line-height: 1.1
}

.host-banner .content button {
    padding: 0 30px;
    font-size: 16px;
    height: 47px
}

/* .host-help {
    padding: 300px 0 0;
    margin-top: -170px;
    z-index: 1
}

.host-help::before {
    content: "";
    position: absolute;
    width: 46%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 99
} */

.host-help .content1 {
    position: relative;
    z-index: 9999;
    height: 100%
}

.host-help .content1 h1 {
    color: #fff;
    font-weight: 400
}

.host-help .content1 button {
    position: absolute;
    bottom: 110px;
    padding: 0 30px;
    background: #fff;
    border-radius: 8px;
    font-size: 14px;
    height: 47px
}

.host-help .help-content {
    padding: 0;
    margin: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.host-help .help-content li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 50px
}

.host-help .help-content li img {
    margin-right: 40px;
    width: 30px
}

.host-help .help-content li:nth-child(3) img {
    width: 33px;
}

.host-help .help-content li .content h5 {
    margin-bottom: 15px;
    color: #575757;
    font-weight: 600;
}

.host-help .help-content li .content p {
    font-size: 15px;
    line-height: 1.2;
    margin-bottom: 0;
}

.property-detail .head-content {
    position: relative;
    margin: 25px 0
}

.property-detail .head-content .wish-icon {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center
}

.property-detail .head-content .wish-icon img {
    width: 18px;
    margin-right: 4px
}

.property-detail .property-gallery .main-image {
    width: 100%;
    height: 400px
}

.inner-image img,
.property-detail .property-gallery .main-image img {
    width: 100%;
    border-radius: 10px;
    border: 1px solid #e3e0e0;
    object-fit: cover;
}

.inner-image {
    width: 100%;
    height: calc(200px - 8px);
    margin-bottom: 15px;
    position: relative
}

.inner-image a {
    display: block;
}

.inner-image .full-view {
    position: absolute;
    background: #0000005e;
    height: 0;
    left: 50%;
    top: 50%;
    width: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    opacity: 0;
    visibility: hidden;
    transition: .3s ease-in-out
}

.inner-image:hover .full-view {
    visibility: visible;
    opacity: 1;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.inner-image .full-view a {
    color: #fff;
    text-decoration: none;
    font-size: 30px;
    font-weight: 300
}

.property-detail .property-inner-detail {
    display: flex;
    margin: 20px 0
}

.property-detail .property-inner-detail .property-description,
.transaction-detail {
    flex: 0 0 50%;
    max-width: 50%
}

.property-detail .property-inner-detail .property-description h5 span {
    color: #13cb99;
    font-size: 14px
}

.listing-question label,
.property-detail .property-inner-detail .property-description li {
    color: var(--them-secondary)
}

.property-detail .property-inner-detail .user {
    flex: 0 0 50%;
    max-width: 50%;
    display: flex;
    align-items: flex-start;
    justify-content: space-around
}

.property-detail .property-inner-detail .user img {
    width: 45px;
    height: 45px
}

.singal-user {
    justify-content: end !important;
    display: flex;
    padding-top: 28px;
}

.singal-user .user-detail {
    margin: 0 20px;
}

.property-detail .property-inner-detail .user .cust-btn {
    padding: 8px 15px
}

.overview {
    margin: 20px 0 40px
}

.property-detail .property-feature-list h4,
.transaction {
    margin: 20px 0
}

.property-detail .property-feature-list ul {
    margin-bottom: 25px !important
}

.property-detail .property-feature-list li {
    display: flex;
    flex: 0 0 50%;
    color: var(--them-secondary);
    font-size: 16px;
    margin-bottom: 15px;
    align-items: center;
}

.property-detail .property-feature-list li p {
    display: inline-block;
    margin-bottom: 0
}

.property-detail .property-feature-list li span {
    color: var(--green-color)
}

.property-detail .property-feature-list li img {
    width: 100%;
    object-fit: cover;
}

.property-pricing {
    padding: 30px;
    border-radius: 25px;
    box-shadow: rgb(0 0 0 / 35%) 0 5px 15px;
    margin-top: 30px;
    position: relative;
}

.pricing-inner .pricing span {
    font-size: 20px;
    color: #575757
}

.pricing-inner form .check {
    border: 1px solid #e2e2e2;
    border-radius: 25px;
    width: 100%
}

.custom-subtotal {
    min-height: max-content;
    max-height: 165px;
    overflow: overlay;
    padding: 0 14px;
    margin: 0 0 10px
}

.pricing-inner form label {
    color: #575757;
    margin-bottom: 6px;
}

.pricing-inner form .reserve {
    width: 100%;
    padding: 10px 0;
    margin-bottom: 15px
}

.calculate-pricing,
.chat-profile-list ul li,
.listing-question .listing-ql,
.popup-calculate-pricing,
.reserv-calculate-pricing {
    margin-bottom: 10px
}

.total-pricing {
    padding-top: 10px;
    border-top: 1px solid #e2e2e2
}

.property-gallery .popup-main-image {
    width: 100%;
    height: 250px
}

.property-gallery .popup-inner-image img,
.property-gallery .popup-main-image img {
    width: 100%;
    border-radius: 10px;
    height: 100%;
    object-fit: cover;
}

.property-gallery .popup-inner-image {
    width: 100%;
    height: calc(125px - 8px);
    margin-bottom: 15px;
    position: relative
}

.property-gallery .popup-inner-image .full-view {
    position: absolute;
    top: 0;
    background: #0000005e;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px
}

.property-gallery .popup-inner-image .full-view a {
    color: #fff;
    text-decoration: none;
    font-size: 18px;
    font-weight: 300
}

.popup-pricing,
.reserv-pricing {
    border: 1px solid #e2e2e2;
    padding: 20px;
    border-radius: 10px
}

.property-detail .property-inner-detail .popup-user {
    flex: 0 0 50%;
    max-width: 50%;
    display: flex;
    align-items: flex-start;
    justify-content: end
}

.property-detail .property-inner-detail .popup-user img {
    width: 45px;
    margin-right: 15px;
    height: 45px;
}

.payment-option {
    margin: 10px 0 15px
}

.popup-check {
    position: relative;
    margin: 30px 0 60px
}

.popup-check img,
.reserv-check img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

.popup-check .check-date p {
    font-size: 14px
}

.popup-check .check-date h6 {
    font-size: 15px
}

.popup-total-pricing {
    border-bottom: 1px solid #e2e2e2;
    padding-bottom: 10px
}

.floated-img img {
    position: absolute;
    width: 17%
}

.floated-img {
    position: relative;
    height: 65vh;
    padding-top: 15px
}

img.fl-1 {
    left: -3%;
    z-index: 8
}

img.fl-2 {
    left: 11%;
    bottom: -1px;
    width: 20%
}

img.fl-3 {
    left: 16%;
    width: 21%;
    top: 8%
}

img.fl-4 {
    left: 33%;
    z-index: 9;
    top: 24%;
    width: 18%
}

img.fl-5 {
    right: 36%;
    top: 50%;
    z-index: 8
}

img.fl-6 {
    right: 24%;
    width: 20%
}

img.fl-7 {
    right: 7%;
    bottom: -21%;
    width: 22%;
    z-index: 4
}

img.fl-8 {
    right: 1%;
    top: 16%;
    width: 22%
}

.banner-content {
    position: absolute;
    bottom: 100px
}

section.home-bg {
    height: 100vh;
    background: url(../images/home-bg.jpg) bottom center/cover no-repeat #f5c33e;
    position: relative
}

/* section.services {
    padding: 40px 0
} */

.prim-btn {
    background: #f5c33e;
    color: #ffff;
    padding: 5px 20px;
    border: 1px solid #f5c33e;
    height: 47px;
    border-radius: 10px
}

.cm-img img,
.ls-img img {
    border-radius: 12px;
    object-fit: cover
}

.list-hd {
    padding: 30px 0
}

.list-hd h1 {
    margin-bottom: 0;
    font-weight: 400
}

.cm-list-item ul,
.text-by .mini-profile.reviews,
ul.side-inner {
    padding: 0
}

ul.side-inner li a {
    text-decoration: none;
    color: #909090;
    font-size: 20px;
    font-weight: 400;
    transition: .3s ease-in;
}

.listing-checkbox h4,
ul.side-inner li {
    margin-bottom: 18px
}

.fc-black,
.on-pg {
    color: #000 !important;
}

.cm-img img {
    width: 100% !important;
    height: 180px
}

p.uncomp {
    text-align: left;
    color: #f45e5e;
    font-weight: 400
}

p.comp a {
    color: #23bc4c
}

.booking_table td,
.ls-actions,
.text-right,
p.comp {
    text-align: right
}

.fs-20,
.policy-mid-content,
.shared-room-info .filter-check .filter-check-inner p {
    font-size: 20px
}

.fw-400,
.newlstng-total-guest .total-guest h4,
.newlstng-total-guest .total-guest input,
.reserv-product-detail {
    font-weight: 400
}

.dt-today .final_day,
.fw-500,
.ls-edits a,
.underline-btn,
label.listed,
table.dataTable tfoot th,
table.dataTable thead th {
    font-weight: 500
}

.fc-gray {
    color: #909090;
}

.fs-18 {
    font-size: 18px
}

.cm-list,
.deselect-on-click,
.filter-check .filter-check-inner {
    margin-bottom: 20px
}

.ls-img img {
    height: 107px;
    width: 100%
}

.ls-item {
    margin-bottom: 10px;
    padding: 15px;
    border: 1px solid #d5d5d5b3;
    border-radius: 10px;
}

.ls-edits a {
    color: #000;
    font-size: 17px;
    margin-left: 20px
}

.ls-edits a img {
    margin-left: 4px;
    position: relative;
    bottom: 3px;
    width: 18px;
    object-fit: fill
}

.switch {
    display: inline-block;
    height: 25px;
    position: relative;
    width: 47px
}

.slider-list {
    background-color: #ccc0;
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
    border: 3px solid
}

.slider-list::before {
    background-color: #000;
    bottom: 2px;
    content: "";
    height: 15px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 15px
}

.ls-toggle input:checked+.slider-list {
    background-color: #66bb6a00;
    border-color: #23bc4c;

}

.ls-toggle input:checked+.slider-list:before {
    transform: translateX(19px);
    background: #23bc4c
}

.slider-list.round {
    border-radius: 34px
}

.slider-list.round:before {
    border-radius: 50%
}

label.listed {
    font-size: 18px;
    cursor: pointer;
    margin-right: 13px
}

.text-success {
    color: #23bc4c;
}

.ls-toggle input:checked+.listed {
    color: #23bc4c !important;
    position: absolute;
    top: 0;
    right: 0;
}

.ls-action .ls-toggle {
    margin-top: 60px
}

.ls-toggle,
.req-status,
.reserv-head .reserv-head-btn {
    display: flex;
    align-items: center;
    justify-content: end
}

.list-manage {
    padding: 10px 0 50px;
}

.side-menu-inner {
    height: 100%;
    border-right: 1px solid #bfbbbb8f
}


.list-descrip {
    overflow: hidden;
    font-size: 18px;
    margin: 5px 0 0;
    line-height: 1.3;
}

.list-price {
    font-size: 18px;
    margin-top: 5px
}

.list-status {
    font-size: 17px;
    margin-top: 2px
}

.rcpt-btn {
    color: #000;
    position: relative;
    transition: .3s;
    font-size: 16px
}

.rcpt-btn::before {
    content: "";
    position: absolute;
    right: 0;
    bottom: 0;
    height: 1px;
    width: 82%;
    margin-left: auto;
    background-color: #000;
    transition: .3s
}

.listing-link-btn {
    padding: 0 45px !important;
    height: 40px !important;
    font-size: 17px !important;
    font-weight: 500 !important
}

.list-nav-pills {
    height: unset !important
}

.filter-check .filter-check-inner p {
    color: var(--theme-middle);
    font-weight: 500
}

.fltr-chk-btn {
    padding: 10px 0;
    width: 95%;
    margin: 0 auto
}

/*
.popup {
  box-sizing: border-box;
  position: absolute;
  background: #fff;
  padding: 50px;
  border-radius: 25px;
  box-sizing: border-box;
  top: 25px;
  box-shadow: 1px 4px 10px 0 #00000059
} */

.h-position {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1
}

.new-listing {
    height: 100vh;
    overflow: hidden
}

.new-listing .left-side {
    height: 100vh;
    /* background: var(--theme-primary); */
    padding: 60px 0px;
}

.left-inner-side {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    /* height: 100vh; */
}

.new-listing .right-side {
    height: 100vh;
    padding: 0;
    background-color: #fff;
    position: relative
}

.alrt-position {
    position: absolute;
    width: 75%;
    top: 60px;
    margin: 0 auto;
    z-index: 1;
    left: 0;
    right: 0;
}

.listing-upload-head h3,
.loc-drop h6,
.map-content {
    margin-bottom: 0
}

.close-error {
    width: 3px;
    height: 3px;
    font-size: 7px;
    position: absolute;
    top: 5px;
    right: 5px
}

.new-listing .right-side .right-inner-side {
    height: 100%;
    position: relative
}

.right-content {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: overlay;
    padding: 0 0px
}

.limit-para,
.listing-checkbox-content,
.ntf-main .ntf-content p,
.ntf-user h6,
.title h4,
select {
    overflow: hidden
}

.listing-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 100px;
    background: #ffff;
    z-index: 99;
}

.listing-footer-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%
}

.listing-footer-map {
    position: sticky
}

.new-listing .left-side .cont-listing {
    width: 100%;
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-listing .left-side .left-sec-content h1 {
    color: #fff;
    font-weight: 400;
    font-size: 50px
}

.book-img,
.change-pro,
.listing-counter-main,
.main-logo,
.new-listing .right-side .add-lst-content,
.review-user,
.swal-footer {
    text-align: center
}

.new-listing .right-side .add-lst-content h1 {
    font-weight: 400;
    font-size: 45px;
    margin-bottom: 30px;
    line-height: 1.1
}

.new-listing .right-side .add-lst-content p {
    font-size: 20px;
    line-height: 1.1
}

.black-btn {
    padding: 0 !important;
    position: relative
}

.black-btn a {
    color: var(--theme-middle) !important
}

.black-btn img {
    width: 8px;
    position: relative;
    top: 5px;
    transform: translateY(-50%);
    left: 0px;
    margin-right: 7px;
}

.listing-category {
    text-align: center;
    width: 100%
}

.listing-category .category input[type=radio] {
    position: absolute;
    opacity: 0
}

.listing-category .category {
    cursor: pointer;
    width: 45%;
    margin-bottom: 30px;
}

.listing-category .category .category-content {
    padding: 35px 20px;
    border: 2px solid #d3d3d3;
    border-radius: 10px;
    position: relative;
    background: #ffffff;
}

.listing-category .category .category-content img {
    position: absolute;
    left: 40px;
    top: 50%;
    transform: scale(1) translateY(-50%);
    width: 35px
}

.listing-category .category .category-details span {
    display: block;
    font-size: 18px;
    line-height: 24px;
    color: var(--theme-middle)
}

.listing-category .category input[type=radio]:checked+.category-content {
    border: 2px solid #000
}

.listing-category .category input[type=radio]:checked+.category-content img {
    filter: brightness(0);
    transform: scale(1.1) translateY(-50%)
}

.ac-img,
.left-arrow-title {
    position: relative;
    text-align: center
}

.left-arrow-title img {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    padding: 5px
}

.listing-counter input,
.listing-map-popup {
    left: 50%;
    transform: translateX(-50%);
    outline: 0
}

.left-arrow-title h6 {
    margin-bottom: 0;
    font-size: 20px;
    font-weight: 600
}

.shared-room-info label {
    color: #575757;
    font-size: 16px
}

.listing-map {
    height: calc(100vh - 85px);
    position: relative
}

.p-100 {
    padding: 0 100px
}

.listing-map-popup {
    position: absolute;
    top: 13%;
    width: 90%;
    border: 1px solid #e1e1e1;
    background-color: #fff;
    border-radius: 25px;
    box-shadow: 2px 5px 9px 3px #0000000f;
}

.listing-form-main {
    padding: 15px 0
}

.listing-add-field {
    padding: 0 30px 25px
}

.listing-form {
    padding: 0 35px 50px
}

.map-content {
    line-height: 1.2
}

.listing-form button {
    padding: 13px 0;
    font-size: 18px
}

.loc-drop h6 {
    font-size: 20px;
    font-weight: 400
}

.collapsed .sh-2,
.daterangepicker .cancelBtn,
.dropzone .dz-preview.dz-error .dz-error-message,
.fav-in.active img.fav-blank,
.loader-bg,
.sh-1,
.slider.slider-horizontal .slider:before,
span.drp-selected {
    display: none !important
}

.collapsed .sh-1,
.fav-in.active img.fav-fill {
    display: inherit !important
}

.newlstng-total-guest {
    width: 100%;
    margin-bottom: 30px;
    width: calc(100% - 10px)
}

.newlstng-total-guest .total-guest {
    margin-bottom: 10px;
    padding: 15px 0 30px;
    width: 100%
}

.home-cont,
.listing-checkbox-main {
    height: 100%
}

.listing-checkbox {
    margin-bottom: 30px;
    margin-right: -10px;
    width: calc(100% + 10px)
}

.listing-checkbox-input {
    position: absolute;
    visibility: hidden
}

.listing-checkbox-input:checked+.listing-checkbox-tile {
    border-color: var(--theme-middle);
    box-shadow: 0 5px 10px rgba(0, 0, 0, .1);
    color: var(--theme-middle)
}

.listing-checkbox-input:checked+.listing-checkbox-tile .listing-checkbox-icon,
.listing-checkbox-input:checked+.listing-checkbox-tile .listing-checkbox-label {
    filter: brightness(0)
}

.listing-checkbox-tile {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    border: 1px solid #909090;
    background-color: #fff;
    box-shadow: none;
    transition: .15s;
    cursor: pointer;
    position: relative
}

.listing-checkbox-tile:hover {
    border-color: #000
}

.listing-checkbox-icon {
    transition: 375ms;
    color: var(--them-secondary);
    margin-bottom: 10px;
    width: 35px;
    height: 25px;
    margin-right: 10px;
}

.listing-checkbox-icon img {
    width: 100%;
    height: 100%;
    object-fit: fill
}

.listing-checkbox-label {
    color: var(--them-secondary);
    transition: 375ms;
    text-align: center;
    font-size: 15px;
    height: 40%;
    display: flex;
    align-items: center;
    margin-top: 5px
}

.listing-upload-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50px
}

.listing-upload-head .upload-btn {
    padding: 12px 25px
}

.listing-upload-body {
    display: flex;
    align-items: center;
    flex-flow: wrap
}

.upload-image-full {
    width: 100%;
    height: 300px;
    margin-bottom: 15px;
    border-radius: 10px;
    position: relative
}

.dotted-bd {
    border: 1px dashed var(--them-secondary);
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px
}

.upload-image-full .cover-photo {
    height: 100%;
    width: 100%;
    border-radius: 10px;
    object-fit: cover
}

.upload-image-half-main {
    width: calc(100% + 15px);
    margin-left: -15px;
    display: flex;
    align-items: center
}

.upload-image-half,
.upload-photo {
    align-items: center;
    position: relative;
    display: flex
}

.upload-image-half {
    height: 100px;
    width: calc(33.33% - 15px);
    margin-left: 15px;
    justify-content: center;
    border-radius: 10px
}

.sm-image {
    width: 30px;
    height: 30px;
    object-fit: fill
}

.upload-image-half img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px
}

.photo-btn-bg {
    background-color: #fff;
    box-shadow: rgb(0 0 0 / 35%) 0 0 10px;
    width: 5px;
    height: 5px;
    font-size: 7px
}

.upload-photo {
    width: 100%;
    height: 100%;
    justify-content: center;
    cursor: pointer
}

.upload-photo input[type=file] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer
}

.listing-place-name label {
    font-size: 28px;
    margin-bottom: 30px
}

.listing-counter-main h3,
.listing-counter-main h4 {
    margin: 20px 0;
    color: var(--theme-middle);
    text-align: center
}

.listing-place-name .listing-input {
    border-radius: 8px;
    height: 90px;
    padding: 10px 12px 0 12px;
    position: relative;
    border-color: var(--theme-middle);
    width: 100%;
    resize: none
}

.listing-place-name .listing-input::placeholder {
    font-size: 16px;
    color: var(--theme-middle)
}

.listing-counter .minus,
.plus {
    width: auto
}

.pr-cont {
    width: 70%;
    margin: 10px auto 30px
}

.listing-group {
    position: relative;
    width: 70%;
    margin: 0 30px;
    height: 90px
}

.listing-inner-group {
    border: 2px solid var(--them-secondary);
    border-radius: 10px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative
}

.listing-inner-group input {
    pointer-events: revert !important
}

.listing-currency {
    font-size: 28px;
    color: var(--them-secondary);
    cursor: default !important;
    width: 30%;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    text-align: center
}

.listing-night {
    right: 0;
    left: unset;
    font-size: 25px;
    padding-right: 14px
}

.listing-counter input {
    font-size: 55px;
    height: auto;
    font-weight: 400;
    position: absolute;
    width: 50%
}

.guest-counter input:focus,
.listing-counter input:focus {
    border: none;
    outline: 0
}

.listing-position {
    position: absolute;
    width: 100%;
    right: -200%;
    background-color: #fff;
    top: 0;
    height: calc(100% - 100px);
    padding: 60px 150px
}

.lstng-close-btn {
    position: absolute;
    top: 40px;
    right: 40px
}

.listing-question h3 {
    margin-bottom: 30px
}

.listing-question .listing-ql p {
    font-size: 16px
}

.fc-green {
    color: #23bc4c
}

#room-detail-map,
.map-view {
    height: 600px;
    width: 100%
}

.pop-img img {
    height: 129px !important;
    border-radius: 10px;
    width: 129px !important;
    object-fit: cover;
    margin-bottom: 10px
}



button.submit-header {
    background: #f5c33e00;
    border: none;
    color: #f5c33e;
    border-radius: 50%;
    padding: 0;
    font-size: 39px;
}

.card-dt,
.text-by,
.wallet,
.whatsapp-icon {
    display: flex;
    align-items: center
}

.visa i.ri-visa-line {
    color: #1434cb;
    font-size: 43px
}

.prev-btn,
i.ri-visa-line {
    margin-right: 20px
}

.card-numbers {
    font-size: 18px;
    font-weight: 500;
    margin-right: 40px
}

.dlt-data {
    margin-left: 30px
}

section.payment {
    padding: 80px 0
}

.mb-6 {
    margin-bottom: 6em
}

.gray-card {
    background: #f8f8f8;
    padding: 15px 20px;
    border-radius: 12px;
    margin-bottom: 10px
}

.wall-cred p {
    margin: 0
}

.price span {
    color: green;
    font-weight: 500;
    margin-left: 20px
}

.gray-card .table>:not(caption)>*>* {
    border: none
}

.gray-card th,
.gray-card th a {
    font-size: 20px;
    font-weight: 400;
    color: #282828
}

.gray-card td {
    font-size: 18px !important;
    font-weight: 400 !important;
    color: #282828 !important
}

.daterangepicker {
    background: #fff;
    padding: 40px 50px;
    border-radius: 30px;
    font-family: 'dubai-font' !important;
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
    background: #000;
    border-radius: 35px !important;
    /* padding: 4px 12px !important */
}

.daterangepicker .calendar-table th,
.heade-date .daterangepicker .calendar-table td {
    /* padding: 8px !important */
}

.daterangepicker .calendar-table td,
.daterangepicker .calendar-table th {
    width: 40px;
    height: 40px;
    border-radius: 0;
}

td.active.end-date.in-range.available {
    background: #000;
    /* padding: 4px 12px !important */
}

.heade-date input {
    font-size: 17px;
    background: 0 0;
    border: none;
    font-weight: 500;
    height: 100%;
    width: 100%;
    text-align: center;

}

section.request-booking,
section.trans-table,
section.wishlist {
    padding: 60px 0
}

.white-list-overlay {
    position: absolute;
    top: 0;
    height: 100%;
    width: 100%;
    text-align: right;
    z-index: 99;
}

.alert-modal,
.loader {
    text-align: center
}

.property .product .image {
    border-radius: 9px;
    overflow: hidden
}

a.closed-whish {
    color: #fff;
    font-size: 16px;
    transition: .6s;
    transform: translate(151px, -3px);
    display: block
}

a.closed-whish i {
    font-size: 22px;
    position: relative;
    top: 5px
}

.property .product .image:hover a.closed-whish {
    transform: translate(-10px, -3px)
}

.property .product .image:hover .white-list-overlay {
    background: #00000017
}

.btn-primary,
.btn-primary:hover {
    background: #f5c33e;
    border-color: #f5c33e
}

.tb-date {
    font-size: 14px;
    color: #909090;
    margin-bottom: 15px
}

.tb-price {
    color: #575757;
    font-size: 14px
}

td.booked {
    background: #ededed
}

.sync a i {
    position: relative;
    top: 3px
}

.booking_table th {
    text-align: right;
    font-weight: 400;
    padding: 20px 8px
}

.md-sc {
    padding-bottom: 100px
}

.btn-success {
    background: #23bc4c
}

.alert-modal {
    padding-bottom: 15px;
    padding-left: 15px;
    padding-right: 15px
}

.acc-inner ul li .ac-act a:nth-child(3),
.text-danger {
    color: #f45e5e !important
}

.btn-danger {
    background: #f45e5e !important
}

/* select {
  overflow: -moz-hidden-unscrollable;
  background: url(../icons/down-arrow.svg) 100% 50% no-repeat #fff !important;
  -webkit-appearance: none;
} */

.date-rng {
    padding: 10px
}

.notification-drop a {
    color: #000;
    font-size: 20px;
    position: relative;
    margin-right: 20px
}

.star-rating {
    direction: rtl;
    display: inline-block;
    cursor: default
}

.star-rating label {
    color: #bbb;
    font-size: 2rem;
    padding: 0;
    cursor: pointer;
    transition: .3s ease-in-out
}

.fc-prime,
.star-rating input[type=radio]:checked~label,
.star-rating label:hover,
.star-rating label:hover~label {
    color: #f5c33e
}

.review textarea {
    height: 170px;
    border-radius: 10px;
    resize: none;
    margin-bottom: 20px;
    margin-top: 20px;
    padding: 12px
}

.rating-top {
    margin-bottom: 9px;
    margin-top: -10px
}

.daterangepicker .drp-calendar.left {
    clear: left;
    margin-right: 30px;
}

.notification-drop a img {
    width: 23px
}

.notify {
    background: red;
    position: absolute;
    height: 8px;
    width: 8px;
    border-radius: 19px;
    animation: 2s infinite pulse-animation;
    top: 0;
}

@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, .2)
    }

    100% {
        box-shadow: 0 0 0 15px transparent
    }
}

.req-status button {
    margin-right: 15px
}

.fltr-rang {
    position: relative;
    justify-content: space-between
}

.flt-in {
    position: relative;
    height: 40px
}

.fav-in,
.reserv-date i,
a.float-anc {
    position: absolute
}

.slider.slider-horizontal {
    border: none;
    width: 100%;
    background: 0 0
}

.slider-selection {
    background-color: #f5c33e;
    background-repeat: repeat-x;
    box-shadow: none !important
}

.slider-handle {
    background-color: #f5c33e !important
}

.price-btn {
    border: 1px solid #c5c5c5;
    padding: 7px 15px;
    border-radius: 7px;
    color: #181818;
    font-size: 13px;
}

.price-btn span {
    font-weight: 500;
    font-size: 17px;
}

.price-btn p {
    color: #8A8A8A;
    font-size: 16px;
}

.slider.slider-horizontal .tooltip-inner {
    left: 0 !important;
    right: 0 !important;
    width: 100%;
    margin: 0 auto
}

.fav-in {
    top: 3px;
    right: 7px;
    cursor: pointer;
    z-index: 99
}

.loader-bg,
.whatsapp-icon {
    position: fixed;
    background: #fff
}

.form-check-input:checked[type=radio] {
    background-image: url("../icons/radio-button-line.svg") !important;
    background-size: 115%
}

.form-check-input:checked[type=radio]:focus {
    box-shadow: none !important;
    outline: none !important;
}

.pr-radiobtn .form-check-input:checked[type=radio] {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNSIgaGVpZ2h0PSIyNSIgdmlld0JveD0iMCAwIDI1IDI1Ij4NCiAgPGcgaWQ9Ikdyb3VwXzEyMSIgZGF0YS1uYW1lPSJHcm91cCAxMjEiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0xMDI0IC01NjgpIj4NCiAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlXzYzIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSA2MyIgd2lkdGg9IjExIiBoZWlnaHQ9IjExIiByeD0iNS41IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMDMxIDU3NSkiLz4NCiAgICA8ZyBpZD0iUmVjdGFuZ2xlXzYzLTIiIGRhdGEtbmFtZT0iUmVjdGFuZ2xlIDYzIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMDI0IDU2OCkiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzAwMCIgc3Ryb2tlLXdpZHRoPSI1Ij4NCiAgICAgIDxyZWN0IHdpZHRoPSIyNSIgaGVpZ2h0PSIyNSIgcng9IjEyLjUiIHN0cm9rZT0ibm9uZSIvPg0KICAgICAgPHJlY3QgeD0iMi41IiB5PSIyLjUiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgcng9IjEwIiBmaWxsPSJub25lIi8+DQogICAgPC9nPg0KICA8L2c+DQo8L3N2Zz4NCg==") !important;
}

.cust-check .form-check-input[type=radio] {
    width: 20px;
    height: 20px
}

.form-check-input:checked~img {
    box-shadow: rgba(0, 0, 0, 0.35) 0px 2px 10px;
}

.form-check-input:focus {
    box-shadow: none;
    border-color: rgba(0, 0, 0, .25) !important
}

.pricing-inner form .check {
    padding: 10px 15px !important
}

.reserv-date i {
    right: 18px;
    top: 7px;
    font-size: 21px
}

.whatsapp-icon {
    right: 30px;
    width: 50px;
    height: 50px;
    justify-content: center;
    border-radius: 50%;
    bottom: 30px;
    border: 1px solid #ebebeb;
    box-shadow: 1px 1px 13px 3px #0000001c;
    z-index: 100
}

.loader,
.ntf-header .total-ntf {
    display: flex;
    align-items: center
}

.simple-cancel img,
.whatsapp-icon img {
    width: 22px
}

.loader,
.loader-bg,
a.float-anc {
    width: 100%;
    height: 100%
}

.mr-3 {
    margin-right: 3em
}

a.float-anc {
    z-index: 10
}

.loader-bg {
    z-index: 999999;
    top: 0
}

.loader {
    justify-content: center;
    position: absolute
}

.daterangepicker.opensright:before {
    left: 22px
}

.daterangepicker.opensright:after {
    left: 23px
}

.check-icon img {
    width: 18px;
    margin-left: 20px
}

.cy-positon {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex
}

.ntf-main {
    width: 300px !important;
    border: 1px solid #ddd;
    box-shadow: rgb(0 0 0 / 30%) 0 0 4px;
    border-radius: 10px;
    padding: 0;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    transform: translate(-138px, 42px) !important
}

.ntf-header,
.ntf-main li {
    border-bottom: 1px solid #e2e2e2
}

.notification-drop,
.ntf-header {
    position: relative;
    text-align: center
}

.ntf-header {
    padding: 10px 0
}

.ntf-header .total-ntf {
    position: absolute;
    background: var(--theme-primary);
    color: #fff;
    border-radius: 50%;
    font-size: 11px;
    width: 20px;
    height: 20px;
    justify-content: center;
    top: 50%;
    left: 72%;
    transform: translate(-50%, -50%)
}

.no-ntf,
.ntf-main .ntf-item {
    align-items: center;
    display: flex
}

.ntf-inner {
    min-height: max-content;
    max-height: 240px;
    overflow: auto;
    margin-bottom: 0
}

.ntf-main li:last-child {
    border-bottom: none;
    margin-bottom: 0
}

.ntf-main .ntf-item {
    margin: 0;
    padding: 8px 15px;
    white-space: unset !important
}

.card-logo,
.ntf-main .ntf-image {
    flex: 0 0 20%;
    max-width: 20%
}

.ntf-main .ntf-image img {
    width: 80%;
    margin: 0 auto
}

.ntf-main .ntf-content {
    flex: 0 0 80%;
    max-width: 80%
}

.ntf-user {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.ntf-user h6 {
    margin-bottom: 0;
    font-size: 14px;
    flex: 1;
    display: -webkit-box;
    margin-right: 13px
}

.ntf-user p {
    font-size: 12px
}

.ntf-main .ntf-content p {
    margin-bottom: 0;
    font-size: 14px
}

.ntf-see {
    border-top: 1px solid #e2e2e2;
    text-align: center;
    padding: 8px 0
}

.ntf-see a {
    display: inline;
    font-size: 14px;
    text-decoration: revert
}

.acc-inner ul li .ac-act a,
.change-pro a,
.clndr-btn,
.transaction-head button,
span.rd-more {
    text-decoration: underline
}

.no-ntf {
    justify-content: center;
    text-align: center;
    height: 100%
}

.no-ntf img {
    display: block;
    width: 80px;
    margin: 0 auto
}

.no-ntf p {
    margin-bottom: 0;
    font-weight: 500;
    font-size: 17px
}

.change-pro a,
.chat-date,
.date-mark,
.nav-pills .nav-link,
.phone-slt,
.text-by h4,
.title h4,
span.pro-date {
    font-weight: 400
}

.calendar-month {
    width: 100%;
    padding: 20px;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

.calendar-month .current-month-selection {
    display: inline-block;
    position: relative;
    cursor: pointer;
    margin: 0 5px;
}

.calendar-month .current-month-selection select {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    -ms-filter: "alpha(opacity=0)"
}

.calendar-month .current-month-selection .current-month-arrow {
    font-size: 24px;
    position: relative;
    top: 4px;
    text-rendering: optimizeLegibility
}

.calendar-month .month-nav {
    border: 1px solid #ddd;
    display: inline-block
}

.calendar-month .month-nav.disabled {
    color: #d7d7d7;
    border-color: #e6e6e6;
    cursor: default
}

.calendar-month .month-nav.disabled:hover {
    color: #d7d7d7
}

.calendar-month .month-nav .icon {
    width: 46px;
    height: 42px;
    line-height: 42px
}

/*
.calendar-month .month-nav:hover {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: #fff;
  text-decoration: none
} */

.calendar-month .spinner-next-to-month-nav {
    position: absolute;
    top: 18px;
    left: 100%;
    width: 35px;
    margin-left: 13px;
    font-size: 0;
    color: transparent
}

.calendar-month.calendar-placeholder {
    min-height: 456px
}

.col-md-02 {
    position: relative;
    min-height: 1px;
    padding-right: 0;
    padding-left: 0;
    margin-left: -1px;
    float: left;
    width: 14.333%
}

.calender_box {
    border: 1px solid #d4d4d4;
    padding: 5px;
    height: 90px;
    border-top: 0;
    text-align: right
}

.dt-not-available {
    background-color: #ededed;
    cursor: not-allowed;
    pointer-events: none
}

.dt-available-with-events {
    background-color: #eeae9c
}

.tile {
    width: 100%;
    background: #fff;
    border-radius: 5px;
    float: left;
    transform-style: preserve-3d
}

.card-box {
    border: 1px solid #ededed;
    padding: 60px;
    border-radius: 10px;
    box-shadow: 4px 6px 13px 6px #0000001c
}

.card-box textarea {
    height: 170px;
    padding: 17px
}

.md-clndr-icon {
    right: 12px;
    left: unset
}

.md-clndr-icon img {
    margin: 0;
    width: 15px
}

.swal-footer button {
    width: 100px
}

button.swal-button.swal-button--confirm.btn.vbtn-outline-success.text-16.font-weight-700.pl-5.pr-5.pt-3.pb-3.swal-button--danger {
    background: 0 0;
    color: #4bb543;
    border: 1px solid #4bb543
}

input.date-modal:focus,
.input-field input:focus {
    outline: 0 !important;
    border: 2px solid #d5d5d5;
}

.nav-fixed {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1050;
    /* animation: .3s linear sticky; */
}

.date-before {
    position: fixed;
    width: 100%;
    height: 100%;
    background: #000;
    left: 0;
    right: 0
}

.nav-header {
    transition: .2s
}

.btn-sc-calendar button {
    color: #fff;
    width: 100%
}

.calendar-month {
    padding-top: 0
}

.wkText {
    color: #414141;
    padding: 3px;
    text-align: right;
    margin-bottom: 10px;
    width: 100%
}

.add-card-btn,
.n-msg,
.no-booking,
.send-btn {
    text-align: center
}

.calendar-month .month-nav {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: #909090;
    border-color: #909090;
}

.page-link:focus,
.page-link:hover {
    background-color: var(--theme-primary) !important;
    border-color: var(--theme-primary) !important;
    color: #fff !important
}

.calendar-month .month-nav i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 19px
}

.current-month-selection {
    border: 1px solid #909090;
    border-radius: 38px
}

.current-month-selection h2 {
    font-size: 16px;
    font-weight: 400;
    color: gray
}

.calendar-month .current-month-selection h2 {
    padding: 3px 24px
}

.pagination {
    --bs-pagination-border-radius: 25px !important;
    --bs-pagination-color: #000 !important;
    --bs-pagination-active-bg: var(--theme-primary) !important;
    --bs-pagination-active-border-color: var(--theme-primary) !important;
    --bs-pagination-active-color: #fff !important;
    --bs-pagination-border-width: 2px !important;
    margin-bottom: 0;
    justify-content: end;
    flex-flow: wrap;
    align-items: center;
}

.pagination .pre-btn,
.ne-btn {
    width: max-content !important;
    height: max-content !important;
    border: none !important;
}

.pagination .pre-btn:hover,
.ne-btn:hover {
    color: #000 !important;
}

.pagination .page-item:first-child .page-link:hover,
.pagination .page-item:last-child .page-link:hover {
    background-color: transparent !important;
}

.page-link {
    margin-right: 7px !important;
    border-radius: 50% !important;
    padding: 0 !important;
    margin-bottom: 7px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center
}

.page-link:focus {
    box-shadow: none !important
}

.floated-img {
    -webkit-animation: 3s infinite alternate action;
    animation: 3s infinite alternate action
}

@-webkit-keyframes action {
    0% {
        transform: translateY(0)
    }

    100% {
        transform: translateY(-10px)
    }
}

@keyframes action {
    0% {
        transform: translateY(0)
    }

    100% {
        transform: translateY(-10px)
    }
}

.title h4 {
    font-size: 20px;
    display: -webkit-box;
    flex: 0 0 78%;
    max-width: 78%;
    margin-bottom: 0
}

/* .home-cont h1 {
    font-size: 40px
} */

.host-sc img.fl-9 {
    left: 47px;
    width: 37%;
    bottom: -20px;
    z-index: 9
}

.host-sc img.fl-10 {
    left: 280px;
    width: 35%;
    bottom: -174px;
    z-index: 8
}

.host-sc img.fl-11 {
    top: 0;
    width: 40%;
    right: 50px
}

.host-sc img.fl-12 {
    bottom: -141px;
    width: 48%;
    z-index: 4;
    right: -259px
}

.host-sc img.fl-13 {
    right: -340px;
    top: 27px;
    width: 40%
}

/* .host-banner {
    height: 700px;
    overflow-x: hidden
} */

/* .floated-img-2 {
    position: relative;
    padding-top: 15px;
    height: 500px
}

.floated-img-2 img {
    position: absolute;
    width: 18%
} */

.pr-img img,
.text-by .pr-img img {
    width: 40px;
    height: 40px
}

.date-mark {
    font-size: 16px;
    margin-bottom: 6px;
    color: #909090
}

.date-mark span {
    margin-right: 9px
}

.mini-profile {
    display: flex;
    align-items: center;
    padding: 10px 0 0;
    justify-content: end
}

.custom-subtotal,
.guest-list-item ul,
ul {
    padding-left: 0
}

.mr-14,
.pr-img {
    margin-right: 14px
}

.pr-img img {
    border-radius: 50%
}

.pr-mini-detail h4 {
    font-size: 18px;
    margin-bottom: 0px;
    text-align: left
}

.pr-mini-detail p {
    font-size: 16px;
    margin-bottom: 0
}

.fs-30 {
    font-size: 30px
}

.fs-16 {
    font-size: 16px !important
}

.tg-box {
    padding-top: 10px
}

.grey-btn2 {
    height: 47px;
    background: #ededed;
    border: 2px solid #ededed;
    padding: 8px 25px;
    border-radius: 10px;
    color: #000;
    font-size: 18px
}

::-webkit-scrollbar {
    width: 10px
}

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px #e7e7e7;
    border-radius: 10px
}

::-webkit-scrollbar-thumb {
    background: var(--theme-primary);
    border-radius: 10px
}

.chat-lst:hover,
.chat-pop {
    background: #f8f8f8
}

.fl-right,
.reserve-calendar .drp-calendar.right {
    float: right
}

.chat-inner-1,
.reserve-calendar.drp-calendar.left {
    float: left
}

.pro-img img {
    height: 120px;
    width: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #dbd9d996;
}

.pro-img,
.reserv-head-inner {
    margin-bottom: 15px
}

.change-pro a {
    color: #575757;
    font-size: 16px;
    display: block;
    font-weight: 500;
}

span.pro-date {
    font-size: 15px;
    color: #909090
}

ul {
    list-style: none
}

.acc-inner ul li .ac-dt {
    padding: 13px 0 0;
    border-bottom: 1px solid #dbd9d9;
    position: relative
}

/* .acc-inner ul li:last-child .ac-dt::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: 6px;
  border-bottom: 1px solid #dbd9d9;
  width: 100%
} */

.acc-inner ul li:last-child .ac-dt h4,
.listing-tabs-head {
    padding-bottom: 15px
}

.acc-inner ul li .ac-dt h4,
.acc-inner ul li .ac-dt p {
    margin: 0 0 15px
}

.acc-inner ul li .ac-act {
    position: absolute;
    top: 12px;
    right: 0
}

.acc-inner ul li .ac-act a:nth-child(2) {
    margin-right: 8px;
    color: #23bc4c !important
}

.acc-inner ul li .ac-dt input {
    border: unset;
    outline: unset;
    border-bottom: 1px solid;
    width: 100%;
    padding-bottom: 13px;
    font-size: 18px;
    transition: .3s
}

.acc-inner ul li .ac-dt .gender-slt {
    outline: 0;
    border: none;
    width: 100%;
    border-bottom: 1px solid;
    padding-bottom: 13px;
    font-size: 18px
}

.acc-inner ul li .ac-dt textarea {
    width: 100%;
    font-size: 18px;
    border: unset;
    resize: none;
    height: 100px;
    outline: 0;
    position: relative;
    border-bottom: 1px solid
}

.phone-slt-input {
    display: flex
}

.phone-slt {
    border: none;
    font-size: 17px;
    outline: 0 !important;
    width: 83px
}

.lst-lgin-input {
    width: 86.6% !important
}

.phone-focus {
    border-bottom: 1px solid;
    padding-bottom: 15px;
}

img.fl-check {
    position: absolute;
    bottom: -18px;
    right: 17%;
    transform: translateX(-50%)
}

.lg-row {
    border-bottom: 1px solid #dbd9d9;
    padding-bottom: 15px
}

.mini-profile.reviews {
    justify-content: start
}

.tabs-sc .nav-pills {
    border-radius: 35px;
    color: #000
}

.tabs-sc .nav-pills .nav-link {
    border-radius: 34px;
    padding: 10px 30px;
    height: 100%;
    font-size: 18px;
    color: gray
}

.fs-40 {
    font-size: 40px
}

section.about {
    padding: 50px 8px
}

section.about p {
    font-size: 18px;
    color: #000
}

.Inbox {
    padding: 35px 0 60px
}

.chat-profile-list {
    overflow: overlay;
    padding-right: 20px;
    max-height: calc(70vh - 73px);
    min-height: min-content
}

.chat-lst {
    padding: 10px 20px;
    border-radius: 10px;
    transition: .3s
}

.chat-img {
    width: 41px;
    height: 41px
}

.chat-inner-1,
.chat-inner-2 {
    width: 70%;
    margin-bottom: 30px
}

.chat-inner-2 .chat-pop {
    /* background-color: #e2e2e2 !important; */
    background-color: #e2e2e2b8 !important;
}

.chat-img img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 1px solid transparent;
    object-fit: cover;
    transition: .3s
}

.chat-name {
    flex: 1;
    margin-left: 15px;
    position: relative;
}

.chat-name h6 {
    color: #7B8793;
    font-size: 14px;
    line-height: 1.1;
}

.chat-name p {
    margin: 0;
    color: #909090;
    transition: .3s
}

.chat-lst:hover .chat-img img {
    border: 1px solid #909090
}

.chat-pop {
    font-size: 14px;
    padding: 10px 15px;
    border-radius: 10px
}

.chat-inner-2 {
    float: right
}

.chat-date {
    font-size: 12px;
    color: #a3a3a3;
    margin-top: 7px;
    padding-left: 10px
}

.main-chat {
    height: 70vh
}

.chat-msg {
    padding: 0 50px 0 30px;
    overflow: hidden;
    height: calc(100% - 60px)
}

.chat-field {
    justify-content: space-between;
    padding: 13px 50px 0;
    flex-flow: wrap;
}

.send-btn,
.slide-btn {
    align-items: center;
    display: flex
}

.send-btn {
    width: 40px;
    height: 40px;
    background: #f5c33e;
    color: #ffff;
    border-radius: 50%;
    justify-content: center;
    font-size: 18px;
    border: 1px solid transparent;
    transition: .4s
}

.chat-profile-list ul li a,
.reserv-check {
    border-radius: 10px
}

a.send-btn:hover {
    color: #fff;
}

.chat-text {
    resize: none;
    height: 47px;
    overflow: hidden;
    outline: 0 !important;
    margin-right: 10px;
    padding: 12px;
    width: 90%
}

.verified p {
    color: #23bc4c;
    margin-bottom: 5px
}

.verified p i {
    padding-right: 4px;
    position: relative;
    top: 4px
}

.text-by p {
    margin-bottom: 0;
    margin-right: 15px;
    margin-left: 15px
}

.text-by h4 {
    font-size: 18px;
    margin-bottom: 0
}

.reservation {
    margin: 50px 0
}

.reserv-title {
    margin: 0 0 35px
}

.slide-btn {
    font-size: 16px;
    color: var(--them-secondary)
}

.reserv-detail-descrip p,
.reserv-details {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical
}

.slide-btn img {
    width: 10px
}

.prev-btn img {
    margin-right: 12px
}

.clndr-btn img,
.next-btn img {
    margin-left: 12px
}

.clndr-btn {
    margin-left: 15px;
    padding: 0
}

.reserv-detail-descrip h5 {
    font-size: 16px;
    margin-top: 20px
}

.reserv-detail-descrip ul li {
    color: var(--them-secondary);
    font-size: 14px
}

.reserv-details {
    -webkit-line-clamp: 4
}

.reserv-detail-descrip p {
    font-size: 14px;
    margin-bottom: 0;
    -webkit-line-clamp: 3
}

.reserv-detail-descrip .reserv-btn {
    align-items: center;
    justify-content: space-between;
    margin: 25px 0
}

.reserv-detail-descrip .reserv-btn .host {
    font-size: 16px;
    height: 35px;
    padding: 8px 15px;
    width: 100%;
    margin-bottom: 10px;
}

.reserv-check {
    background: #f8f8f8;
    padding: 15px;
    margin: 0 0 15px;
    position: relative
}

.rs-title {
    display: flex;
    align-items: center;
    padding: 5px 0
}

.list-rate,
.notification-title {
    align-items: center;
    display: flex
}

.rs-content {
    margin: 0 10px 0 5px;
    font-size: 16px;
    color: #575757
}

.rs-ls-img img {
    width: 100%;
    height: 65px;
    object-fit: cover
}

/* .ls-item:first-child {
  border-top: none
} */

.list-rate {
    padding: 0;
    margin: 3px 0 0 !important;
    line-height: inherit;
    position: relative;
    font-size: 15px
}

.list-rate img {
    margin-right: 4px
}

.reservation .rs-img img {
    width: 35px;
    height: 35px
}

.reservation .list-descrip {
    font-size: 16px;
    margin: 0
}

.resrv-chosefile input {
    outline: 0;
    padding: 12px 20px;
    height: unset
}

.reservation input::file-selector-button {
    border-radius: 15px;
    font-size: 14px;
    outline: 0
}

.reservation-left {
    position: relative;
    overflow: hidden;
    height: 100%
}

.reservation-calend {

    width: 100%;
    background: #fff;
    height: 100%;
    right: -100%;
    z-index: 99
}

.fc .fc-button-primary {
    background-color: #f4f4f4 !important;
    border: 1px solid #f4f4f4 !important;
    color: #000 !important
}

.reservation-calend .fc .fc-col-header-cell-cushion {
    color: #000;
    font-weight: 500;
    font-size: 14px;
}

.fc .fc-button-primary:hover,
.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
    background-color: var(--theme-primary) !important;
    border: 1px solid var(--theme-primary) !important;
    color: #fff !important
}

.fc .fc-button-primary:focus,
.fc .fc-button-primary:not(:disabled).fc-button-active:focus,
.fc .fc-button-primary:not(:disabled):active:focus {
    box-shadow: none;
    background-color: var(--theme-primary) !important;
    border: 1px solid var(--theme-primary) !important;
    color: #fff !important
}

.reservation-calend .fc.fc-theme-standard .fc-view-harness .fc-event.fc-daygrid-block-event {
    background: #f5c33e;
    border: 1px solid #f5c33e !important
}

.reservation-calend .fc-h-event {
    background: #f5c33e;
    border: 1px solid #f5c33e !important
}

.reservation-calend .fc.fc-theme-standard .fc-toolbar .fc-button:focus {
    box-shadow: none !important;
}

.reservation-calend .fc .fc-button-primary:not(:disabled):active:focus {
    background: var(--theme-primary) !important;
    background-color: var(--theme-primary) !important;
}

.fc .fc-daygrid-event {
    border-color: #fff !important
}

.notification {
    margin: 30px 0
}

.notification-title {
    margin-bottom: 20px
}

.notification-title img {
    width: 35px;
    margin-left: 15px
}

.notification-main {
    height: calc(100vh - 370px);
    overflow-y: auto;
    padding: 10px 15px 0 0;
}

.notification-user {
    position: relative;
    background: #fff;
    margin-bottom: 15px;
    border-bottom: 1px solid #E2E2E2;
    padding-bottom: 15px;
}

.nt-userdetail {
    display: flex;
    align-items: center;
}

.nt-userdetail img {
    width: 40px;
    margin-right: 10px;
}

.nt-recive-content {
    margin-left: 20px;
    flex: 1;
}

.nt-recive-content p {
    font-size: 16px;
    color: #575757;
    font-weight: 500;
}

.nt-date p {
    color: var(--theme-primary);
    font-size: 14px;
    box-shadow: rgba(149, 157, 165, 0.1) 0px 6px 24px;
    padding: 5px 12px;
    border-radius: 10px;
}

/* .latest-ntf {
  position: absolute;
  width: 6px;
  height: 100%;
  background: #f5c33e;
  left: 0;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px
} */

.card-num,
.cn-down i,
.dummy {
    position: relative
}

/* .notification-user:hover {
  transform: scale(1.02)
} */

.notification-userinner {
    display: flex;
    align-items: center;
    margin-right: 15px;
    flex: 0 0 75%;
}

.cn-down,
.review-content {
    justify-content: space-between
}

.map-view-full-location {
    width: 100%;
    height: 100%
}

.map-view-location {
    width: 100% !important;
    height: 170px !important
}

.dummy {
    padding: 15px 0
}



.review-main {
    padding: 35px 0
}

.review-user img {
    width: 50px;
    height: 50px;
    object-fit: cover;
}

.review-content {
    display: flex;
    padding: 0 8px 0 0
}

.rv-content {
    flex: 1;
    margin-right: 10px
}

.gm-style .gm-style-iw-c {
    padding: 10px !important
}

.gm-ui-hover-effect {
    top: 16px !important;
    right: 15px !important;
    width: 19px !important;
    height: 19px !important;
    border: 1px solid !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: rgb(0 0 0 / 35%) 0 0 10px !important;
    background-color: #fff !important
}

.gm-ui-hover-effect span {
    margin: 0 !important;
    width: 12px !important;
    height: 12px !important
}

.map-property-img {
    width: 100%;
    height: 200px;
    border-radius: 10px
}

.map-property-name .location-title h5 {
    font-size: 15px;
    margin-bottom: 0;
    padding-top: 10px
}

.reserve-calendar .daterangepicker .drp-calendar.left {
    clear: left;
    margin-right: 30px !important
}

.reserve-calendar .daterangepicker {
    position: inherit !important;
    display: flex !important;
    padding: 20px 14px !important
}

.no-booking img {
    width: 300px;
}

.no-booking {
    padding: 120px 0
}

.no-booking h5 {
    color: #b3b3b3
}

/* .floated-img img,
.floated-img-2 img {
    filter: drop-shadow(6px 8px 5px #00000026)
} */

.pac-container {
    box-shadow: none !important;
    border-top: none !important;
    margin-top: 3px;
    height: auto;
    overflow-y: auto;
    padding-top: 10px;
    border-radius: 10px;
    width: 430px !important;
    transform: translate(-33px, 30px);
    padding: 40px;
    box-shadow: 1px 4px 10px 0 #00000059;
}

.pac-icon {
    width: 38px;
    height: 38px;
    margin-right: 7px;
    margin-top: 6px;
    display: inline-block;
    vertical-align: top;
    background-image: url(https://maps.gstatic.com/mapfiles/api-3/images/autocomplete-icons.png);
    -webkit-background-size: 34px 34px;
    background-size: 34px;
}

.pac-item {
    padding: 8px 0px;
    border-top: 0;
    border-bottom: 1px solid #cdcdcd80
}

.accordion-btn,
.accordion-btn:focus {
    border-bottom: 1px solid var(--grey-two) !important
}

.pac-item-query {
    display: block;
}

.pac-item span:nth-child(3) {

    margin-bottom: 7px;
    line-height: 1
}

.pac-icon {
    background-image: url("../images/lc-pin.png") !important;
    background-size: contain !important;
    background-position: center top !important;
    background-repeat: no-repeat;
    padding-right: 11px !important;
    float: left;
}

.wish-icons {
    justify-content: end;
    height: 100%;
    align-items: flex-start;
    position: relative;
    max-width: max-content;
    margin-left: auto;
}

.wish-icons .fav-in {
    position: inherit
}

.accordion-btn {
    background-color: transparent !important;
    color: #000 !important;
    padding: 20px 0;
    border-radius: 0 !important;
    font-size: 20px
}

.accordion-btn:focus {
    box-shadow: none
}

.accordion-btn:not(.collapsed)::after {
    background-image: var(--bs-accordion-btn-icon) !important
}

.cust-acrdn-body {
    padding: 10px 0 !important
}

.media-photo-badge img {
    width: 70%;
    border-radius: 105px;
    border: 1px solid #ddd6d6;
    object-fit: cover;
}

.cn-down {
    background: #f3f3f3;
    padding: 7px 15px;
    border-radius: 10px;
    display: flex;
    margin-bottom: 15px;
    margin-top: 15px
}

.accp-btn {
    display: flex;
    justify-content: space-between;
    margin-top: 20px
}

.accp-btn button {
    width: 49%;
    margin-bottom: 8px
}

.card {
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important
}

.cn-down i {
    font-size: 15px;
    top: 2px
}

.thead-dark {
    background-color: #f7f7f7
}

.bd-none {
    border: none !important
}

.side-inner li a i {
    font-size: 22px;
    position: relative;
    top: 4px;
    margin-right: 6px;
}

.policy-head {
    font-size: 26px
}

.policy-content {
    font-size: 17px
}

.listing-checkbox-wrapper.fsrch {
    width: 85px;
    height: 75px;
    margin: 0 10px 0 0
}

.listing-checkbox-wrapper {
    text-align: center;
    width: 31.33%;
    height: 80px;
    justify-content: center;
    align-items: center;
    transition: .3s;
    cursor: pointer;
    margin: 5px 5px 8px;
}

.listing-checkbox-wrapper .property_types_image .listing-checkbox-tile {
    box-shadow: none !important;
    border-color: transparent
}

.listing-checkbox-wrapper .listing-checkbox-input:checked+.listing-checkbox-tile {
    border-color: var(--them-secondary)
}

.listing-checkbox-wrapper .listing-checkbox-icon {
    margin-bottom: 0 !important
}

.payment-detail {
    padding: 65px 0
}

.payment-detail .payment-title {
    margin: 0 0 30px 110px;
}

.card-detail {
    margin-bottom: 60px
}

.card-detail p {
    color: #8A8A8A;
    font-size: 18px;
}

.card-content {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 3px;
}

.card-num {
    flex: 0 0 80%;
    max-width: 80%;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.rated-sc,
.t-pa,
img.verified {
    position: absolute
}

.card-num .usercard-defaultbtn {
    text-transform: capitalize;
    border-radius: 5px;
    padding: 5px 7px;
    font-size: 13px;
    font-weight: 600;
    color: #575757;
    box-shadow: rgb(0 0 0 / 5%) 0 0 0 1px, rgb(209 213 219) 0 0 0 1px inset;
    margin-right: 3px;
}

.usercard-cancelbtn {
    padding: 5px;
    border-radius: 10px;
    transition: .3s ease-in-out;
}

.usercard-cancelbtn:hover {
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.usercard-cancelbtn img {
    width: 22px;
}

.modal-icon {
    width: 80px !important;
    font-size: 65px;
    color: #f5c33e
}

.content-color {
    color: #676363
}

.t-pa {
    top: 0;
    right: 0
}

.card-bg {
    background: #fff;
    padding: 30px 45px;
    border-radius: 10px;
    box-shadow: 2.5px 3.5px 15px rgba(149, 157, 165, 0.2);
}

.wallet span,
h6.transaction-detail {
    color: #575757
}

.total-credit-detail {
    width: 65%;
    margin: 0 auto
}

.total-list {
    margin: 0;
    min-height: min-content;
    max-height: 150px;
    overflow: auto;
}

.total-list li {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    padding: 0 25px 0 10px;
}

.total-amount {
    display: inline-block;
    margin-left: 10px
}

.transaction-head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px
}

.transaction-head button {
    color: #575757
}

.transaction-head h6 {
    margin-bottom: 0;
    color: #575757
}

.add-card-main {
    padding: 20px 0 0;
}

.add-card-btn {
    display: block
}

.add-card-btn button {
    padding: 0 50px
}

.profile-user {
    width: 40px;
    height: 40px;
    object-fit: fill;
    border-radius: 50%
}

.modal-title {
    position: relative;
    top: 12px
}

.book-img img {
    width: 40%
}

.nav-header {
    background: transparent;
}

@keyframes sticky {
    0% {
        transform: translateY(-50px)
    }

    100% {
        transform: translateY(0)
    }
}

.hv-74 {
    height: 74vh
}

section.list {
    /* height: calc(100vh - 227px) */
}

.n-msg {
    display: flex;
    align-items: center;
    height: 73vh;
    justify-content: center
}

.n-msg img {
    width: 250px;
    margin-bottom: 20px
}

.popularcity {
    padding: 3px;
    border: 1px solid transparent;
    border-radius: 11px;
    cursor: pointer
}

.location-act {
    border-color: #bdbaba
}

header .popup h3 {
    padding-left: 25px
}

header .popup .main {
    justify-content: center
}

.no-gst {
    display: flex !important;
    align-items: center;
    justify-content: center
}

.guest-counter input {
    pointer-events: stroke;
    appearance: none
}

.account-userimg {
    width: max-content;
    margin: 0 auto 15px
}

img.verified {
    width: 30px;
    height: 30px;
    bottom: -1px;
    right: 5px
}


header .search-reservation li:nth-child(2) {
    padding: 0 !important
}

span.rd-more {
    color: #f5c33e;
    font-size: 13px;
    font-weight: 500
}

.rs-title .mini-profile {
    justify-content: flex-end !important
}

.rated-sc {
    top: 0;
    right: 0;
    width: auto
}

.loading-image,
div#loader {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #ffff;
    text-align: center
}

#loader img {
    object-fit: cover;
}

.loading-image {
    border: 1px solid #dfdfdf;
    border-radius: 10px
}

.loading-image img {
    position: relative;
    top: 38%
}

.main-logo a img {
    width: 150px;
    padding: 20px 0
}

.message-inner {
    box-shadow: rgb(67 71 85 / 27%) 0 0 .25em, rgb(90 125 188 / 5%) 0 .25em 1em;
    border-radius: 15px;
    margin: 50px 20px;
    text-align: center;
    padding: 17px 15px 22px
}

.message-inner img {
    width: 65px
}

.message-inner-btn {
    height: 40px;
    padding: 0 20px;
    font-size: 14px;
    border: none;
    color: #fff;
    font-weight: 500;
    border-radius: 8px;
    width: 90%
}

.card-logo img {
    width: 55px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    padding: 18px 8px;
    border-radius: 8px;
}

a.dt-button,
button.dt-button,
div.dt-button {
    background: #f5c33e !important;
    border: 1px solid #f5c33e !important;
    color: #ffff !important;
    font-weight: 500;
    border-radius: 40px !important;
    margin-bottom: 30px !important
}

.tr-tb {
    border: 1px solid #e3dddd;
    border-radius: 10px;
    padding: 30px
}

.dataTables_wrapper .dataTables_filter input {
    margin-left: .5em;
    border: 1px solid #d9d9d9;
    border-radius: 50px;
    height: 34px;
    width: 210px;
    outline: 0 !important;
    padding: 10px
}

table.dataTable thead td,
table.dataTable thead th {
    border-bottom: 1px solid #e3dddd !important;
    font-weight: 500 !important
}

table.dataTable.no-footer {
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #f5c33e !important;
    color: #fff !important;
    font-weight: 500 !important;
    border-radius: 34px !important;
    border: 1px solid #f5c33e !important
}

div.dt-buttons {
    position: relative;
    float: right !important
}

.dataTables_wrapper .dataTables_filter {
    float: left !important;
    text-align: right
}

table.dataTable.display tbody tr.odd>.sorting_1,
table.dataTable.order-column.stripe tbody tr.odd>.sorting_1 {
    background-color: unset !important
}

table.dataTable.display tbody tr.even>.sorting_1,
table.dataTable.order-column.stripe tbody tr.even>.sorting_1 {
    background-color: inherit !important
}

.popup-inner-image.re-img {
    width: 96%
}

.carddefault-bgtheme {
    border-radius: 4px;
    padding: 0px 6px;
    font-size: 13px;
    font-weight: 500;
    color: #fff;
    background-color: var(--theme-primary);
    margin-right: 1px;
}

div#loader {
    border: 1px solid #f3f3f3;
    border-radius: 0
}

.popup.guest-popup button.mt-3.submit-header {
    width: 100%;
    border-radius: 8px;
    font-size: 20px;
}

.mychat {
    background-color: transparent;
}

#ui-datepicker-div {
    display: none !important;
}

a {
    cursor: pointer;
}

.slider.slider-horizontal {
    display: flex !important;
}

.blk-logo {
    display: none;
}

span.text-err {
    text-align: center;
    display: block;
    color: red;
    font-size: 12px;
}

.cl-on {
    display: flex;
    align-items: center;
    font-weight: 500;
    justify-content: space-between;
    width: 100%;
    margin: 0px auto;
    padding: 1px 30px;
}

.cl-on h6 {
    margin: 0;
    text-transform: capitalize;
}

.listing-form {
    padding: 0px 25px 20px;
}

.cl-on i {
    font-size: 25px;
}

.sh-1 {
    width: 100%;
}

.sub-header {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 100;
    background: transparent !important;
}

.mychat .chat-lst .chat-img img {
    border: 1px solid #909090
}

.mychat .chat-lst .chat-name p {
    color: #000
}

.mychat .chat-lst {
    background: #f8f8f8
}

a.dt-button,
button.dt-button,
div.dt-button {
    background: #f5c33e !important;
    border: 1px solid #f5c33e !important;
    color: #ffff !important;
    font-weight: 500;
    border-radius: 40px !important;
    margin-bottom: 30px !important
}

.tr-tb {
    border: 1px solid #e3dddd;
    border-radius: 10px;
    padding: 30px
}

.dataTables_wrapper .dataTables_filter input {
    margin-left: .5em;
    border: 1px solid #d9d9d9;
    border-radius: 50px;
    height: 34px;
    width: 210px;
    outline: 0 !important;
    padding: 10px
}

table.dataTable thead td,
table.dataTable thead th {
    border-bottom: 1px solid #e3dddd !important;
    font-weight: 500 !important
}

table.dataTable.no-footer {
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #f5c33e !important;
    color: #fff !important;
    font-weight: 500 !important;
    border-radius: 34px !important;
    border: 1px solid #f5c33e !important
}

div.dt-buttons {
    position: relative;
    float: right !important
}

.dataTables_wrapper .dataTables_filter {
    float: left !important;
    text-align: right
}

table.dataTable.display tbody tr.odd>.sorting_1,
table.dataTable.order-column.stripe tbody tr.odd>.sorting_1 {
    background-color: unset !important
}

table.dataTable.display tbody tr.even>.sorting_1,
table.dataTable.order-column.stripe tbody tr.even>.sorting_1 {
    background-color: inherit !important
}

.popup-inner-image.re-img {
    width: 96%
}

div#loader {
    border: 1px solid #f3f3f3;
    border-radius: 0
}

.popup.guest-popup button.mt-3.submit-header {
    width: 100%;
    border-radius: 8px;
    font-size: 20px;
}

.mychat {
    background-color: transparent;
}

#ui-datepicker-div {
    display: none !important;
}

a {
    cursor: pointer;
}

.slider.slider-horizontal {
    display: flex !important;
}

.blk-logo {
    display: none;
}

span.text-err {
    text-align: center;
    display: block;
    color: red;
    font-size: 12px;
}

.cl-on {
    display: flex;
    align-items: center;
    font-weight: 500;
    justify-content: space-between;
    width: 100%;
    margin: 0px auto;
    padding: 1px 30px;
}

.cl-on h6 {
    margin: 0;
    text-transform: capitalize;
}

.listing-form {
    padding: 0px 25px 20px;
}

.cl-on i {
    font-size: 25px;
}

.sh-1 {
    width: 100%;
}

button.btn-close.cstm-close {
    border: none;
    padding: 23px;
    font-size: 11px;
}

.pop-not {
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 99;
    left: 0;
    text-transform: capitalize;
    text-align: center;
}

.home-cont,
.listing-checkbox-main {
    position: relative;
}

span.text-required {
    color: gray;
    font-size: 11px;
    margin-top: 20px;
}

.chat-cancel {
    display: none;
}

.chat-cancel i {
    padding-top: 4px;
    font-size: 18px;
}

.i-property-name {
    visibility: visible;
    opacity: 1;
}

.gr-btn button {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}

.gr-btn button img {
    width: 11px;
    margin-right: 10px;
}

.gr-inner {
    display: flex;
    align-items: center;
    margin: 5px 0;
}

.gr-inner img {
    width: 11px;
    margin-right: 5px;
}

.realt-chat {
    margin-bottom: 30px;
}

.realt-chat:last-child {
    margin-bottom: 0;
}

.inbox-property {
    position: sticky;
    top: 0;
    background: #fff;
    padding-top: 5px;
    margin-bottom: 15px;
}

.inbox-inner-property {
    display: flex;
    align-items: center;
    /* box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px; */
    border-radius: 5px;
    padding: 10px;
    /* box-shadow: rgba(0, 0, 0, 0.45) 0px 12px 20px -18px; */
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.inbox-property-img {
    flex: 0 0 12%;
    margin-right: 10px;
    height: 60px;
}

.inbox-property-img img {
    width: 100%;
    border-radius: 8px;
    height: 100%;
    object-fit: cover;
}

.prof-in {
    position: relative;
}

.prof-in::before {
    display: none;
}

.inbox-property-content a {
    font-size: 16px;
    text-transform: capitalize;
    color: #000;
    font-weight: 500;
}

.inbox-property-content p {
    font-size: 15px;
    line-height: 1.3;
}

.inbox-property-date {
    line-height: 1.3;
}

.inbox-property-date span {
    font-size: 14px;
    color: var(--theme-middle);
}

.cc-display {
    font-size: 14px;
    margin-top: 4px;
    display: block;
    font-weight: 500;
}

.send-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.pending-tm {
    display: flex;
    align-items: center;
}

.bk-calender-head {
    display: flex;
}

@-moz-document url-prefix() {

    /* .daterangepicker td.active, .daterangepicker td.active:hover{
    padding: 0 3px !important;
  } */
    input[type="number"] {
        -moz-appearance: textfield;
    }
}

.gm-style img {
    object-fit: cover;
}

.tab-pane {
    margin-bottom: 35px;
}

.edit-sc-tb .nav-pills {
    height: auto;
}

.edit-sc-tb .nav-link {
    height: auto !important;
    padding: 10px 30px !important;
}

.c-policy-btn {
    background-color: #d9d9d9;
}

.edit-sc-tb .tabs-sc {
    display: flex;
}

.edit-sc-tb .nav-pills {
    flex-flow: column;
    margin-right: 20px;
}

.edit-sc-tb .tab-content {
    flex: 1;
}

.edit-sc-tb .nav-pills .nav-item .nav-link {
    width: 100%;
}

.editlisting-map {
    height: 450px;
}

.edit-place-name {
    font-size: 18px;
    font-weight: 500;
    color: #000;
}

.aminities-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 10px !important;
}

.carrent-loc-btn {
    display: flex;
    margin-top: 15px;
    border: 1px solid #E2E2E2;
    width: 100%;
    padding: 14px;
    border-radius: 7px;
    text-transform: capitalize;
    justify-content: center;
    font-size: 16px;
    align-items: center;
    transition: .3s ease-in-out;
}

.carrent-loc-btn i {
    margin-right: 5px;
    color: #000;
    transition: .3s ease-in-out;
}

.theme-btn-hover:hover {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
    color: #fff;
}

.theme-btn-hover:hover i {
    color: #fff;
}

/* promo section */

.promo-disc {
    height: 120px;
    resize: none;
}

.promo-box {
    border: 1px solid #e2e2e2;
    border-radius: 10px;
    padding: 40px;
    margin-bottom: 60px;
}

.input.copied.mb-3 {
    position: relative;
}

button.btn.btn-copy {
    position: absolute;
    right: 15px;
    top: 5px;
    font-size: 22px;
    color: gray;
    padding: 0;
    border: none;
}

.copied input {
    font-size: 20px;
    font-weight: 500;
    color: grey;
}

.promo-bx {
    text-align: center;
    border: 1px solid #e2e2e2;
    padding: 20px;
    border-radius: 15px;
    transform: translate(0px, 0px);
    transition: 0.3s ease;
}

.promo-bx p {
    font-size: 12px;
}


.promo-edit a {
    color: grey;
    font-size: 19px;
    padding: 6px 14px;
}

.promo-edit a:hover {
    color: black;
}

.promo-bx:hover {
    opacity: 1;
}

.promo-bx:hover {
    cursor: pointer;
    box-shadow: 1px 5px 5px 0px #0000001f;
    transform: translate(0px, -5px);
    transition: 0.3s ease;
}


.btn-copy .cp-in {
    display: block;
}

.btn-copy.clicked .cp-in {

    display: none;
}

.btn-copy.clicked .clk-in {
    color: #f5c33e;
    display: block;
}

.clk-in {
    display: none;
}

.promo-bx h1 {
    color: #f5c33e;
    font-weight: 700;
    padding-top: 5px;
}

textarea.form-control.accpt-ta {
    height: 110px;
    resize: none;
    border-radius: 10px;
    margin-top: 16px;
    margin-bottom: 20px;
}

textarea.form-control {
    height: 100px;
    border-radius: 10px;
    padding: 10px;
}

.wish-icons.d-flex .fav-in {
    z-index: 9;
}

input.dt-rng {
    cursor: pointer;
}

.daterangepicker select.monthselect {
    margin-right: 2%;
    width: 60%;
    border: 1px solid #DDDDDD;
    border-radius: 25px;
    padding: 5px 10px;
}

.daterangepicker select.yearselect {
    border: 1px solid #DDDDDD;
    border-radius: 25px;
    padding: 5px 10px;
}

.daterangepicker .drp-calendar.left {
    padding: 8px 0 8px 0px;
}

.daterangepicker .drp-calendar.right {
    margin-left: 0px;
    margin-right: 0px !important;
}

.dropzone {
    min-height: 150px;
    border: 1px solid #DBD9D9;
    background: #fff;
    padding: 20px 20px;
    border-radius: 10px;
}

.text-danger.fr-md {
    font-size: 12px;
    text-align: right;
}

.white-list-overlay {
    z-index: 99;
}

.swal-title {
    text-transform: uppercase;
}

.swal-button {
    background: #f5c33e !important;
    box-shadow: none !important;
}

a.menubtn.header-btn {
    color: white;
    font-size: 18px;
    font-weight: 400;
}

.heade-date input {
    height: 25px;
    cursor: pointer;
}

p.product-rate img {
    margin: 0px 5px;
}

.review-content .ls-img img {
    height: 100px;
    width: 100px;
}

button:disabled,
button[disabled] {
    opacity: 0.5;
    cursor: not-allowed;

}

a[disabled] {
    opacity: .7;
    cursor: not-allowed;
}

.amt-er {
    position: relative;
    bottom: 40px;
}

/*
button.submit-header {
  position: relative;
  top: 2px;
} */


.dis-guest {
    opacity: 0.4;
    cursor: not-allowed !important;
    pointer-events: none;
}



.cover img {
    width: 100%;
    border: 1px solid #e6e6e6;
    border-radius: 10px;
    overflow: hidden;
    height: 300px;
    object-fit: cover;
}

.pd-2 {
    padding: 2rem;
}


/* .upload__box {
  padding: 40px;
} */
.upload__inputfile {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
}



/* .upload__btn:hover {
  background-color: unset;
  color: #4045ba;
  transition: all 0.3s ease;
} */
.upload__btn-box {
    margin-bottom: 10px;
}

.upload__img-wrap {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.upload__img-box {
    margin-bottom: 15px;
}

.upload__img-close {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #fff;
    position: absolute;
    top: 10px;
    right: 10px;
    text-align: center;
    z-index: 1;
    cursor: pointer;
    color: gray;
    border: 1px solid gray;
    box-shadow: -2px 2px 5px 3px #00000036;
    display: flex;
    align-items: center;
    justify-content: center;
}


.img-bg {
    background-repeat: no-repeat;
    background-position: top center;
    background-size: cover;
    position: relative;
}

.img-bg {
    height: 110px;
    width: 100%;
    object-fit: cover;
    border-radius: 5px;
    overflow: hidden;
}

.setcover .img-bg img {
    object-fit: cover;
    height: 200px;
    width: 100%;
}

.setcover .img-bg {
    height: 200px;
}

.upload__btn p {
    color: #fff;
    /* font-weight: 500; */
    margin: 0;
    font-size: 14px;
}

.st-lst {
    display: flex;
    align-items: center;
    position: relative;
    top: 3px;
}

span.chkicon {
    padding-right: 9px;
    font-size: 17px;
}

.amenities-tb .total-guest {
    width: 100%;
}


.btn:hover,
.btn-check:focus+.btn,
.btn:focus {
    background-color: #f5c33e;
    border-color: #f5c33e;
    outline: none !important;
    box-shadow: none !important;
}

.btn-mini {
    height: 40px;
    padding: 4px 20px;
}


.image-check img {
    width: 100%;
    height: 130px;
    border-radius: 6px;
    object-fit: cover;
}

.image-check label {
    border: 1px solid #e5e5e5;
    border-radius: 10px;
    width: 100%;
    position: relative;
}

.image-check input {
    position: absolute;
    z-index: 1;
    left: 10px;
    top: 7px;
}

.image-check:hover {
    border-color: #0000;
}

.form-check-input:checked {
    background-color: inherit;
    border-color: inherit;
}

.select-photos {
    padding: 20px;
}

.listed {
    background: #f4f4f4;
    padding: 5px 12px;
    border-radius: 10px;
    display: flex;
    width: auto;
    font-weight: 500;
    margin-top: 3px;
    margin-right: 10px;
    text-align: center;
    justify-content: center;
}

.status-list {
    margin-right: 5px;
}

.fc-listed {
    color: green;
}

.fc-unlisted {
    color: #ef0c0c;
}

.align-item-center {
    align-items: center;
}

.listing-action {
    display: flex;
    justify-content: end;
}

.tabs-sc .nav-pills .nav-link {
    display: flex;
    align-items: center;
}

.tabs-sc .nav-pills .nav-link i {
    margin-right: 5px;
}

.text-left {
    text-align: left !important;
}


.am-icon {
    width: 30px;
    height: 30px;
    padding: 2px;
    margin-right: 8px;
}

p.suitable {
    width: 231px;
}

.trans-box {
    border: 1px solid #d9d9d9;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px;
}

.trans-bx-icon i {
    font-size: 40px;
    color: #f5c33e;
}

.trans-bx-icon {
    margin-right: 5px;
}

.tab-pane .card.pd-2 button.cust-btn.theme-btn {
    /* width: 200px; */
    margin-top: 20px;
}

.tb-btn-sc {
    text-align: center;
}

.alert.alert-danger ul {
    margin: 0;
}

.pin {
    /* -webkit-animation-name: pin; */
    -webkit-animation: pin 1s ease-out 0.6s infinite alternate-reverse both;
    animation: pin 1s ease-out 0.6s infinite alternate-reverse both;
    width: 30px;
    height: 30px;
    border-radius: 50% 50% 50% 0;
    background: #f5c33e;
    position: absolute;
    transform: rotate(-45deg);
    left: 50%;
    top: 50%;
    margin: -20px 0 0 -20px;
}

.pin:after {
    content: '';
    width: 14px;
    height: 14px;
    margin: 0px 0 0 0px;
    background: #fff;
    position: absolute;
    border-radius: 50%;
    top: 8px;
    left: 8px;
}

.pulse {
    background: #b3b3b3;
    border-radius: 50%;
    height: 14px;
    width: 14px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: 11px 0px 0px -12px;
    transform: rotateX(55deg);
    z-index: 1;
}

.pulse:after {
    content: "";
    border-radius: 50%;
    height: 40px;
    width: 40px;
    position: absolute;
    margin: 0;
    -webkit-animation: pulsate 1.3s ease-out;
    -webkit-animation-iteration-count: infinite;
    opacity: 0;
    box-shadow: 0 0 1px 2px #b3b3b3;
    top: -12px;
    right: -13px;
}

@-webkit-keyframes pulsate {
    0% {
        -webkit-transform: scale(0.1, 0.1);
        transform: scale(0.1, 0.1);
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        -webkit-transform: scale(1.2, 1.2);
        transform: scale(1.2, 1.2);
    }
}

@-webkit-keyframes bounce {
    0% {
        -webkit-transform: translateY(-2000px) rotate(-45deg);

    }

    60% {
        -webkit-transform: translateY(30px) rotate(-45deg);
    }

    80% {
        -webkit-transform: translateY(-10px) rotate(-45deg);
    }

    100% {
        -webkit-transform: translateY(0) rotate(-45deg);
    }
}

@-webkit-keyframes pin {
    100% {
        top: 45%;
        bottom: 60px;
    }
}

.mp-loader {
    position: relative;
}

.mp-loader {
    background: #f5f5f5;
    width: 100%;
    height: 100%;
}

.er-mg .invalid-feedback {
    display: inherit;
}

.loadergif.btn-ld {
    border: 1px solid #f5c33e;
    height: 47px;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.iqamaloader.btn-ld {
    border: 1px solid #f5c33e;
    height: 47px;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
    justify-content: center;
    display: flex;
}

.loadergif.btn-ld img {
    height: 50px;
    position: relative;
    bottom: 3px;
}

.gm-style-iw-d {
    width: 250px;
}


.map-property-name b {
    color: #000;
    font-weight: 600;
}

.map-property-name .title h5 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px !important;
    color: #000;
}

.map-property-name {
    padding-top: 9px;
}

.product-content-map {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 10px;
}



.invalid-phone {
    border-color: #dc3545;
}

.mg-20 {
    margin: 20px 0;
    margin-bottom: 20px !important;
}



span.caut-icon {
    color: #f5c33e;
    font-size: 28px;
    padding-right: 10px;
}

.caution {
    display: flex;
    text-align: left !important;
    font-size: 12px;
    color: gray;
    font-style: italic;
    border-left: 2px solid #f5c33e;
    padding-left: 10px;
}

.caution p {
    margin: 0;
    line-height: 14px;
    align-items: center;
    padding-top: 3px;
}

span.caut-marker {
    position: relative;
    top: 5px;
}


.show-tick.cstm-slt-pick button {
    width: 100%;
    display: block;
    padding: 10px 25px;
    font-size: 14px;
    color: var(--theme-middle);
    background-color: #fff;
    border: 1px solid var(--grey-one);
    border-radius: 25px;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 44px;
}

.show-tick.cstm-slt-pick {
    width: 100% !important;
    display: block;
}


.bootstrap-select .bs-ok-default:after {
    border-width: 0 0.18em 0.18em 0 !important;
}

button.btn.btn-copy:hover {
    background: transparent;
    color: #f5c33e;
}






/* coupan code modal */
.coupon_acc {
    border: none !important;
    padding: 0px;
    font-size: 17px;
    border-bottom: none !important;
    box-shadow: none !important;
}

.coupon_item {
    border: 1px solid gray;
}

.accordion-item.coupon_item {
    padding: 15px 15px;
    border-radius: 10px;
    border: 1px solid var(--grey-one) !important;
}

.accordion-item.coupon_item .accordion-header {
    box-shadow: none !important;
}

.coupon-inp {
    margin-top: 10px;
}

.coupon-inp {
    display: none;
}

.coupon-check {
    justify-content: space-between;
    padding-left: 0;
    font-size: 18px;
    font-weight: 400;
}

.coupon-checkbox {
    padding: 12px 10px;
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    margin-bottom: 15px;
}

.cust-form-check-input {
    cursor: pointer;
}

.gr-code {
    position: relative;
}

button.gr-promo-btn {
    position: absolute;
    top: 1px;
    right: 1px;
    background: #e2e2e2;
    border: 1px solid #e2e2e2;
    width: 50px;
    height: 42px;
    border-radius: 0px 25px 25px 0px;
    color: black;
    font-size: 20px;
}

.gr-promo-btn:hover i {
    transition: 0.9s;
    -webkit-transform: rotateZ(720deg) !important;
    -moz-transform: rotateZ(720deg) !important;
    transform: rotateZ(720deg) !important;
}

.multi-slt {
    position: relative;
}

.multi-slt i {
    position: absolute;
    right: 13px;
    top: 1px;
    font-size: 30px;
}

.show-tick.cstm-slt-pick button {
    padding-right: 44px;
}

button.pd-promo-btn {
    position: absolute;
    top: 1px;
    right: 1px;
    background: #f5c33e;
    border: 1px solid #f5c33e;
    width: 103px;
    height: 42px;
    border-radius: 0px 15px 15px 0px;
    color: white;
    font-weight: 500;
    font-size: 14px;
}


.disabled-btn {
    pointer-events: none;
    opacity: .65;
    cursor: none !important;
}

.receipt-card {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.no-promo-icon img {
    width: 200px;
    opacity: 0.3;
}

.promo-not-avail {
    height: 55vh;
    padding-top: 55px;
}

.no-promo-icon {
    margin-bottom: 15px;
}


.promo-not-avail h3 {
    color: #b2b2b2;
}



.gr-code input {
    text-transform: uppercase;
}


.reserv-pagination .page-item:first-child .page-link {
    border: none;
    width: max-content;
}

.reserv-pagination .page-item:last-child .page-link {
    border: none;
    width: max-content;
}

.reserv-pagination.pagination .page-item:first-child .page-link:hover,
.reserv-pagination .page-item:last-child .page-link:hover {
    color: #000 !important;
}

.sure {
    font-size: 66px;
    color: #f45e5e;
    line-height: 1.2;
}

.delete-pr {
    display: inline-block;
    width: 100%;
}

.delete-pr a {
    display: inline-block;
}

.promo-bx {
    position: relative;
    overflow: hidden;
}

.expired-promo {
    position: absolute;
    left: -30px;
    top: 14px;
    background: #f8d7da;
    padding: 6px 10px;
    transform: rotate(315deg);
    font-size: 11px;
    text-transform: capitalize;
    border-radius: 0;
    color: #842029;
    font-weight: 500;
    letter-spacing: 1px;
    width: 45%;
}

.drp-calendar.left.single {
    margin-right: 0 !important;
}

.auto-height {
    min-height: 58vh;
    height: auto;
}


@media print {
    @page {
        size: auto;
        margin-top: 0;
        margin-bottom: 0;
        width: 100%;
    }

    .print-div {
        display: none;
    }

    body {
        margin: 0;
        padding-top: 1cm;
        /* adjust as needed */
        padding-bottom: 1cm;
        /* adjust as needed */
    }

    header,
    footer,
    aside,
    nav,
    form,
    iframe,
    .menu,
    .hero,
    .adslot {
        display: none;
    }

    /* Hide header and footer */
    .print-header,
    .print-footer {
        display: none;
    }
}


.after-end.is-invalid .phone-er {
    display: block !important;
    width: 100%;
    margin-top: 0.25rem;
    font-size: .875em;
    color: #dc3545;
}

.after-end.is-invalid .phone-number.invalid-phone .invalid-feedback {
    display: none !important;
}

.start-date.active {
    background: black !important;
}


button.promo-drop-btn {
    background: transparent;
    border: 1px solid #ffffff;
    position: absolute;
    right: -12px;
    height: 25px;
    width: 25px;
    border-radius: 25px;
    font-size: 16px;
    top: -8px;
}

button.promo-drop-btn:hover {
    background: #e9e9e9;
}

.promo-dropdown a.dropdown-item span {
    position: relative;
    bottom: 2px;
    padding-left: 3px;
}

button.promo-drop-btn.show {
    background: #e9e9e9;
}

.discount-tag {
    top: 15px;
    position: absolute;
    background: #f5c33e;
    padding: 1px 15px;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    left: 15px;
    border-radius: 5px;
}

span.discount-cut {
    text-decoration: line-through;
    color: gray;
    font-weight: 400;
    padding-right: 3px;
}



.report-badge {
    position: fixed;
    bottom: 90px;
    right: 30px;
    right: 30px;
    display: flex;
    width: 50px;
    height: 50px;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid #ebebeb;
    box-shadow: 1px 1px 13px 3px #0000001c;
    z-index: 100;
    transition: 0.3s ease;
    background-color: #fff;
    text-align: center;
    padding: 12px;
}

.report-badge:hover {
    transform: scale(1.1);
    cursor: pointer;
}

.fs-12 {
    font-size: 12px;
}

.inner-discount {
    color: #fff;
    font-weight: 600;
    font-size: 15px;
    border: 1px solid #f5c33e;
    padding: 2px 15px;
    border-radius: 22px;
    margin-left: 10px;
    background: #f5c33e;
}

/* tickets page ui */
.ticket-form-inner {
    border: 1px solid #e2e2e2;
    border-radius: 10px;
    padding: 40px;
    margin-bottom: 60px
}

/* .ticket-table .dataTables_wrapper {
  width: 800px;
  margin: 0 auto;
} */
.ticket-table {
    border: 1px solid #e3dddd;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 60px;
}

.ticket-table .dataTables_wrapper .dataTables_filter {
    float: right !important;
    margin-bottom: 30px;
}

.ticket-table .dataTables_length {
    display: none;
}

.ticket-table ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 0;
}

.ticket-table ::-webkit-scrollbar-track {
    border-radius: 0;
    background-color: #f1f1f1;
}

.ticket-table table.dataTable,
table.dataTable td {
    font-size: 14px;
}

.ticket-table .dataTables_wrapper.no-footer .dataTables_scrollBody {
    border-bottom: transparent;
}

.ticket-table table.dataTable.row-border tbody tr:last-child td,
.ticket-table table.dataTable.display tbody tr:last-child td {
    border-bottom: 1px solid #ddd;
}

.ticket-table .ft-action-btn a {
    color: #000;
}

.ticket-table .ft-action-btn i {
    background: #d6d6d6;
    border-radius: 50%;
    padding: 8px;
    font-size: 16px;
}

.ticket-table .complient-status {
    border-radius: 5px;
    color: #fff;
    padding: 2px 5px;
    font-size: 14px;

}

.status-green {
    background-color: #005a30;
}

.status-yellow {
    background-color: #bd8f02;
}

.status-red {
    background-color: #81020e;
}


.status-app {
    background-color: #005a30;
}

.status-pen {
    background-color: #bd8f02
}

.status-rej {
    background-color: #81020e;
}

.list-content {
    display: inline-block;
}

.ticket-table .dataTables_filter label {
    font-weight: 500;
    color: #000;
}

.ticket-inbox {
    height: calc(100vh - 72px);
    overflow: hidden;
}

.ticket-detail {
    margin-bottom: 25px;
}

.ticket-detail .ticket-dhead {
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
    margin-bottom: 25px;
}

.ticket-detail .ticket-dhead .ticket-head-content {
    font-weight: 500;
    color: #000;
    font-size: 18px;
}

.ticket-dbody {
    height: 47vh;
    overflow-y: auto;
    padding-right: 15px;
}

.support-box {
    display: flex;
    position: relative;
    margin-bottom: 15px;
}

.support-detail {
    width: 100%;
    border: 1px solid var(--grey-one);
    margin-left: 15px;
    border-radius: 5px;
    overflow: hidden;
}

.support-detail h6 {
    color: #000;
    font-size: 14px;
    margin-bottom: 5px;
    background: #e2e2e2;
    padding: 10px 15px;
}

.support-detail .dt-area {
    /* border: 1px solid var(--grey-one); */
    padding: 10px;
    font-size: 14px;
    border-radius: 5px;
    width: 100%;
    margin: 0;
}

.support-detail button {
    font-size: 14px;
    font-weight: 500;
}

.support-detail .ticket-com-button {
    display: flex;
    align-items: center;
    justify-content: end;
}

.ticket-com-dropdown-main span {
    font-size: 14px;
    font-weight: 500;
    margin-right: 5px;
}

.ticket-com-dropdown {
    width: 100px;
    background-color: white;
    ;
}

.calender-.dropdowticket-com-dropdown {
    width: 230px;
}

.ticket-com-dropdown .dropdown-switch:checked+.dropdown-options-filter .dropdown-select {
    transform: scaleY(1);
}

.ticket-com-dropdown .dropdown-switch:checked+.dropdown-options-filter .dropdown-filter:after {
    transform: rotate(-135deg);
}

.ticket-com-dropdown .dropdown-options-filter {
    width: 100%;
    cursor: pointer;
}

.ticket-com-dropdown .dropdown-filter {
    position: relative;
    display: flex;
    padding: 12px 10px;
    background-color: var(--theme-primary);
    border: transparent;
    font-size: 14px;
    text-transform: uppercase;
    transition: 0.3s;
    font-weight: 600;
    margin: 0;
    color: #000;
    border-radius: 7px;
}

.ticket-com-dropdown .dropdown-filter::after {
    position: absolute;
    top: 40%;
    right: 9px;
    content: "";
    width: 8px;
    height: 8px;
    border-right: 2px solid #000;
    border-bottom: 2px solid #000;
    transform: rotate(45deg) translateX(-45%);
    transition: 0.2s ease-in-out;
}

.ticket-com-dropdown .dropdown-select {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    margin-top: 5px;
    overflow: hidden;
    box-shadow: rgb(0 0 0 / 30%) -1px 1px 4px;
    transform: scaleY(0);
    transform-origin: top;
    font-weight: 300;
    transition: 0.2s ease-in-out;
    overflow: auto;
}

.ticket-com-dropdown .dropdown-select-option {
    padding: 5px 20px;
    background-color: #fff;
    border-top: 1px solid #d6d6d6;
    transition: 0.3s;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    color: #414141;
}

.ticket-com-dropdown .dropdown-select-option:last-of-type {
    border-bottom: 0;
}

.ticket-com-dropdown .dropdown-select-option:hover {
    background-color: #f9f9f9;
}

.ticket-form-inner .note-editor .note-toolbar .note-color-all .note-dropdown-menu,
.note-popover .popover-content .note-color-all .note-dropdown-menu {
    min-width: 350px !important;
}

.ticket-form-inner .note-statusbar {
    display: none;
}

.side-smenu {
    height: 100%;
    border-right: 1px solid #bfbbbb8f;
}

.side-smenu-inner {
    padding: 0;
}

.side-smenu .side-smenu-inner li {
    margin-bottom: 25px;
}

.side-smenu .side-smenu-inner li a {
    text-decoration: none;
    color: #909090;
    font-size: 20px;
    font-weight: 400;
}

.side-smenu .side-smenu-inner li a i {
    font-size: 22px;
    position: relative;
    top: 4px;
    margin-right: 10px;
}

span.dt-time {
    float: right;
}

.support-detail:before {
    position: absolute;
    top: 11px;
    left: 47px;
    display: block;
    width: 8px;
    height: 16px;
    pointer-events: none;
    content: " ";
    clip-path: polygon(0 50%, 100% 0, 100% 100%);
    background-color: #e2e2e2;
}

.support-box:after {
    content: '';
    width: 1px;
    height: 100%;
    position: absolute;
    background: #e2e2e2;
    left: 19px;
    top: 43px;
}

.support-box:last-child:after {
    content: none;
}

.ticket-dhead {
    display: flex;
    justify-content: space-between;
}

h1.landing-head {
    font-size: 60px;
    font-weight: 500;
    text-transform: capitalize;
    color: #1c1c1c;
}

.landing-serv {
    padding-top: 70px !important;
}

.landing-float img {
    border-radius: 10px;
}

.tickets_files img {
    width: 30px;
}

.tickets_files a {
    padding-right: 5px;
    padding-left: 5px;
}

.custom-subtotal-bq {
    max-height: 230px !important;
    padding: 6px 9px !important;
    margin: 10px 0px 0 !important;
}


.search-with-icon i {
    position: absolute;
    top: 8px;
    right: 15px;
    font-size: 18px;
}

.search-with-icon input {
    padding-right: 34px;
}

.fade {
    opacity: 1;
}

.fade.show {
    -webkit-transition: opacity .15s linear;
    transition: opacity .15s linear !important;

}

a.report-pr {
    color: #2f2f2f;
    font-weight: 500;
    font-size: 20px;
    position: absolute;
    top: -4px;
    right: -124px;
    transition: 0.6s ease;
}

a.report-pr span {
    font-size: 14px;
    position: relative;
    bottom: 5px;
}

a.report-pr:hover {
    right: 3px;
    transition: 0.6s ease;
}

.ls-content.rp-hide {
    overflow: hidden;
}

.ls-content {
    position: relative;
}

section.transaction_failed {
    padding: 60px 0px;
}

.tr-f img {
    width: 67%;
}

/*New Inbox Chat*/
.nInbox {
    padding: 10px 20px 0;
    height: calc(100vh - 95px);
    overflow: hidden;
}

.nInbox .page-title {
    margin-bottom: 24px;
}

.chat-head {
    height: 55px;
    padding: 0 15px;
}

.chat-head p {
    font-size: 14px;
    color: #000;
}

.nchat-profile-list .chat-head {
    display: flex;
    align-items: center;
}

.nchat-profile-list .chat-head h5 {
    font-size: 22px;
}

.nchat-profile-list .chat-head .chat-logo {
    display: flex;
    align-items: center;
    text-transform: uppercase;
    margin-left: 7px;
}

.chat-user-profile {
    position: relative;
}

.chat-user-profile .chat-list-inner {
    align-items: flex-start;
}

.chat-user-profile .chat-list-inner .chat-img {
    width: 47px;
    height: 47px;
}

.chat-user-profile .chat-name {
    padding-right: 20px;
}

.chat-user-profile .chat-name h6 {
    font-size: 15px;
    line-height: 1.2;
}

.chat-user-profile .chat-name p {
    color: #7B8793;
    font-size: 12px !important;
}

.chat-up-status {
    border: none;
    width: max-content;
    padding: 0px 30px 0 0;
    background-position: bottom 2px right 14px;
    color: #76C00D;
    font-size: 12px;
    font-weight: 500;
    margin-top: 2px;
    background-size: 14px 10px;
}

.chat-up-status.form-select:focus {
    box-shadow: none;
    border: none;
}

.chat-up-icon {
    position: absolute;
    top: 12px;
    right: 5px;
    height: auto;
    display: flex;
    transition: .3s ease-in-out;
    border-radius: 50%;
    padding: 8px;
}

.chat-up-icon:hover {
    background-color: #f9fafc;
}

.chat-search-bar {
    position: relative;
    margin: 20px 0 5px 0;
}

.chat-search-bar input {
    background-color: #F9FAFC;
    border: 1px solid #DBE5ED;
    padding: 0 43px 0 20px;
    height: 48px;
    width: 100%;
    border-radius: 8px;
}

.chat-search-bar input::placeholder {
    color: #AFBBC6;
    font-weight: 500;
    font-size: 14px;
}

.chat-search-bar img {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
}

.nInbox .nchat-profile-list ul {
    padding: 5px 8px;
    margin: 0;
    height: 66vh;
    overflow-y: auto;
}

.nInbox .nchat-profile-list ul li {
    margin-bottom: 10px;
}

.nInbox .nchat-profile-list ul li a {
    border-radius: 10px;
}

.nInbox .nchat-profile-list ul li.active a .chat-list-inner {
    border: 1px solid #DBE5ED;
    background: #f9fafc;
}

.nInbox .nchat-profile-list ul li.active a .chat-name h6 {
    color: #4B5155;
}

.nInbox .nchat-profile-list ul li.active a .chat-list-inner .chat-name .date-content {
    color: #4B5155;
}

.nInbox .nchat-profile-list ul li.active a .chat-list-inner .chat-name p {
    color: #7B8793;
}

.nInbox .nchat-profile-list ul li:hover a .chat-list-inner {
    border: 1px solid #DBE5ED;
    background: #f9fafc;
}

.nInbox .nchat-profile-list ul li:hover a .chat-name h6 {
    color: #4B5155;
}

.nInbox .nchat-profile-list ul li:hover a .chat-list-inner .chat-name .date-content {
    color: #4B5155;
}

.nInbox .nchat-profile-list ul li:hover a .chat-list-inner .chat-name p {
    color: #7B8793;
}

.nInbox .nchat-profile-list ul li:last-child {
    margin-bottom: 0;
}

.chat-list-inner {
    display: flex;
    align-items: center;
    padding: 10px 5px;
    border-radius: 10px;
    border: 1px solid transparent;
    transition: .3s ease-in-out;
}

.chat-list-inner .chat-img {
    width: 55px;
    height: 55px;
}

.chat-list-inner .chat-img img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 1px solid transparent;
    object-fit: cover;
    transition: .3s;
}

.chat-list-inner .chat-name .date-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: #7B8793;
}

.chat-list-inner .chat-name p {
    margin: 0px;
    font-size: 14px;
    color: #AFBBC6;
    line-height: 1.3;
}

.chat-list-inner .chat-name .chat-p-available {
    font-size: 12px;
}

.chat-view-main {
    height: 100%;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.chat-view-main .chat-head {
    background: #F4F5F8;
    border: 1px solid #DBE5ED;
    border-top-left-radius: 25px;
    border-top-right-radius: 25px;
}

.chat-view-main .chat-head h5 {
    color: #4B5155;
}

.chat-vh-btn {
    display: flex;
    align-items: center;
    justify-content: end;
}

.chat-vh-btn button {
    padding: 8px;
    border-radius: 50%;
    transition: .3s ease-in-out;
    margin-right: 2px;
    outline: none;
}

.chat-vh-btn button:nth-child(1) {
    margin-top: 6px;
}

.chat-vh-btn button:hover {
    background-color: #f9fafc;
}

.mark-read img {
    width: 25px;
    display: block;
    color: #4B5155;
}

.read-message {
    width: 25px !important;
    height: 25px;
}

.chat-rate img {
    width: 25px;
}

.chat-view-main.full-width {
    flex: 0 0 100%;
}

.chat-view-main.full-width {
    border-right: 1px solid #e9e9e9;
}

.sc-btn {
    height: 40px;
    font-size: 14px;
    padding: 0 14px;
    font-weight: 500;
}

.chat-property-prompt {
    margin-top: 20px;
}

.cv-date p {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    margin: 30px 0;
    color: #4B5155;
}

.chat-property-prompt {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f5f8;
    font-size: 14px;
    padding: 7px 15px;
    border-radius: 25px;
    margin-bottom: 10px;
}

.chat-property-prompt img {
    width: 16px;
}

.chat-property-prompt p {
    margin: 0 4px 0 8px;
}

.chat-property-prompt a {
    text-decoration: underline;
    color: var(--them-secondary);
}

.chat-message {
    display: flex;
    align-items: flex-start;
    margin-top: 25px;
}

.chat-message .chat-img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.chat-message .chat-detail {
    flex: 1;
}

.chat-detail h6 {
    color: #4B5155;
}

.chat-detail h6 span {
    font-weight: 400;
    color: #7B8793;
    font-size: 13px;
}

.cm-content {
    font-size: 15px;
    color: #7B8793;
    margin-top: 5px;
}

.chat-message .chat-detail .cm-content-main .cm-content:nth-child(1) {
    margin-top: 0;
}

.chat-property-text {
    margin-bottom: 25px;
}

.chat-message .chat-detail .chat-seen {
    font-size: 13px;
    font-weight: 500;
    color: #4B5155;
}

.chat-media {
    margin-top: 10px;
    margin-left: 48px;
}

.chat-media img {
    border-radius: 5px;
    width: 380px;
    height: 280px;
    object-fit: cover;
    margin-bottom: 10px;
    display: block;
}

.chat-media img:last-child {
    margin-bottom: 0;
}

.cm-group {
    margin-top: 20px;
}

.in-chat-profile {
    position: relative;
}

.in-chat-profile .chat-img {
    position: absolute;
    top: 0;
}

.cview-detail {
    flex: 0 0 30%;
    max-width: 30%;
}

.cm-group-left {
    margin-left: 10px;
}

.cm-group-left .chat-host {
    margin-top: 0 !important;
}

.cview-detail .chat-head {
    border-left: 1px solid #e9e9e9;
    border-right: 1px solid #e9e9e9;
}

.cview-detail .host-property {
    padding: 5px 8px;
    height: calc(100vh - 234px);
    overflow-y: auto;
    border-left: 1px solid #e9e9e9;
    border-right: 1px solid #e9e9e9;
}

.chat-property-media img {
    margin: 10px 0 0 48px;
}

.chat-view-main .chat-head h5 {
    margin: 0 16px 0 0;
}

/* .chat-property-media:last-child img{
    margin-top: 0;
} */
.cv-detail-btn img {
    filter: brightness(0);
    width: 22px;
}

.host-property {
    padding: 0 15px;
}

p.unit-code-inner {
    font-weight: 500;
    color: black;
    margin-bottom: 10px;
    margin-top: 10px;
    display: block;
}

.hp-detail {
    border-bottom: 1px solid #e9e9e9;
}

.hp-detail-img {
    position: relative;
    height: 200px;
}

.hp-detail-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hp-detail-img span {
    background-color: #fff;
    position: absolute;
    top: 5px;
    left: 5px;
    border-radius: 3px;
    padding: 1px 6px;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 12px;
}

.hp-detail-content {
    text-align: center;
    margin: 15px 0 20px;
}

.hp-detail-content h5 {
    font-size: 18px;
}

.hp-detail-content p {
    font-size: 14px;
    margin: 3px 0 10px;
}

.hp-detail-content .hp-btn {
    width: 90%;
    font-weight: 500;
    transition: .3s all;
    font-size: 16px;
}

.hp-user {
    flex: 1;
}

.hp-user-img img {
    width: 45px;
    height: 45px;
    object-fit: cover;
}

.hp-reserv-detail {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e9e9e9;
    padding: 20px 0;
}

.hp-reserv-detail h6 {
    font-size: 14px;
}

.hp-reserv-detail p {
    text-transform: capitalize;
    font-size: 14px;
}

.hp-total p {
    color: #000;
    font-weight: 500;
}

.nInbox ::-webkit-scrollbar-thumb {
    background-color: #DBE5ED;
    border-radius: 25px;
}

.nInbox ::-webkit-scrollbar-track {
    border-radius: 0;
    background-color: transparent;
    box-shadow: none;
    border-radius: none;
}

.nInbox ::-webkit-scrollbar {
    width: 5px;
}

.nInbox .main-chat {
    height: calc(100vh - 160px);
    background: #F9FAFC;
    border-bottom-right-radius: 25px;
    border-bottom-left-radius: 25px;
    padding: 5px 0 0;
}

.nInbox .cv-msg {
    height: 80%;
    padding: 0 10px;
    overflow-y: auto;
    margin: 0 7px;
}

.nInbox .cv-msg-inner {
    width: 95%;
    margin: 0 auto;
}

.nInbox .chat-field {
    padding: 13px 0px;
    width: 95%;
    margin: 0 auto;
    height: 20%;
    display: flex;
    flex-flow: wrap;
}

.chat-field-inner {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
}

.nInbox .chat-field input {
    height: 55px;
    width: 100%;
    border-radius: 30px;
    padding: 10px 0 12px 18px;
    border: 1px solid #DBE5ED;
}

.nInbox .chat-field input::placeholder {
    color: #7B8793;
    font-weight: 500;
    font-size: 13px;
}

.nInbox .chat-field .chat-field-btn {
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
}

.nInbox .chat-field .atach-icon img {
    margin-right: 10px;
    width: 18px;
}

.ninbox-property {
    display: none;
}

.ninbox-property .inbox-inner-property {
    box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 3px;
}

.ninbox-property .inbox-property-img {
    width: 80px;
    flex: none !important;
}

.ninbox-property .inbox-property-content h6 {
    text-decoration: underline;
}

.ninbox-property .inbox-property-content span {
    color: var(--them-secondary);
    display: inline-block;
    position: relative;
    border: 1px solid #e9e9e9;
    padding: 4px 10px;
    border-radius: 7px;
    display: flex;
    align-items: center;
    max-width: max-content;
}

.ninbox-property span i {
    color: var(--them-secondary);
    margin-right: 2px;
}

.ninbox-property .inbox-property-content span:hover {
    background-color: var(--theme-primary);
    border: 1px solid var(--theme-primary);
    color: #fff;
}

.ninbox-property .inbox-property-content span:hover i {
    color: #fff;
}

.nInbox .nchat-profile-list {
    height: 100%;
    border-right: none;
    padding-right: 0;
}

.cv-mb-hide {
    display: none;
}

.cv-mb-hide img {
    width: 12px;
}

#images-box {
    display: flex;
    align-items: center;
    flex-flow: wrap;
    width: 100%;
    margin-bottom: 5px;
}

#images-box .img-box {
    width: 60px;
    height: 50px;
    margin-right: 10px;
    position: relative;
}

#images-box .img-box::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0, transparent);
    border-radius: 5px;
}

#images-box .img-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
}

#images-box .img-box .cross {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #fff;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

#images-box .img-box .cross img {
    width: 8px;
    height: auto;
}

.text-upper {
    text-transform: uppercase !important;
}


.phone-number.us-rl {
    height: 44px;
}

a.edit-user-role {
    color: black;
    margin-left: 11px;
}

.reserv-tab .reserv-tab-content {
    min-height: min-content;
    max-height: 82vh;
    overflow-y: auto;
    overflow-x: hidden;
}

.reserv-tab .reserv-tab-content .reserv-tab-content-in {
    height: 100%;
}



.tb-scroll {
    overflow-y: scroll;
    max-height: 50vh;
}


.your-side-list {
    height: 66vh !important
}

.side-list {
    height: 66vh;
    overflow: auto;
    padding: 0 25px 0 0;
}

#appendLoader {
    text-align: center;
}



.spinner-icall {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: 9px solid #f5c33e;
    animation: spinner-bulqg1 0.6400000000000001s infinite linear alternate,
        spinner-oaa3wk 1.2800000000000002s infinite linear;
}

@keyframes spinner-bulqg1 {
    0% {
        clip-path: polygon(50% 50%, 0 0, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%);
    }

    12.5% {
        clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%);
    }

    25% {
        clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 100% 100%, 100% 100%);
    }

    50% {
        clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%);
    }

    62.5% {
        clip-path: polygon(50% 50%, 100% 0, 100% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%);
    }

    75% {
        clip-path: polygon(50% 50%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 50% 100%, 0% 100%);
    }

    100% {
        clip-path: polygon(50% 50%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 0% 100%);
    }
}

@keyframes spinner-oaa3wk {
    0% {
        transform: scaleY(1) rotate(0deg);
    }

    49.99% {
        transform: scaleY(1) rotate(135deg);
    }

    50% {
        transform: scaleY(-1) rotate(0deg);
    }

    100% {
        transform: scaleY(-1) rotate(-135deg);
    }
}



.load-icon {
    width: 100%;
    position: absolute;
    height: 100%;
    z-index: 1;
    background: #ffff;
    left: -3px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calenBox {
    display: flex;
    flex-wrap: wrap;
    position: relative;
}

.ri-calendar-close-line:before {
    content: "\f38e";
}

.theme-color {
    color: var(--theme-primary);
}

.custom-tooltip {
    --bs-tooltip-bg: #fff;
    --bs-tooltip-color: #000;
    --bs-tooltip-font-size: 12px;
}

.custom-tooltip .tooltip-inner {
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    font-weight: 500;
}

.nav.profile-nav {
    justify-content: end;
}

.slick-slide {
    height: auto !important;
}

.log-btn {
    display: flex;
    align-items: center;
}

.logo-main {
    position: relative;
    backdrop-filter: blur(20px) brightness(230%);
    padding: 15px;
    border-radius: 10px;
    overflow: hidden;
}

a.sign-in-btn {
    color: #fff;
    margin-right: 25px;
    font-weight: 400;
    font-size: 18px;
}

a.signup-in-btn {
    background: #ffff;
    border: unset;
    border-radius: 7px;
    line-height: 1;
    height: 42px;
    display: block;
    color: #000000;
    padding: 11px 20px;
    font-weight: 500;
    font-size: 18px;
    width: 140px;
    text-align: center;
}

button.host.header-btn a {
    color: #ffff;
    font-weight: 400;
    font-size: 18px;
}

.slide-main {
    height: 78vh;
    position: relative;
    background-position: center;
    background-size: cover;
    display: flex !important;
    align-items: center;
}

img.logo {
    width: 120px;
}

section.home-slider {
    position: relative;
    height: 78vh;
    overflow: hidden;
}

a.menubtn.header-btn>i {
    font-size: 18px;
    position: relative;
    top: 5px;
}

.slick-dots {
    bottom: 130px !important;
}

.slick-dots li {
    background: #ffffff7a;
    border-radius: 17px;
    width: 15px;
    height: 15px;
    transition: 0.6s ease;
}

li.slick-active {
    /* background: #f5c33e;
  width: 30px; */
    transition: 0.6s ease;
}


.slick-next {
    right: 70px;
    background: #ffffffc7 !important;
    height: 40px;
    width: 40px;
    border-radius: 35px;
}

.slick-prev {
    left: 70px !important;
    background: #ffffffc7 !important;
    height: 40px;
    width: 40px;
    border-radius: 35px;
    z-index: 9;
}


.slide-main:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: #00000063;
}

.nav-inerpage {
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 10px;
}

.nav-scrolled {
    position: sticky;
    top: 0;
    z-index: 900;
    background: #fff;
    box-shadow: 0 5px 20px 2px #0000001f;
    transition: .3s;
    animation: .3s linear sticky;
}

.nav-scrolled-fixed {
    position: fixed;
    top: 0;
    z-index: 9999;
    background: #fff;
    box-shadow: 0 5px 20px 2px #0000001f;
    transition: transform 0.3s, opacity 0.3s;
    animation: stickySlideDown 0.3s linear;
}

@keyframes stickySlideDown {
    0% {
        opacity: 0.7;
        transform: translateY(-50px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.wishlist-main a {
    font-size: 18px;
    margin-right: 20px;
    color: #ffff;
}


.nav-scrolled a.signup-in-btn {
    background: #f5c33e;
    color: #fff;
}

.fc-primary {
    color: #f5c33e;
}

.slide-main h1 {
    font-size: 60px;
}

.slide-main h4 {
    font-size: 30px;
}

.slick-dots li button:before {
    display: none;
}

.nav-menu:hover {
    background: inherit;
    border: inherit;
}

.nav-menu {
    background: transparent;
    font-size: 18px;
    border: 0px solid transparent;
}

.wishlist-main i {
    position: relative;
    top: 3px;
}

.user.dropdown {
    font-size: 20px;
}

ul.search-reservation {
    display: flex;
    margin-bottom: 0;

}

.filter-box {
    align-items: center;
    border: 1px solid #ededed;
    padding: 25px 20px;
    border-radius: 10px;
    position: absolute;
    width: 100%;
    bottom: 1px;
    background: #ffff;
    box-shadow: 1px 4px 7px 1px #00000021;
    left: 22.1%;
    z-index: 100;
}

.views {
    position: absolute;
    display: flex;
    background: #00000070;
    top: 15px;
    left: 15px;
    padding: 2px 10px;
    color: #ffff;
    font-size: 12px;
    font-weight: 400;
    border-radius: 22px;
    z-index: 12;
}

.views i {
    margin-right: 3px;
    position: relative;
    top: 2px;
}

p.unit-code {
    font-size: 16px;
    margin-bottom: 0;
    color: var(--them-secondary);
}

.pr-code p {
    font-size: 15px !important;
}

.pr-code {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
}

.product-price span {
    color: var(--them-secondary);
    /* padding-left: 2px; */
}

.product-price .sar-pr {
    color: black !important;
    font-weight: 400;
}

.nav-fixed button.host.header-btn {
    color: white;
    font-size: 18px;
    font-weight: 400;
}



.fd-icon.loadskull:before {
    border-radius: 80px;
    border: 1px solid #d5d5d5;
}

.sc-home p {
    font-size: 28px;
    color: black;
}

.nav-scrolled .host {
    color: #000 !important;
}

button.btn-theme-white {
    border: 1px solid #fff;
    width: 230px;
    color: #f5c33e;
    padding: 0 30px;
    background: #fff;
    border-radius: 8px;
    font-size: 21px;
    height: 48px;
    font-weight: 500;
}

.vr-slide {
    border-radius: 10px;
    width: 150px;
    height: 174px;
    overflow: hidden;
}

.vr-slide img {
    width: 100%;
    object-fit: cover;
}

.vertical-slid-main .slick-slide {
    float: none !important;
}

swiper-container.mySwiper {
    height: 500px;
}

swiper-slide {
    height: auto !important;
}


.slide-vertical {
    display: flex;
    justify-content: center;
}

section.what-can-do {
    padding: 80px 0px;
}

.do-for-box {
    padding: 20px 0px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    margin-bottom: 0px;
}

.fd-icon img {
    width: 90%;

}

.fd-content h3 {
    font-size: 20px;
    font-weight: 500;
    color: #535353;
    margin-bottom: 5px;
}

.fd-content p {
    margin-bottom: 0;
}

.fd-icon {
    width: 50px;
    margin-right: 10px;
    border-radius: 46px;
    height: 50px;
    padding: 8px;
    border: 1px solid #c3c3c3;
    align-items: center;
    display: flex;
    justify-content: center;
}


.loadskull {
    position: relative;
}

.loadskull .skullinner {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    border-radius: 5px;
    z-index: 99;
    border: 1px solid #ffff;
}

.loadskull:before {
    animation: skeleton-loading 1s linear infinite alternate;
    content: '';
    position: absolute;
    width: 100%;
    height: 103%;
    z-index: 99;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 3px;

}

@keyframes skeleton-loading {
    0% {
        background-color: hwb(0 83% 17%);
    }

    100% {
        background-color: hsl(0, 0%, 92%);
    }
}



.downApp-img img {
    width: 650px;
    position: absolute;
    bottom: 0;
    left: -180px;
}

section.download-app {
    padding-top: 200px;
    padding-bottom: 100px;
}

section.download-app .row {
    align-items: center;
}

.downApp h2 {
    font-size: 70px;
    margin-bottom: 20px;
}



.downApp-img {
    position: relative;
}

.download-app .row,
.download-app .downApp,
.download-app .downApp-img,
.download-app-container {
    height: 100%;
}



.download-app-container {
    background: #f5c33e;
    height: 360px;
    border-radius: 20px;
    padding: 0px 60px;

}

.blk-bar {
    position: absolute;
    background: black;
    height: 41%;
    width: 100px;
    top: 0;
    border-radius: 0px 0px 10px 10px;
}

.downApp .fc-white {
    position: relative;
    padding-right: 7px;
    color: #fff;
}

.downApp {
    position: relative;
}





.filter-design .filter-box {
    position: inherit;
    margin: 0px auto;
    margin-top: 30px;
}

section.filter-main-inner {
    padding: 30px 0px;
}

.filter-design {
    position: relative;
    z-index: 15;
}

.nav-menu {
    color: #fff;
}

.head-miniheight {
    text-align: left;
    justify-content: start;
    line-height: normal;
}



.nav-inerpage button.host.header-btn a,
.nav-inerpage a.menubtn.header-btn,
.nav-inerpage a.sign-in-btn,
.nav-inerpage .wishlist-main a,
.nav-inerpage .nav-menu,
.nav-inerpage button.host .header-btn {
    color: #000;
}

.nav-inerpage .trans-bt,
.nav-scrolled .trans-bt {
    box-shadow: -1px 5px 9px 3px #c7c7c759;
}

.nav-scrolled button.host.header-btn a,
.nav-scrolled a.menubtn.header-btn,
.nav-scrolled a.sign-in-btn,
.nav-scrolled .wishlist-main a,
.nav-scrolled .nav-menu {
    color: #000;
}

.nav-listing .trans-bt {
    box-shadow: none !important;
}



.filter-cont {
    position: relative;
    width: 70%;
}

.filter-design .filter-cont {
    width: 70%;
    margin: 0px auto;
}

.host-slider {
    height: 280px;
    position: relative;
    z-index: 11;
}
.swiper-pagination-bullet{
    background: #f5c33e !important;
}
.swiper-pagination-bullet-active {
    background-color: var(--theme-primary) !important;
}


.user-book img {
    width: 30px;
    height: 30px;
    border: 1px solid gray;
    border-radius: 49px;
    object-fit: cover;
    object-position: top;
    position: relative;
    right: 3px;
}


.user-book {
    background-image: url('../images/userbook.svg');
    background-size: 100% 100%;
    width: 54.38px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 68px;
    background-repeat: no-repeat;
    position: absolute;
    z-index: 12;
    bottom: 11px;
    left: 18px;
    background-position: center center;
    border: none;
    outline: none;
    /* box-shadow: -2px 3px 3px 1px #00000026; */
}

.fw-700 {
    font-weight: 700;
}

.footer_list {
    display: flex;
    justify-content: space-between;
}

ul.foot-nav li {
    margin-bottom: 15px;
}

.store-link img {
    width: 100%;
}

.store-link {
    text-align: right;
}

.store-link a {
    display: block;
    margin-top: 15px;
}

.property-inner-detail .product-rate {
    background: #f5c33e;
    width: fit-content;
    padding: 7px 11px;
    color: #fff;
    font-weight: 500;
    border-radius: 8px;
    margin-top: 10px;
    text-align: center;
    margin-bottom: 0;
}

.property-inner-detail .product-rate i {
    padding-right: 6px;
}

.pr-list {
    width: 245px;
    padding: 0;
    padding: 10px 0px;
    overflow: hidden;
    top: 24px !important;
}

.pr-list li a {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 3px 20px;
}

.pr-list li a i {
    margin-right: 9px;
    font-size: 18px;
}


.review-progress .progress-bar {
    background-color: #f5c33e;
    border-radius: 22px;
}

.review-progress .progress {
    height: 7px;
    width: 100%;
}

.review-progress {
    display: flex;
    align-items: center;
}

.prog-cont {
    font-size: 20px;
    margin-right: 20px;
    width: 290px;
}

.review-progress-count {
    font-size: 17px;
    font-weight: 500;
    margin-left: 20px;
}

.popup1 {
    left: 0;
}

.popup2 {
    right: 0;
    width: 50%;
}

.input-field input {
    height: 50px;
    border: 2px solid #d5d5d5;
    border-radius: 4px;
    padding: 10px;
    font-weight: 500;
    width: 100%;
    font-size: 14px;
}

.search-reservation li span {
    margin-bottom: 10px;
    text-align: left;
}

.input-field-gst {
    height: 50px;
    border: 2px solid #d5d5d5;
    border-radius: 4px;
    padding: 10px 10px;
}

.for-filter.openscenter:after,
.for-filter.openscenter:before {
    content: none !important;
    display: none;
}

li.filter-bt {
    display: flex;
    align-items: center;
    justify-content: center;
}

.popularcity p {
    margin-bottom: 0;
}

section.filter-main {
    padding-top: 60px;
}

.input-field input::placeholder {
    color: #000;
}

.for-ar {
    display: none;
}

.for-eng {
    display: inherit;
}

.mapCol {
    border-radius: 10px;
    overflow: hidden;
}

.ongrid .col-cstm {
    width: 33.33%;
}

.ongrid .mapCol {
    position: sticky;
    top: 100px;
    height: 100vh;
}

.ongrid .mapCol .map-view {
    height: 100% !important;
}

.ongrid .loader-img {
    position: relative !important;
    height: 100vh !important;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 0;
}

.nav-inerpage a.signup-in-btn {
    background: #f5c33e;
    color: #fff;
    background: #f5c33e;
    color: #fff;
}

.aft-map .loader-img {
    position: relative !important;
}
#loader-more .dots{
    margin: 0 auto;
}
.trans-bt {
    padding: 4px 20px;
    border-radius: 12px;
    display: block;
}

.cs-h {
    height: 40px !important;
}

.rev-date {
    font-weight: 400;
    margin-top: 10px;
    margin-bottom: 10px;
    color: gray;
}

.review-count {
    margin-bottom: 35px;
}

.v-none {
    visibility: hidden;
    height: 0 !important;
}


.vr-slide img {
    width: 150px;
    border-radius: 10px;
    overflow: hidden;
    height: 174px;
}

.vr-pack {
    margin-right: 8px;
    width: 150px !important;
}

.vr-pack.sl-1 .slick-slide {
    position: relative;
    bottom: 70px;
}

.vr-pack.sl-3 .slick-slide {
    position: relative;
    bottom: 70px;
}

.no-scroll .filter-design {
    z-index: 102 !important;
}

.currancy-dropdown {
    margin-right: 20px;
}


.product-detail {
    position: relative;
}

.nav-listing .logo-main {
    background: #ffff;
    box-shadow: 2px 4px 11px 7px #0000000f;
}

.view-img {
    height: 280px;
    overflow: hidden;
    max-height: 280px;
}

.nav-inerpage .trans-bt {
    color: black !important;
}

.slide-main .container {
    position: relative;
    z-index: 1;
}

/* a:hover {
    color: inherit;
} */

.theme-btn:hover {
    color: #fff !important;
}

@media (max-width:1440px) {
    .vr-slide {
        width: 130px;
        height: 145px;
    }

    /* .download-app-container {
        height: 380px;

    } */

    .slide-main h1 {
        font-size: 48px;
    }

    .slide-main h4 {
        font-size: 24px;
    }

    .slide-main h4 {
        font-size: 24px;
    }

    .popularcity p {
        font-size: 12px;
        font-weight: 600;
    }

    .pop-img img {
        margin-bottom: 5px;
    }

    .input-field input,
    .input-field-gst {
        height: 45px;
    }

    .search-reservation li span {
        font-size: 13px;
    }

    .filter-box {
        padding: 20px 20px;
    }

    .slick-dots {
        bottom: 90px !important;
    }

    .popup-main {
        top: 120px;
        padding: 30px;
        width: 54%;
    }

    .daterangepicker {
        padding: 20px 30px;
        margin: -4px 0 0 0;
    }

    .pr-list li a {
        padding: 5px 25px;
    }

    .vr-pack {
        width: 130px !important;
    }

    .vr-slide img {
        width: 130px;
        border-radius: 10px;
        overflow: hidden;
        height: 160px;
    }

    section.second-home {
        height: 465px;
    }

    .product-detail .product-price,
    p.unit-code {
        font-size: 13px !important;
        font-weight: 500;
    }

    .downApp h4 {
        font-size: 30px;
    }

    .bg-transparent-btn,
    .grey-btn,
    .theme-btn {
        padding: 8px 20px;
        font-size: 16px;
        height: 47px;
    }

    .review-content .limit-para {
        font-size: 14px;
    }

    .rev-date {
        margin-top: 6px;
        margin-bottom: 5px;
        color: #b1b1b1;
        font-size: 13px;
    }

    .ongrid img.product-img {
        height: 210px !important;
    }

    /* .daterangepicker .calendar-table td,
    .daterangepicker .calendar-table th {
        width: 30px;
        height: 38px;
    } */

    a.menubtn.header-btn,
    button.host.header-btn a,
    .trans-bt,
    .nav-menu {
        font-size: 16px !important;
    }

    img.logo {
        width: 130px;
    }


}


.tel-no {
    display: flex;
    direction: ltr;
}

section.home-slider li.slick-active {
    background-color: #f5c33e;
    width: 23px;
    transition: 0.3s ease;
}




span#display_phone {
    display: -webkit-inline-box;
    direction: ltr;
}

span.vat-msg {
    display: block;
    text-align: center;
    font-size: 12px;
    margin-top: -10px;
    color: gray;
    font-weight: 400;
}

.user.singal-user .theme-btn {
    width: 170px;
    font-size: 16px;
    padding: 0 !important;
}

/* section.host-banner {
    margin-top: 100px;
    overflow: hidden;
} */


/* otp */
.verification-code--inputs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: none;
    width: 100%;
    height: 100%;
    border: 1px solid #e2e2e2;
    border-radius: 25px;
    padding: 4px 20px;
}

.verification-code--inputs input {
    border: none;
    transition: .2s ease-out;
    width: 30px;
    height: 30px;
    text-align: center;
    outline: none;
    box-shadow: none !important;
}

.verification-code--inputs input:focus {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

.otp-in-text {
    text-align: center;
    font-size: 12px !important;
}

.loadskull {
    appearance: none !important;
    pointer-events: none;
}

/* new listing design */
.cont-listing h2 {
    font-size: 45px;
    font-weight: 500;
}

.get-list ul li {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e5e5e5;
    padding: 20px 0px;
}

.container-listing {
    width: 1680px;
    margin: 0px auto;
}

.get-img img {
    width: 110px;
}

.get-cont p {
    margin: 0;
    font-size: 20px;
}

.get-img {
    margin-left: 35px;
}

.get-cont h3 {
    font-size: 30px;
    font-weight: 400;
    margin-bottom: 12px;
}

.get-list ul {
    margin: 0;
}


.progress.listing .progress-bar {
    background: #181818;
    border-radius: 0px 10px 10px 0px;
}

.progress.listing {
    border-radius: 10px;
    height: 8px;
    width: 33%;
    margin-right: 2px;
}

.lst-progress {
    margin-bottom: 13px;
    display: flex;
    width: 100%;
}

.step-img img {
    width: 100%;
}

.cont-listing h1 {
    margin-bottom: 20px;
    font-weight: 400;
    font-size: 54px;
}

.cont-listing p {
    font-size: 20px;
}

.listing-category {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
}

button.btn-save {
    border: 2px solid #707070;
    height: 47px;
    width: 150px;
    border-radius: 10px;
    background: transparent;
    font-size: 18px;
    background-color: #fff;
    font-weight: 500;
}

.listing-map {
    height: 100vh;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.map-view-full-location {
    height: 500px;
}

.map-bx {
    height: 500px;
    position: relative;
    width: 100%;
}

.lst-mp {
    width: 100%;
}

.map-view-full-location {
    border-radius: 10px;
}


.loc-lst {
    position: relative;
}

.loc-lst img {
    width: 26px;
    position: absolute;
    top: 13px;
    left: 21px;
}

.loc-lst input {
    padding-left: 60px;
    height: 60px;
    font-size: 20px;
    color: black;
}

.map-listing {
    height: 100%;
    overflow: auto;
    padding: 100px 0px;
}

.br-line {
    width: 100%;
    height: 1px;
    background: #d8d8d8;
    margin: 40px 0px;
}

.exact-map {
    padding-bottom: 40px;
}

.exact-map iframe {
    border-radius: 10px;
}

.ext-lc .switch {
    height: 30px;
    width: 65px;
}

.ext-lc .slider-list::before {
    background-color: #000;
    bottom: 2px;
    content: "";
    height: 20px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 20px;
}

.ext-lc input:checked+.slider-list:before {
    transform: translateX(32px);
    background: #23bc4c;
}

.ext-lc {
    margin-top: 14px;
}

section.new-listing-amenities {
    height: 100%;
    overflow: auto;
    padding: 100px 0px;
}

.photo-loading img {
    width: 65px !important;
}

/* .listing-counter {
    width: 800px;
} */
.listing-counter-main {
    padding-bottom: 60px;
}

.review-prod {
    box-shadow: 0px 3px 3px 3px #0000001a;
    border-radius: 15px;
    padding: 20px;
}

.justify-align-center {
    align-items: center;
}

.rv-pr-img img {
    width: 100%;
}

.rv-pr-img {
    margin-bottom: 15px;
}

.rv-pr-cont h3 {
    font-size: 20px;
    margin-bottom: 10px;
}

.new-listing-rev {
    overflow: auto;
    margin-top: 80px;
    height: calc(100vh - 165px);
}

.new-listing-rev form{
    height: 100%;
}

.drop-zone .dropzone {
    border-style: dashed !important;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.drop-zone {
    margin-bottom: 30px;
}

.new-listing-dropzone {
    min-height: 100vh;
    height: auto;
    max-height: auto;

    display: flex;
    align-items: center;
    padding: 100px 0px;
}

a.dz-remove:before {
    color: black;
}

.upload__box {
    margin-top: 30px;
}

.upload__inputfile {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
}

.upload__btn-box {
    margin-bottom: 10px;
}

.upload__img-wrap {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.upload__img-box {
    width: 100%;
    padding: 0;
    margin-top: 20px;
    position: relative;
    border: 1px solid #dedede;
    border-radius: 8px;
    /* overflow: hidden; */
}

.img-bg {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    position: relative;
}

.setcover {
    width: 100%;
}

.mainCover img {
    object-position: center;
    width: 100%;
    height: 350px;
    object-fit: cover;
    border-radius: 10px;
}

.new-listing-cover {
    height: 100%;
    overflow: auto;
    padding: 120px 0px;
}

.mainCover {
    position: relative;
    margin-bottom: 40px;
}

.mainCover .promo-dropdown {
    position: absolute;
    right: 23px;
    top: 20px;
}

.mainCover button.promo-drop-btn {
    background: #ffffffd1;
    font-size: 20px;
    height: 30px;
    width: 30px;
}


.drop-img {
    position: absolute;
    right: 30px;
    top: 16px;
    z-index: 9;
}

.drop-img button.promo-drop-btn {
    background: #ffffffd1;
    font-size: 27px;
    height: 40px;
    width: 40px;
    box-shadow: -1px 3px 8px 5px #00000024;
}

.upload__btn.ad-more {
    height: 252px;
    width: 252px;
    border: 2px solid #d1d1d1;
    background: transparent;
    border-style: dashed;
    border-radius: 10px;
    padding: 45px 16px;
    text-align: center;
}

.upload__btn.ad-more i {
    display: block;
    font-size: 60px;
}

.upload__btn.ad-more span {
    display: block;
    font-size: 23px;
    font-weight: 500;
}

.loc-map-sc {
    display: flex;
    align-items: center;
}

.content-head {
    margin-bottom: 35px;
}

.loc-map-sc {
    display: flex;
    align-items: center;
    height: 100vh;
    overflow: auto;
    /* padding: 60px 0px; */
}

.nav-listing {
    background: #ffff;
}

.listing-category.adplc {
    justify-content: space-between;
}

.addCover {
    position: absolute;
    backdrop-filter: blur(14px) brightness(189%);
    height: 45px;
    width: 135px;
    border: transparent;
    border-radius: 10px;
    overflow: hidden;
    left: 10px;
    top: 10px;
    color: #ffff;
}

.rv-rating svg {
    width: 16px;
}

.dropzone .dz-preview .dz-remove {
    color: black;
    text-decoration: underline;
}



.fsrch .listing-checkbox-tile {

    display: block;
    text-align: center;
    padding: 12px 5px;
}

.fsrch .listing-checkbox-icon {
    margin: 0px auto;
}

.fsrch .listing-checkbox-label {
    justify-content: center;
}

.services li.property_types_image img {
    margin-bottom: 5px;
}

.fancybox__container {
    z-index: 999999 !important;
}

.price-listing {
    padding: 100px 0px;
}

section.new-listing-question {
    padding: 100px 0px;
}

section.new-listing-hstAgree {
    padding: 100px 0px;
    text-align: right;
    direction: rtl;
}

.form-check.cust-check.listing-ql {
    border: 1px solid #e4e4e4;
    height: 50px;
    padding: 9px 45px;
    border-radius: 10px;
}

.rv-pr-img img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: 10px;
    border: 1px solid #dfdfdf;
}

.pop-not {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999000;
    padding: 24px 57px;
    border-radius: 0px;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 22px;
    right: 17px;
    font-size: 20px !important;
    color: rebeccapurple !important;
}

.dz-max-files-reached+.nxtBtn {
    opacity: 1;
}

.uploaded-success img {
    width: 200px;
    margin-bottom: 15px;
}

.uploaded-success {
    margin-top: 40px;
    text-align: center;
}

.upload-cont {
    font-size: 18px;
    font-weight: 500;
    color: green;
    position: relative;
    right: 23px;
}

.dropzone .dz-preview .dz-success-mark,
.dropzone .dz-preview .dz-error-mark {
    z-index: 50 !important;
}

.dropzone .dz-preview .dz-progress {
    z-index: 50 !important;
}

.v-h-none {
    height: 0;
    transition: 0.3s ease;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    border: none;
    opacity: 0;
}

.total-guest {

    transition: 0.3s ease;
}

.listing-checkbox-wrapper.inner-pg {
    width: 90px;
    height: 80px;
}

.inner-pg .listing-checkbox-tile {
    flex-wrap: wrap !important;
    flex-direction: column;
}

.inner-pg .listing-checkbox-icon {
    margin: 0px auto;
}

.inner-pg span.listing-checkbox-content,
.inner-pg span.listing-checkbox-label {
    font-size: 13px !important;
    line-height: 13px !important;
}

.inner-pg .listing-checkbox-icon {
    width: 30px;
    height: 22px;
}

.inner-upload {
    width: 20%;
    margin: 10px;
}

.photos-sc .upload__box {
    margin-top: 5px;
}

.tab-pane .listing-counter-main {
    padding-bottom: 0px !important;
}

.services {
    padding-bottom: 30px;
}


.otp-input-wrapper {
    width: 100%;
    text-align: center;
    display: block;
    margin: 0px auto;
    border: 1px solid gray;
    border-radius: 40px;
}

.otp-input-wrapper input {
    padding: 0;
    width: 200px;
    height: 47px;
    font-size: 25px;
    font-weight: 500;
    color: #3e3e3e;
    background-color: transparent;
    border: 0;
    margin-left: 0;
    letter-spacing: 30px;
    margin: 0px auto;
    text-align: center;
}

.otp-input-wrapper input:focus {
    box-shadow: none !important;
    outline: none !important;
    border: none !important;
}

.otp-input-wrapper input::placeholder {
    letter-spacing: 15px;
}

section.new-listing.translate {
    height: auto;
}

/* host dashboard journey */
.container-host {
    max-width: 92%;
    margin: 0 auto;
}

section.host-listing-sec {
    padding: 100px 0px;
}

.host-sh-right {
    text-align: right;
}

*/ .sc-100 {
    padding: 100px 0px;
}

.tb-search {
    position: relative;
}

.tb-search i {
    position: absolute;
    top: 4px;
    left: 14px;
    font-size: 25px;
    color: #8A8A8A;
}

.tb-search input {
    border-radius: 35px;
    padding-left: 45px;
    background-color: #FAFAFA;
    border-color: #707070;
    color: #8A8A8A;
}

select.sort {
    border: none;
}

.sorted {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hs-property img {
    width: 93px;
    height: 58px;
    object-fit: cover;
    border-radius: 6px;
    margin-right: 10px;
}

.host-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hs-property {
    display: flex;
    align-items: center;
}

.hs-property p {
    margin: 0;
    font-size: 16px;
    font-weight: 400;
    color: black;
    width: 230px;
    margin-right: 4px;
}

.host-box.active {
    background: #FAFAFA;
}

.host-box {
    padding: 15px;
    border-radius: 10px;
    margin: 5px 0;
    background-color: #fff;
}

.hs-check input {
    background-color: #EDEDED;
    border-color: #EDEDED;
    margin-right: 10px;
    height: 26px;
    width: 26px;
}

.hs-alert {
    font-size: 22px;
    text-align: center;
}

.hl-sidebar {
    height: 100vh;
    border-right: 1px solid #E2E2E2;
    padding-right: 15px;
    position: sticky;
    top: 100px;
}

.hs-tb-head {
    display: flex;
    justify-content: space-between;
}

.hs-tb-head h2 {
    margin-bottom: 0px;
}

.hs-tb-actions a {
    color: black;
    font-size: 26px;
    margin-right: 18px;
}

.hs-line-tabs .nav-link {
    border: none;
    color: black;
    font-family: 'dubai-font';
    font-size: 18px;
    padding: 0 7px;
    font-weight: 500;
    height: 50px;
    position: relative;
    margin-right: 40px;
    color: #A3A3A3;
    transition: .3s ease-in-out;
}

.hs-line-tabs .nav-link::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -2px;
    left: 0;
    background: #000000;
    visibility: hidden;
    opacity: 0;
    transition: .3s ease-in-out;
}

.hs-line-tabs .nav-link:hover {
    color: var(--dr-shade-gray);
}

.hs-line-tabs .nav-link:hover::before {
    visibility: visible;
    opacity: 1;
}

.hs-line-tabs .nav-link.active::before {
    color: var(--dr-shade-gray);
    visibility: visible;
    opacity: 1;
}

.hs-tb-sc {
    padding: 30px 0px;
}


.hs-card {
    border: 1px solid #707070;
    border-radius: 10px;
    padding: 30px;
    position: relative;
}

.hs-card-head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    align-items: center;
}


a.hs-card-close {
    font-size: 30px;
    display: block;
    color: black;
}

.hs-card-content h3 {
    font-size: 24px;
    font-family: 'dubai-font';
    margin-bottom: 15px;
}

.hs-card-content p {
    font-size: 18px;
    color: #5F5F5F;
    font-family: 'dubai-font';
    margin-bottom: 20px;
    font-weight: 400;
}

.host-btn {
    border: 1px solid #707070;
    height: 47px;
    background: transparent;
    padding: 0px 20px;
    border-radius: 7px;
    /* text-transform: capitalize; */
    font-size: 21px;
    font-family: 'dubai-font';
    transition: 0.3s ease;
    color: black;
}

.host-btn-black {
    border: 1px solid #707070;
    height: 47px;
    background: black;
    padding: 0px 20px;
    border-radius: 7px;
    /* text-transform: capitalize; */
    font-size: 21px;
    font-family: 'dubai-font';
    transition: 0.3s ease;
    color: #ffff;

}

.host-btn:hover {
    background: black;
    color: #fff;
    transition: 0.3s ease;
}

.hs-card-lg {
    height: 400px;
}

.hs-cta {
    position: absolute;
    bottom: 30px;
}

.hs-card {
    margin-bottom: 25px;
}

.host-next-slide img {
    width: 100%;
    height: 177px;
    border-radius: 10px;
}

.hs-slide {
    padding: 5px;

}

.cont-hd {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.cont-hd p {
    font-size: 28px;
}

.cont-host p {
    font-size: 18px;
}

.cont-hd a {
    color: black;
    font-size: 19px;
    font-weight: 500;
}

.cont-hd h3 {
    font-size: 20px;
    margin-bottom: 0;
    font-family: 'dubai-font';
}

.cont-host p {
    font-size: 20px;
    font-weight: 300;
}

ul.amenties-list {
    -moz-column-count: 2;
    -moz-column-gap: 0;
    -webkit-column-count: 2;
    -webkit-column-gap: 0;
    column-count: 2;
    column-gap: 0;
    width: 600px;
    font-size: 20px;
    font-weight: 300;
    font-family: 'dubai-font';
}

a.host-show-more {
    color: black;
    font-size: 20px;
    font-family: 'dubai-font';
    font-weight: 300;
    text-decoration: underline;
    display: block;
    margin-top: 30px;
}

.cont-hd a i {
    position: relative;
    top: 4px;
}

/* .host-slide {
    display: flex;
    align-items: self-start;
} */
.host-slide-photos .slick-track {
    margin-left: 0;
}

.gray-tag {
    background: #EDEDED;
    padding: 2px 15px;
    font-size: 15px;
    border-radius: 5px;
    margin-left: 10px;
}

a.host-show-import {
    color: black;
    font-size: 20px;
    font-family: 'dubai-font';
    font-weight: 300;
    text-decoration: underline;
    margin-top: 30px;
}

a.host-show-import {
    color: black;
    font-size: 20px;
    font-family: 'dubai-font';
    font-weight: 300;
    text-decoration: underline;
    margin-top: 30px;
}

.host-exp img {
    margin-right: 15px;
    position: relative;
    top: -3px;
}

.host-exp {
    margin-bottom: 11px;
}


.host-radio input {
    width: 0;
    height: 0;
    visibility: hidden;
    position: absolute;
}

.host-radio {
    position: relative;
    margin-left: 10px;
}

.host-radio label {
    border: 1px solid;
    width: 30px;
    text-align: center;
    height: 30px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 36px;
    background: #ffff;
    cursor: pointer;
    transition: 0.3s ease;
}

.host-check {
    display: flex;
    align-items: center;
}

.host-radio input:checked+label {
    background: black;
    color: #ffff;
    border-color: #000;
}

.wp-50 {
    width: 50%;
}

.host-toggle {
    width: 55px !important;
    height: 32px;
}

.host-toggle {
    transition: 0.3s ease;
}

.form-switch .form-check-input {
    transition: 0.3s ease;
}

.form-switch .form-check-input:checked {
    background-color: black !important;
    transition: 0.3s ease;
}

.hs-tb-actions a:hover {
    color: #f5c33e;
    transition: 0.3s ease;
}

.co-host-box {
    text-align: center;
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e0e0e059;
    transition: 0.3s ease;
}

.co-host-box:hover {
    box-shadow: 3px 5px 9px 4px #00000012;
    transition: 0.3s ease;
}

.co-host-box {
    text-align: center;
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e0e0e059;
    transition: 0.3s ease;
}

.co-host-box:hover {
    box-shadow: 3px 5px 9px 4px #00000012;
    transition: 0.3s ease;
}

.co-img img {
    width: 93px;
    height: 93px;
    overflow: hidden;
    border: 1px solid #e5e5e582;
    border-radius: 71px;
}

.co-img {
    margin-bottom: 12px;
}

.co-content h4 {
    background: #E2E2E2;
    font-size: 11px;
    width: fit-content;
    margin: 0px auto;
    padding: 7px 10px;
    border-radius: 5px;
    margin-bottom: 12px;
    font-weight: 600;
    height: 26px;
}

.co-content h3 {
    font-size: 18px !important;
    font-family: 'dubai-font';
    font-weight: 400;
    margin-bottom: 0;
}

.co-content p {
    font-size: 14px;
    margin-bottom: 0;
    font-family: 'dubai-font';
}

.hs-alertt {
    width: 85px;
    height: 81px;
    background: #e07912;
    border-radius: 73px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payout-host {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    border: 1px solid #eeeeee;
    border-radius: 10px;
    width: 560px;
}

.hs-alertt {
    margin-right: 15px;
}

.payout-content h2 {
    font-size: 22px;
    font-family: 'dubai-font';
    margin-bottom: 2px;
}


select.host-form-control {
    display: block;
    width: 100%;
    padding: 10px 25px;
    color: var(--theme-middle);
    background-color: #fff;
    border: 1px solid var(--grey-one);
    border-radius: 3px;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    height: 75px;
    font-size: 22px;
    font-family: 'dubai-font';
    font-weight: 400;
}

.payouts {
    border: 1px solid #e2e2e2;
    font-size: 27px;
    padding: 40px;
    font-family: 'dubai-font';
    border-radius: 10px;
    font-weight: 400;
}

.payouts p {
    margin-bottom: 0;
}

section.transaction_history {
    padding: 100px 0px;
}

/* download add */
.download-app-top {
    box-shadow: 0px -3px 7px 0px #0000001a;
    background: #f5c33e;
    padding: 10px;
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 999;
}

.in-down {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.inner-down {
    display: flex;
    align-items: center;
}

.dap-content h3 {
    font-size: 18px;
    color: #ffff;
    margin-bottom: 0px;
}

.dap-icon img {
    height: 40px;
    width: 40px;
    background-color: #ffff;
    padding: 7px;
    border-radius: 6px;
}

.dap-closed {
    cursor: pointer;
}

.dap-closed i {
    font-size: 30px;
    color: #fff;
}

button.btn-download-app {
    height: 40px;
    padding: 0px 15px;
    background: #ffff;
    color: #000000e8;
    font-weight: 500;
    border: 1px solid #fff;
    border-radius: 6px;
    width: 110px;
}

.dap-content {
    margin: 10px;
}

.dap-down {
    margin-right: 20px;
    margin-left: 20px;
}

.dap-down img {
    width: 130px;
}

.dap-down a {
    padding: 0px 3px;
}

/* dropdown menu for host */

.navmenu.host-listings {
    top: 76px;
    width: auto;
}

.navmenu.host-listings ul li a {
    font-size: 20px;
    font-weight: 400;
    font-family: 'dubai-font';
    color: #000;
    padding: 5px 15px;
    display: block;
    border-radius: 7px;
    padding-right: 40px;
    text-align: left;
}

.navmenu.host-listings .nav-inner::before {
    display: none !important;
}

.navmenu.host-listings ul li {
    margin: auto !important;
}

.navmenu.host-listings ul li a::after {
    display: none;

}

.navmenu.host-listings ul li a:hover {
    background-color: gray;
}

.navmenu.host-listings ul li a:hover {
    background-color: #f7f7f7;
}

.btn-sc-calendar {
    width: 150px;
}

/* dropdown menu for host end*/

section.reminder-cards-sec,
.personalInfo {
    padding: 60px 0px;
}

.dr-box-foot a {
    border-radius: 0px 0px 8px 8px;
    transition: 0.3s ease;
}

.guest-counter span {
    cursor: pointer;
    touch-action: manipulation;
}

.create_profile,
.accountManage {
    padding: 60px 0px;
}

.getVerified {
    border: 1px solid #A3A3A3;
    border-radius: 10px;
    margin-top: 40px;
}

.gv-top h3 {
    font-family: 'dubai-font';
    font-size: 28px;
    margin-bottom: 20px;
}

p.gv-email {
    font-size: 22px;
    margin: 0;
}

p.gv-email img {
    width: 20px;
    margin-right: 6px;
}

.gv-top {
    border-bottom: 1px solid #a3a3a3;
    padding: 30px;
}

.gv-bottom {
    padding: 30px;
}

.crpro-inner {
    width: 500px;
    margin: 0px auto;
    padding: 140px 0px;
}

.host-btn-danger {
    border: 1px solid #F45E5E;
    height: 47px;
    background: #F45E5E;
    padding: 0px 20px;
    border-radius: 7px;
    text-transform: capitalize;
    font-size: 21px;
    font-family: 'dubai-font';
    transition: 0.3s ease;
    color: #fff;
}

.mn-box {
    padding: 30px;
    box-shadow: 3px 3px 9px 2px #0000001a;
    border-radius: 10px;
    transition: 0.3s ease;
    height: 220px;
}

.mn-box h2 {
    font-size: 22px;
    margin-bottom: 14px;
}

.mn-box p {
    font-size: 20px;
    margin-bottom: 0;
}

.mn-icon {
    margin-bottom: 15px;
}

.mn-box:hover {

    transform: translate(0px, -4px);
}

.mn-icon img {
    height: 40px;
}

.host-hd-sc p {
    font-size: 20px;
    font-weight: 300;
}

.host-hd-sc h1 {
    font-size: 40px;
    margin-bottom: 10px;
}

.mn-icon {
    margin-bottom: 20px;
}

.bcrm {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
}

.bcrm a {
    font-weight: 500;
}

.hs-icon-inner {
    margin-right: 6px;
    position: relative;
    top: 4px;
}

.hs-tb-log {

    padding: 60px 0px;
}

section.privacy_and_sharing {
    padding: 60px 0px;
}

section.global_preferences {
    padding: 60px 0px;
}

button.host-btn i {
    position: relative;
    top: 4px;
}

.table-host .table-striped>tbody>tr>td {
    vertical-align: middle;
}

.hs-view-edit {
    display: none;
}


.btn-round-black {
    width: 44px;
    height: 44px;
    font-size: 24px;
    border: 2px solid #000000;
    color: #000000;
    border-radius: 49px;
    padding: 3px;
    background: transparent;
    margin-left: 10px;
    transition: 0.3s ease;
}

.hs-ed-bx .form-control {
    /* width: 500px; */
    border-radius: 5px;
}

button.btn-round-black:hover {
    background: black;
    color: #fff;
    transition: 0.3s ease;
}

button.btn-loader-host {
    background: transparent;
    border: 2px solid black;
    transition: 0.3s ease;
    border-radius: 7px;
}

button.btn-loader-host img {
    width: 40px;
}

/* .dots {
    width: 40px;
    margin: 0px auto;
    height: 19.2px;
    background: radial-gradient(circle closest-side, #000000 90%, #0000) 0% 50%,
        radial-gradient(circle closest-side, #000000 90%, #0000) 50% 50%,
        radial-gradient(circle closest-side, #000000 90%, #0000) 100% 50%;
    background-size: calc(100%/3) 9.6px;
    background-repeat: no-repeat;
    animation: dots-7ar3yq 1.2s infinite linear;
} */

@keyframes dots-7ar3yq {
    20% {
        background-position: 0% 0%, 50% 50%, 100% 50%;
    }

    40% {
        background-position: 0% 100%, 50% 0%, 100% 50%;
    }

    60% {
        background-position: 0% 50%, 50% 100%, 100% 0%;
    }

    80% {
        background-position: 0% 50%, 50% 50%, 100% 100%;
    }
}

.btn-loader-host {
    display: none;
}



.hs-ls-group input {
    font-size: 30px !important;
}

.hs-ls-group {
    height: 60px;
    width: 50%;
}

.hs-view-edit {
    border: 1px solid #e2e2e2;
    border-radius: 10px;
}

.sv-cn {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #e2e2e2;
    padding: 20px 30px;
}

.hs-tp {
    padding: 30px;
}

.hs-ed-bx label {
    width: 500px;
}

.hs-tp .total-guest {
    width: 500px;
}


.hs-tp select {
    overflow: -moz-hidden-unscrollable;
    background: url(../icons/down-arrow.svg) no-repeat right white;
    background-position: 97% 50%;
}

label.hs-ls-it {
    display: flex;
    align-items: start;
    width: 600px;
}

label.hs-ls-it input {
    height: 20px;
    width: 20px;
    margin-right: 10px;
    margin-top: 4px;
}

.hs-ls-item h4 {
    font-size: 21px;
    font-family: 'dubai-font';
    font-weight: 400;
    margin-bottom: 0px;
}

.hs-ls-item h4 i {
    font-size: 18px;
    padding-right: 3px;
}

.hs-ls-item p {
    font-size: 15px;
    font-family: 'dubai-font';
    font-weight: 400;
}

.hs-tp .listing-category {
    width: 600px;
}



.host-sh-right {
    text-align: right;
}

.hs-search {
    width: 400px;
    position: relative;
    margin-right: 15px;
}

.hs-search i {
    position: absolute;
    top: 4px;
    left: 15px;
    font-size: 24px;
}

.hs-search input {
    border-radius: 43px;
    padding-left: 43px;
    background: #fbfbfb;
}

.host-search {
    display: flex;
    align-items: center;
    margin-bottom: 30px;

}

.hs-flt-popup>button {
    padding: 0px 25px;
    font-size: 17px;
    height: 45px;
    border-radius: 34px;
    border: 1px solid gray;
    background: transparent;
}

.hs-flt-popup {
    margin-right: 15px;
    position: relative;
}

.hs-flt-popup button i {
    font-size: 18px;
    position: relative;
    top: 4px;
    right: -10px;
}


.popup-hs {
    width: 370px;
    border-radius: 10px;
    background: #fff;
    position: absolute;
    top: 60px;
    z-index: 10;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: .3s;
    transition: .3s;
    padding: 15px;
    display: none;
    box-shadow: 2px 5px 10px 0px #0000000a;
    border: 1px solid #f0f0f0;
}




.popup-hs.opened {
    display: block;
    opacity: 1;
    visibility: visible;
}

.fl-inn {
    padding: 15px;
}

.popup-hs .sv-cn {
    padding: 18px 5px 5px 5px;
}

.fl-inn .total-guest {
    border-bottom: 0;
}

.hs-tp input[type="time"] {
    margin-right: 10px;
}

.morepotos {
    width: 200px;
    position: absolute;
    z-index: 99;
    right: -2px;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 21px;
    text-align: right;
    justify-content: end;
    background: linear-gradient(to right, rgb(255 255 255 / 0%), rgb(255 255 255 / 89%) 59%, rgb(255, 255, 255)) !important;
}

.morepotos a {
    color: black;
    font-size: 18px;
    font-weight: 500;
}

.morepotos a span {
    text-decoration: underline;
}

.host-next-slide {
    overflow: hidden;
    position: relative;
}

.bcrm a {
    color: black;
}

.cn-box {
    display: flex;
}

input.country-cd {
    width: 20% !important;
    margin-right: 10px;
}

.form-switch .form-check-input {
    cursor: pointer;
}

.hls-list ul li {
    border-bottom: 1px solid #f2f2f2;
}

.hls-list ul {
    height: 600px;
    overflow-y: auto;
    padding-right: 10px;
}

.hls-list ul::-webkit-scrollbar {
    width: 6px;
}

.hls-list ul::-webkit-scrollbar-thumb {
    background: #d8d8d8;
}

/* .dotss {
    width: 24px;
    height: 5.8px;
    background: radial-gradient(circle closest-side, #ffffff 90%, #0000) 0% 50%,
        radial-gradient(circle closest-side, #ffffff 90%, #0000) 50% 50%,
        radial-gradient(circle closest-side, #ffffff 90%, #0000) 100% 50%;
    background-size: calc(100%/3) 100%;
    background-repeat: no-repeat;
    animation: dots-zcf63l 1s infinite linear;
}

@keyframes dots-zcf63l {
    33% {
        background-size: calc(100%/3) 0%, calc(100%/3) 100%, calc(100%/3) 100%;
    }

    50% {
        background-size: calc(100%/3) 100%, calc(100%/3) 0%, calc(100%/3) 100%;
    }

    66% {
        background-size: calc(100%/3) 100%, calc(100%/3) 100%, calc(100%/3) 0%;
    }
} */

.dotss {
    margin: 0px auto;
}


.host-btn-black.hs-save {
    width: 100px;
    text-align: center;
    justify-content: center;
}


.host-profile-icn {
    border: 1px solid #d6d6d6;
}

img.hs-notification-btn {
    width: 36px !important;
    margin-right: -12px;
}

.hs-ntf-main {
    top: 30px !important;
}

.hs-error-filled {
    background: #ee404c;
    text-align: center;
    color: #fff;

}

.hs-error-filled p {
    color: #fff !important;
    text-transform: capitalize;
    margin: 0;
    padding: 3px;
}

/* .hs-view-edit{
    overflow: hidden;
} */

.host-next-slide img {
    object-fit: cover;
    object-position: center;

}

/* .host-slide {
    display: flex;
} */
.host-next-slide img {
    width: 320px;
    height: 177px;
    border-radius: 10px;
    float: left;
}


.morepotos i {
    position: relative;
    top: 4px;
}

.listed-hst ul {
    margin: 0;
}

.host-bx-up {
    width: 18.9%;
    margin: 5px;
}

label.upload__btn.host-btn-black span {
    position: relative;
    top: 8px;
}

/* web host journey dashboard end */
.guest-warning {
    color: #ff3535;
}

.dropzone.dz-clickable .add-more-images {
    display: none;
}

.dz-started .add-more-images {
    display: block !important;
}

.dropzone.dz-clickable {
    position: relative;
}

.add-more-images {
    z-index: 1;
    border-radius: 5px;
    color: #b2b2b2 !important;
    right: 6px;
    font-size: 64px;
    bottom: -20px;
    text-align: center;
    border-style: dashed !important;
    border: 2px solid rgba(0, 0, 0, .3);
    background: #fff;
    padding: 8px 12px;
    width: 120px;
    height: 120px;
    cursor: pointer !important;
    border-radius: 10px;
    position: relative;
}

.dropzone .dz-preview .dz-progress .dz-upload {
    background: #f5c33e !important;
}

a:hover {
    color: black !important;
}

.cont-host .map-view-full-location {
    height: 300px;
}

.product-price span:first-word {
    color: red !important;
}

/*** host listing detail page****/
.hostdash-total-guest .content {
    margin-right: 30px;
}

/* for print screen */
@media print {
    .container {
        max-width: 100% !important;
    }
}

.listing-category.adplc {
    justify-content: space-between;
    width: 100%;
    margin: 0px auto;
}

.mb-space {
    width: 100%;
}

.adplc .category {
    width: 48%;
}

.new-listing .content-head h2 {
    text-align: center;
}

#host-amenities .form-check {
    padding-left: 0;
}

#host-amenities .form-check .form-check-input {
    margin-left: 5px;
}

.new-listing.step-one .cont-listing {
    height: 90vh;
}

.download-app-container .col-md-5 {
    height: 100%;
}

.alert-modal img {
    width: 100px;
}




.text-right-dir {
    text-align: right;
}














/* web responsive */
@media (max-width:1680px) {
    section.home-bg {
        background-position: 0 -70px;
        height: 93vh
    }

    img.fl-2 {
        bottom: 140px
    }

    img.fl-7 {
        bottom: -1%
    }

    .banner-content {
        bottom: 150px
    }

    .nInbox .main-chat {
        height: calc(100vh - 160px);
    }

    .nInbox .cv-msg {
        height: 75%;
    }

    .nInbox .chat-field {
        height: 25%;
    }
}

@media (max-width:1600px) {
    .floated-img {
        height: 55vh
    }

    img.fl-2 {
        bottom: 0
    }

    img.fl-7 {
        bottom: -27%
    }

    .banner-content {
        bottom: 100px
    }
    .new-listing-rev{
        align-items: start;
    }
}

@media (max-width:1440px) {
    .expired-promo {
        left: -30px;
        top: 14px;

        width: 45%;
    }

    section.home-bg {
        background-position: 0 -50px
    }

    .floated-img {
        height: 50vh
    }

    .banner-content {
        bottom: 130px
    }

    .host-sc img.fl-9 {
        width: 32%;
        bottom: 50px
    }

    .host-sc img.fl-10 {
        left: 240px;
        width: 32%;
        bottom: -108px
    }

    .host-sc img.fl-11 {
        width: 35%;
        right: 190px
    }

    .host-sc img.fl-13 {
        right: -185px;
        top: 27px;
        width: 39%
    }

    .host-sc img.fl-12 {
        width: 43%;
        right: -110px;
        bottom: -87px
    }

    section.second-home {
        height: 465px;
    }

}

@media (max-width:1366px) {
    .gm-style .gm-style-iw-c {
        max-height: 380px !important;
    }

    .gm-style-iw-d {
        max-height: 380px !important;
    }

    .map-property-img {
        height: 185px;
    }

    footer .foot-nav {
        margin: 0;
    }

    footer .copy-right {
        padding: 15px 0px;
    }

    header .logo {
        width: 100px
    }

    .clndr-btn,
    .date-mark,
    .fs-para,
    .heade-date input,
    .listing-question p,
    .property .product .product-detail .product-content,
    .rcpt-btn,
    .reserv-calculate-pricing p,
    .reserv-check h6,
    .reserv-check p,
    .slide-btn,
    .title .product-rate,
    .user-detail p,
    header .search-reservation p {
        font-size: 14px
    }

    header .search-reservation li {
        padding: 0 10px;
        width: 26%
    }

    header .search-reservation li:nth-child(2) {
        width: 38%
    }

    .heade-date {
        line-height: 1.4
    }

    button.submit-header {
        width: 48px;
        height: 48px;
    }


    .pop-img img {
        height: 100px !important;
        width: 100px !important;
    }

    .popup.search-location-popup {
        width: 750px !important
    }

    /* .grey-btn,
  .theme-btn {
    height: 40px;
    padding: 0 25px
  } */

    .floated-img {
        height: 55vh
    }

    .loc-btn {
        padding: 8px 25px
    }

    img.fl-2 {
        bottom: -80px
    }

    img.fl-7 {
        bottom: -55%
    }

    img.fl-8 {
        top: 22%
    }

    .banner-content {
        bottom: 35px
    }

    .banner-content h1,
    .home-cont h1,
    .list-hd h1 {
        font-size: 35px
    }

    .banner-content button {
        padding: 10px 35px
    }

    .services .services-btn .filter-btn .inner-btn {
        padding: 8px 14px
    }

    .services .services-btn .filter-btn .inner-btn:first-child img {
        width: 10px
    }

    .form-control,
    .services .services-btn .discover-btn button {
        height: 40px
    }

    .property .product .product-detail .title h4 {
        font-size: 16px;
        flex: 0 0 70%;
        max-width: 70%
    }

    .property .product .product-detail .title .product-rate {
        font-size: 14px;
        flex: 0 0 30%;
        max-width: 30%
    }

    .list-status,
    .listing-category .category .category-details span,
    .listing-counter-main h5,
    .new-listing .right-side .add-lst-content p,
    .policy-content,
    .product-detail .product-price {
        font-size: 16px
    }

    #room-detail-map,
    .map-view {
        height: 450px;
        width: 100%
    }

    .property-detail .property-gallery .main-image {
        height: 360px
    }

    .property-detail .property-gallery .inner-image {
        height: calc(180px - 8px)
    }

    .ls-edits a img,
    .property-detail .head-content .wish-icon img {
        width: 16px
    }

    .property-detail .property-inner-detail .user .cust-btn {
        padding: 8px 10px;
        font-size: 14px
    }

    .property-detail .property-inner-detail .popup-user img,
    .property-detail .property-inner-detail .user img {
        width: 40px;
        height: 40px;
    }

    .property-gallery .popup-main-image,
    .upload-image-full {
        height: 220px
    }

    .property-gallery .popup-inner-image {
        height: calc(110px - 8px)
    }

    .host-sc img.fl-9 {
        left: 14px;
        bottom: 90px
    }

    .host-sc img.fl-10 {
        left: 147px;
        bottom: -30px
    }

    .host-sc img.fl-11 {
        right: 190px
    }

    .host-sc img.fl-13 {
        right: -135px;
        top: 27px
    }

    .host-sc img.fl-12 {
        right: -30px;
        bottom: -7px
    }

    .host-banner .content button {
        padding: 0 25px;
        font-size: 14px;
        height: 40px
    }

    .host-help .content1 button {
        padding: 0 25px;
        height: 40px
    }

    .listing-counter input,
    .new-listing .left-side .left-sec-content h1,
    .new-listing .right-side .add-lst-content h1 {
        font-size: 40px
    }



    .right-content {
        padding: 0 0px;
        /* height: calc(100% - 80px) */
    }

    .listing-footer,
    .upload-image-half {
        height: 80px
    }

    .listing-category .category .category-content {
        padding: 25px 20px
    }

    .listing-category .category .category-content img,
    .notification-title img {
        width: 30px
    }

    .listing-group {
        height: 80px;
        width: 60%;
        margin: 0 20px
    }

    .listing-currency {
        font-size: 25px
    }

    .listing-night {
        font-size: 20px;
        padding-right: 0
    }

    .listing-counter-main h4 {
        margin: 15px 0;
        font-size: 22px
    }

    .listing-counter .minus,
    .plus {
        width: auto
    }

    .p-100 {
        padding: 0 60px
    }

    .newlstng-total-guest .total-guest {
        padding: 15px 0 20px
    }

    .listing-upload-head {
        margin-bottom: 20px
    }

    .listing-upload-head h3,
    .policy-head {
        font-size: 24px
    }

    .listing-upload-head .upload-btn {
        padding: 10px 25px;
        font-size: 14px
    }

    .listing-map-popup,
    .total-credit-detail {
        width: 75%
    }

    .list-price,
    .loc-drop h6,
    .policy-mid-content,
    .title h4,
    ul.side-inner li a {
        font-size: 18px
    }

    .listing-add-field {
        padding: 0 20px 20px
    }

    .listing-form {
        padding: 0 35px 20px
    }

    .listing-form-main {
        padding: 8px 0
    }

    .listing-form .form-group,
    .listing-question .listing-ql {
        margin-bottom: 8px
    }

    .map-view-location {
        height: 150px !important
    }

    .listing-question {
        margin-bottom: 25px
    }

    .listing-question h3 {
        margin-bottom: 15px;
        font-size: 22px
    }

    .form-check-input[type=checkbox] {
        height: 20px;
        width: 20px
    }

    .ntf-main {
        width: 270px !important;
        transform: translate(-123px, 35px) !important
    }

    .ntf-inner {
        max-height: 150px
    }

    .ntf-main .ntf-image img {
        width: 70%
    }

    .left-arrow-title h6,
    .list-descrip,
    .list-rate,
    .ls-edits a,
    .pr-mini-detail p,
    .sh-1 {
        font-size: 15px
    }

    .photo-btn-bg {
        width: 3px;
        height: 3px;
        font-size: 5px
    }

    .upload-arrow {
        width: 22px !important
    }

    .cust-btn {
        text-transform: capitalize
    }

    .list-hd {
        padding: 20px 0
    }

    .your-side-list {
        height: 39vh !important
    }

    .side-list {
        /* height: 47vh */
    }

    .list-nav-pills {
        margin-bottom: 0 !important
    }

    .listing-link-btn {
        font-size: 16px !important;
        height: 34px !important
    }

    .notification-title,
    ul.side-inner li {
        margin-bottom: 15px
    }

    .wait-approval {
        font-size: 13px
    }

    .wait-approval i {
        width: 16px;
        height: 16px;
        font-size: 8px
    }

    .pr-img img {
        width: 40px;
        height: 40px
    }

    .pr-mini-detail h4 {
        font-size: 17px;
        margin-bottom: 2px
    }

    .tabs-sc .nav-pills .nav-link {
        padding: 0 20px;
        height: 35px;
        font-size: 16px
    }

    .rs-ls-img img {
        width: 100%
    }

    .slide-btn img {
        width: 9px
    }

    .clndr-btn img {
        margin-left: 8px;
        width: 17px;
        object-fit: fill
    }

    .reserv-detail-descrip .reserv-btn .host {
        font-size: 14px;
        padding: 8px
    }

    .reserv-detail-descrip .reserv-btn {
        margin: 18px 0
    }

    .rcpt-btn-main {
        margin-top: 5px
    }

    .reserv-detail-descrip ul {
        flex-flow: wrap
    }

    .reserv-pricing {
        padding: 10px
    }

    .reserv-check {
        padding: 12px 10px
    }

    .reserv-check img {
        width: 8px
    }

    .form-green-check-input:checked[type=checkbox] {
        background-size: 62%
    }

    .payment-detail {
        padding: 40px 0
    }

    .payment-detail .payment-title {
        margin: 0 0 30px auto
    }

    .card-bg {
        padding: 25px
    }

    /* .host-help {
        padding: 250px 0 0;
        margin-top: -260px
    } */

    .listing-link-btn {
        padding: 0 35px !important
    }

    .list-manage {
        padding: 0
    }

    .cm-img img {
        height: 155px
    }

    .listing-category {
        width: 100%
    }

    section.home-bg {
        background-position: 0 -100px
    }

    .notification {
        margin: 20px 0
    }

    .notification-main {
        height: calc(100vh - 320px)
    }

    .notification-user {
        /* padding: 15px 20px 15px 30px */
    }

    .Inbox {
        padding: 25px 0 30px
    }

    .img-bg {
        height: 90px;
    }

    .upload__img-close {
        width: 18px;
        height: 18px;
        font-size: 12px;
        top: 5px;
        right: 5px;
    }

    .promo-not-avail {
        height: 46vh;
        padding-top: 0px;
    }

    .no-promo-icon img {
        width: 140px;
    }

    .chat-head {
        height: 55px;
    }

    .nInbox .page-title {
        font-size: 30px;
        margin-bottom: 15px;
    }

    .nInbox .nchat-profile-list ul {
        height: 50vh;
    }

    .nInbox .main-chat {
        height: calc(100vh - 160px);
    }

    .cview-detail .host-property {
        height: calc(100vh - 203px);
    }

    .chat-list-inner .chat-img {
        width: 50px;
        height: 50px;
    }

    /* .cv-date p{
    margin-bottom: 8px;
    margin-top: 5px;
  } */
    .chat-property-prompt {
        font-size: 12px;
        padding: 7px 10px;
    }

    .cm-content {
        font-size: 14px;
    }

    .nInbox button.round-cta {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .nInbox .chat-field {
        padding: 5px 0px;
    }

    .ticket-dhead-attach {
        margin-bottom: 25px !important;
    }

    .ticket-detail .ticket-dhead {
        margin-bottom: 10px;
    }

    .ticket-inbox {
        overflow: auto;
    }

    .ticket-dfoot {
        margin-bottom: 10px !important;
    }

    .downApp-img img {
        width: 650px !important;
        left: -200px !important;
    }

    button.pd-promo-btn {
        width: 103px;
        height: 40px;
        top: 0px;
        right: 0px;
    }

    .bh-second-home {
        height: calc(100% - -4px);
        padding: 0;
    }

    .bh-second-home .bh-sh-head {
        font-size: 50px;
        margin-bottom: 15px;
    }

    .bh-second-home .bh-sh-content {
        font-size: 30px;
    }

    .bh-sh-btn button {
        width: 200px;
        font-size: 26px;
        height: 60px;
    }

    .hs-line-tabs .nav-link {
        margin-right: 15px;
    }
}

@media (max-width:1280px) {
    section.home-bg {
        height: 90vh
    }

    img.fl-2 {
        bottom: 10px
    }

    img.fl-7 {
        bottom: -20%
    }

    .banner-content {
        bottom: 75px
    }

    .popup {
        padding: 30px 40px
    }

    .popup.search-location-popup {
        width: 700px !important
    }

    .pop-img img {
        width: 105px !important;
        height: 100px !important
    }

    .popup input::placeholder {
        font-size: 12px
    }

    .popup input {
        padding: 6px 10px
    }

    .list-hd h1 {
        font-size: 30px
    }

    /* section.list {
     height: calc(100vh - 215px)
  } */

    .list-hd {
        padding: 10px 0
    }

    ul.side-inner li {
        margin-bottom: 10px
    }

    footer .copy-right {
        padding: 15px 0
    }

    .align-start {
        align-items: start !important
    }

    .download-app-container {
        height: 270px !important;
    }

    .downApp-img img {
        width: 500px !important;
        left: -140px !important;
    }

    .sh-dlogo {
        top: -88px;
        width: 90px;
    }

    .back-line {
        font-size: 45px
    }

    .downApp p {
        font-size: 41px;

    }

    #images-box .img-box {
        width: 50px;
        height: 40px;
    }

    .nInbox .chat-field input {
        height: 42px;
    }

    .nInbox .cv-msg {
        height: 69%;
    }
}

@media (max-width:1024px) {



    .theme-btn {
        height: 35px;
        padding: 0 20px;
        font-size: 14px
    }

    .footer-logo img,
    header .logo {
        width: 95px
    }

    header .search-reservation li {
        padding: 0 5px
    }

    header .search-reservation li:last-child {
        width: 15%
    }

    .custom-dropdown-menu li a,
    .heade-date input,
    .navmenu ul.text-list li a,
    header .search-reservation li span,
    header .search-reservation p {
        font-size: 12px
    }

    button.submit-header {
        width: 33px;
        height: 33px;
        font-size: 14px
    }

    header .nav .header-btn {
        font-size: 24px;
        padding-right: 10px
    }

    .notify {
        height: 7px;
        width: 7px;
        top: 2px
    }

    .notification-drop a img {
        width: 20px
    }

    .listing-link-btn,
    .user-name a {
        font-size: 14px !important
    }

    header .nav .nav-menu img {
        width: 12px
    }

    .ntf-main {
        width: 240px !important;
        transform: translate(-109px, 35px) !important
    }

    .ntf-inner {
        max-height: 137px
    }

    .ntf-header .total-ntf {
        width: 23px;
        height: 23px;
        left: 75%;
        font-size: 10px
    }

    header .nav .user .nav-menu {
        padding: 3px 10px 3px 4px
    }

    img.fl-7 {
        bottom: -25%
    }

    .product-category li img {
        width: 24px
    }

    .product-category li p {
        font-size: 13px;
        margin: 5px 0 0
    }

    .services .head h1 {
        font-size: 25px
    }

    .feature .bg .d img {
        width: 230px;
        top: -12px
    }

    .banner-content button {
        padding: 10px 25px;
        font-size: 14px
    }

    .feature-content h1,
    .host-help .content1 h1,
    .listing-counter input,
    .new-listing .left-side .left-sec-content h1,
    .new-listing .right-side .add-lst-content h1 {
        font-size: 35px
    }

    .feature-content p,
    .policy-head {
        font-size: 22px
    }

    .whatsapp-icon {
        bottom: 30px
    }

    .host-sc img.fl-9 {
        left: 0;
        width: 31%;
        bottom: 170px
    }

    .host-sc img.fl-10 {
        left: 137px;
        width: 30%;
        bottom: 65px
    }

    .host-sc img.fl-11 {
        width: 31%;
        right: 200px
    }

    .host-sc img.fl-13 {
        right: -35px;
        width: 35%
    }

    .host-sc img.fl-12 {
        width: 38%;
        right: 0;
        bottom: 100px
    }

    .home-cont h1,
    .list-hd h1 {
        font-size: 30px
    }

    .host-help .help-content li .content h5 {
        margin-bottom: 10px;
        font-size: 17px
    }

    content li img {
        margin-right: 30px;
        width: 25px
    }

    .black-btn a,
    .map-content,
    footer .foot-nav li a {
        font-size: 14px
    }

    footer .social-icon li a i {
        font-size: 24px
    }

    .host-help .help-content li img {
        margin-right: 30px;
        width: 28px
    }

    .navmenu ul.text-list li .tick-icon {
        top: 6px
    }

    .listing-category,
    .new-listing .left-side .cont-listing {
        width: 100%
    }


    .right-content {
        padding: 0 30px;
        height: calc(100% - 67px)
    }

    .listing-footer {
        height: 65px
    }

    .black-btn img {
        width: 7px;
        margin-right: 5px
    }

    /* .host-help {
        padding: 180px 0 0;
        margin-top: -330px
    } */

    .listing-form {
        padding: 0 20px 10px
    }

    .confirm-location {
        margin-top: 0
    }

    .inner-listing-form .form-group {
        margin-bottom: 5px
    }

    .inner-listing-form .form-control {
        height: 35px !important;
        padding: 8px 25px
    }

    .listing-map {
        height: calc(100vh - 65px)
    }

    .map-view-location {
        height: 130px !important
    }

    .form-control::placeholder {
        font-size: 14px
    }

    .list-price,
    .pr-mini-detail h4,
    .title h4,
    .total-guest .content h4 {
        font-size: 16px
    }

    .accordion-btn,
    .fs-20 {
        font-size: 18px
    }

    .listing-checkbox h4 {
        margin-bottom: 15px;
        font-size: 22px
    }

    .listing-place-name label {
        font-size: 25px;
        margin-bottom: 25px
    }

    .listing-place-name .listing-input {
        height: 80px
    }

    .fs-lg,
    .listing-counter-main h4 {
        font-size: 20px
    }

    .listing-counter .minus,
    .plus {
        width: 10%
    }

    .listing-group {
        height: 70px;
        width: 70%;
        margin: 0 15px
    }

    .chat-text,
    .pr-cont {
        width: 85%
    }

    .listing-currency {
        font-size: 23px
    }

    .listing-night {
        font-size: 21px
    }

    .pr-cont {
        font-size: 14px
    }

    .listing-counter-main h3 {
        margin-bottom: 20px
    }

    .listing-question h3 {
        margin-bottom: 10px;
        font-size: 20px
    }

    .chat-name p,
    .listing-question .listing-ql p,
    .overview p,
    .pr-mini-detail p,
    .pricing-inner form label,
    .property-detail .property-feature-list li,
    .shared-room-info label,
    ul.side-inner li a {
        font-size: 14px
    }

    .listing-position {
        padding: 0;
        height: calc(100% - 65px)
    }

    .form-check-input[type=checkbox] {
        height: 18px;
        width: 18px
    }

    .left-arrow-title {
        margin-bottom: 10px !important
    }

    .filter-check .filter-check-inner,
    .notification-title {
        margin-bottom: 10px
    }

    .shared-room-info .filter-check .filter-check-inner p {
        font-size: 18px;
        margin-bottom: 10px
    }

    .property-detail .property-gallery .main-image {
        height: 300px
    }

    .property-detail .property-gallery .inner-image {
        height: calc(150px - 8px)
    }

    .property-detail .property-inner-detail {
        flex-flow: wrap
    }

    .property-detail .property-inner-detail .property-description {
        flex: 0 0 100%;
        max-width: 100%
    }

    .property-detail .property-inner-detail .user {
        flex: 0 0 100%;
        max-width: 100%;
        justify-content: flex-start
    }

    .user-detail {
        margin: 0 35px
    }

    .property-pricing {
        padding: 15px
    }

    .check-icon img {
        width: 15px
    }

    .property-list {
        font-size: 14px !important;
        margin-top: 15px !important
    }

    .custom-subtotal {
        margin: 0 0 20px;
        max-height: 153px
    }

    .pricing-inner form .reserve {
        margin-bottom: 0
    }

    .gst {
        margin: 20px 0 0 !important
    }

    .pricing {
        margin-bottom: 15px !important
    }

    .pricing-inner .pricing span {
        font-size: 17px
    }

    ul.side-inner li a i {
        font-size: 21px;
        margin-right: 7px
    }

    .list-status {
        font-size: 15px
    }

    .list-hd {
        padding: 20px 0 0
    }

    .services .services-btn .filter-btn {
        justify-content: center;
        flex-flow: wrap
    }

    .services .services-btn .filter-btn .inner-btn {
        height: 35px !important;
        margin-bottom: 5px;
        padding: 8px 16px
    }

    .services .services-btn .filter-btn .inner-btn:first-child {
        margin-right: 15px
    }

    .services .services-btn .filter-btn .inner-btn:nth-child(2) {
        margin-right: 0
    }

    .services .services-btn .filter-btn .inner-btn:nth-child(3) {
        padding: 8px 20px;
        margin-bottom: 0
    }

    .listing-checkbox-wrapper.fsrch {
        width: 80px;
        height: 70px
    }

    .list-manage {
        padding: 10px 0 20px
    }

    section.list {
        /* height: calc(100vh - 202px) */
    }

    footer .footer-inner {
        padding-top: 20px
    }

    .notification-main {
        height: calc(100vh - 290px)
    }

    .chat-msg {
        padding: 0 20px;
        height: calc(100% - 53px)
    }

    .chat-lst {
        padding: 10px
    }

    .send-btn {
        width: 40px;
        height: 40px;
        font-size: 16px
    }

    .inbox-property-img {
        flex: 0 0 14%;
        height: 55px;
    }

    .full-width {
        flex: unset !important;
        width: 100%;
    }

    .cview-detail {
        display: none;
    }

    .sc-btn {
        display: none;
    }

    .chat-view-main {
        flex: 0 0 100%;
    }

    .chat-view-main {
        border-right: 1px solid #e9e9e9;
    }

    .ninbox-property {
        display: block;
    }

    .nInbox .page-title {
        font-size: 25px;
        margin-bottom: 5px;
    }

    .nInbox {
        height: 100%;
    }

    /* .nInbox .nchat-profile-list ul{
    height: 100%;
  }
  .nInbox .main-chat{
    height: calc(100vh - 170px);
  } */
    .sc-home {
        right: 60px;
    }
}

@media (max-width: 991px) {
    .listing-night {
        font-size: 15px;
        text-transform: capitalize;
    }

    .col-xs-6 {
        flex: 0 0 auto;
        width: 50%;
    }

    #chat-view .bd-right {
        border: none;
    }

    .md-bd-chat {
        border-left: 2px solid var(--grey-one);
        /* border-right: 1px solid var(--grey-one); */
        /* border-radius: 5px 5px 0 0; */
    }

    #chat-view .verified p {
        margin-bottom: 0;
        font-size: 12px;
        font-weight: 600;
    }

    #chat-view .verified p i {
        font-size: 14px;
        padding-right: 0;
    }

    .change-pro .pro-img {
        margin-bottom: 0;
    }

    .change-pro a {
        margin-bottom: 0;
        font-size: 14px;
    }

    .change-pro p {
        margin-bottom: 0;
        font-size: 14px;
    }

    #chat-view .verified {
        display: none;
    }

    .prof-in {
        padding: 10px;
        border-radius: 5px 5px 0 0;
        /* background: #dbdbdb; */
        position: relative;
        margin-bottom: 0 !important;
    }

    .main-chat {
        height: 55vh;
        position: relative;
        margin-top: 2px;
    }

    .chat-profile-list {
        min-height: inherit;
        max-height: inherit;
        height: 65vh;
    }

    .chat-field {
        padding: 13px 0 0;
    }

    #chat-view .rv-mb {
        flex-wrap: wrap-reverse;
    }

    .inbox-profile {
        text-align: start;
        display: flex;
        align-items: start;
    }

    .inbox-profile .pro-img img {
        height: 40px;
        width: 40px;
        margin-right: 10px;
    }

    .inbox-profile-content {
        flex: 1;
    }

    .inbox-profile-content a {
        font-size: 14px;
    }

    .inbox-profile-content p {
        font-size: 13px;
    }

    .inbox-property-img {
        flex: 0 0 13%;
    }

    .prof-in::before {
        content: "";
        bottom: 2px;
        width: 89%;
        height: 2px;
        left: 48%;
        transform: translateX(-50%);
        background-color: #dbdbdb;
        position: absolute;
        display: block;
    }

    .inbox-property-content a {
        font-size: 14px;
    }

    .inbox-property-content p {
        font-size: 13px;
    }

    .chat-msg {
        padding: 0px 20px 0 5px;
    }

    .i-property-name {
        visibility: visible;
        opacity: 1;
    }

    header .header-inner {
        position: relative;
    }

    .fl-on-mb {
        position: absolute;
        top: 13px;
        left: -12px;
    }

    .edit-sc-tb .tabs-sc {
        display: block !important;
    }

    .edit-sc-tb .nav-pills {
        flex-flow: wrap;
    }

    .edit-sc-tb .nav-link {
        padding: 10px 15px !important;
    }

    .edit-sc-tb .nav-pills .nav-link {
        justify-content: center;
    }

    .edit-sc-tb .tabs-sc {
        padding-top: 10px;
        margin-top: 10px;
    }

    .carrent-loc-btn {
        margin-bottom: 15px;
    }

    .photos-sc .cover img {
        height: 300px;
        object-fit: cover;
    }

    .edit-sc-tb .nav-pills {
        margin: 0 20px;
    }

    .tb-btn-sc {
        text-align: center;
        width: 25%;
        margin: 0 auto;
    }

    .tb-btn-sc input {
        width: 100%;
        height: 45px;
        font-size: 16px;
    }

    /* .side-smenu .side-smenu-inner li a{
    font-size: 17px;
  }
  .side-smenu .side-smenu-inner li a i{
    font-size: 20px;
    margin-right: 7px;
  } */
    .support-detail {
        width: 90%;
    }

    .nInbox .nchat-profile-list ul {
        height: calc(100vh - 106px);
    }

    .ticket-inbox .list-hd {
        padding: 15px 0;
    }
}

@media (max-width: 767px) {
    .chat-profile-list {
        min-height: inherit;
        overflow: overlay;
        max-height: inherit;
    }

    .inbox-main {
        position: relative;
        margin-bottom: 20px;
    }

    #chat-view {
        position: absolute;
        top: 0;
        background-color: #fff;
        left: -200%;
        width: 100%;
    }

    .floating-chat {
        left: 0 !important;
    }

    .sm-bd-chat {
        border-left: 1px solid var(--grey-one);
        border-right: 1px solid var(--grey-one);
        border-radius: 5px 5px 0 0;
    }

    .chat-field {
        padding: 13px 15px 0px;
    }

    .chat-cancel {
        display: block;
    }

    .prof-in {
        display: flex;
        justify-content: space-between;
        align-items: self-start;
    }

    .tb-btn-sc {
        width: 100%;
    }

    .side-smenu {
        border: none;
        position: fixed;
        bottom: -1px;
        height: 77px;
        width: 100%;
        left: 0;
        right: 0;
        box-shadow: 0 -2px 7px 3px #0000002e;
        padding-top: 2px;
        z-index: 99;
        margin: 0;
        border-radius: 10px 10px 0 0;
        background: #ffff;
    }

    .side-smenu .side-smenu-inner {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .side-smenu .side-smenu-inner li {
        margin-bottom: 0;
        margin-right: 10px;
        margin-left: 10px;
    }

    .side-smenu .side-smenu-inner li a {
        text-align: center;
        font-size: 12px;
        border: none;
        padding: 5px;
        width: 100px;
        height: 70px;
        border-radius: 10px;
        display: block;
    }

    .side-smenu .side-smenu-inner li a i {
        font-size: 22px;
        margin-right: 0;
        padding-bottom: 5px;
        display: block;
    }

    .side-smenu .side-smenu-inner li a span {
        display: block;
        line-height: 12px;
    }

    .support-detail {
        width: 100%;
    }

    .nInbox-inner {
        position: relative;
    }

    .chat-view-main {
        border-left: 1px solid #e9e9e9;
    }

    .nchat-profile-list .chat-head button {
        display: none;
    }

    .cv-mb-hide {
        display: block;
    }

    span.dt-time {
        float: none;
        display: block;
        margin-top: 2px;
    }

    .ticket-inbox {
        height: calc(100vh - 200px);
    }

    .ticket-dbody {
        height: 50vh;
    }
}

@media (max-width: 640px) {
    .edit-sc .edit-sc-title {
        font-size: 20px;
        margin-bottom: 0;
    }

    .edit-sc .tabs-sc {
        border-top: 1px solid #ede6e6;
    }

    .edit-sc .list-hd {
        text-align: left;
    }

    .edit-sc .slick-slider {
        margin-bottom: 10px !important;
    }

    .ticket-detail .ticket-dhead .ticket-head-content {
        font-size: 16px;
    }
}

@media (max-width: 575px) {
    .photos-sc .cover img {
        height: 200px !important;
    }
}

@media (max-width: 460px) {
    .edit-sc .edit-sc-title {
        font-size: 18px;
    }

    .edit-place-name {
        font-size: 16px;
    }

    .editlisting-map {
        height: 350px;
    }

    .head-content h4 {
        font-size: 18px;
        margin-bottom: 2px !important;
    }

    .head-content p {
        font-size: 14px;
    }

    .chat-property-prompt {
        align-items: flex-start;
    }

    .chat-property-prompt img {
        padding-top: 3px;
    }

    .chat-property-prompt p {
        margin: 0 4px 0 5px;
    }
}

@media (max-width: 414px) {
    .spacetype-sc .listing-group {
        height: 60px;
    }

    .spacetype-sc .listing-group span {
        width: 10%;
    }

    .upload__btn {
        font-size: 14px;
        width: auto;
        margin-left: auto;
        padding: 12px 13px !important;
    }
}

@media (max-width: 360px) {
    .price-tb .listing-currency {
        width: 40%;
    }

    .price-tb .listing-counter input {
        left: 30%;
    }

    .upload__btn {
        padding: 7px 16px !important;
    }

    .property-detail .property-inner-detail .property-description h5 {
        font-size: 18px;
    }

    .property-detail .property-inner-detail .property-description ul li {
        font-size: 14px;
    }
}

@media only screen and (min-width: 1000px) and (max-width: 1500px) {
    /* section.new-listing.translate {
        padding: 60px 0px;
    } */


    .cont-listing h2 {
        font-size: 38px;
    }

    .get-cont h3 {
        font-size: 24px;
    }

    .get-cont p {
        font-size: 15px;
    }

    .get-img img {
        width: 100px;
    }

    .lst-progress {
        margin-bottom: 14px;
    }

    label.category.w-100 {
        width: 70% !important;
    }

    .listing-category {
        justify-content: center;
    }

    .listing-group {
        height: 69px;
        width: 50%;
        margin: 0 20px;
    }

    .new-listing h2 {
        font-size: 25px;
    }

    .right-inner-side .mb-5 {
        margin-bottom: 30px !important;
    }

    /* .new-listing {
        padding: 40px 0px;
    } */

    .loc-map-sc {
        padding: 60px 0px;
    }

    .listing-map-inner {
        padding-top: 300px;
        padding-bottom: 100px;
    }

    section.new-listing-dropzone {
        padding: 120px 0px;
        overflow: auto;
    }

    .new-listing-cover {
        padding: 120px 0px;
    }

    /* .new-listing-rev {
        overflow: auto;
        padding: 120px 0px;
    } */

    /* section.new-listing-rev .row {
        padding-top: 150px;
    } */

    section.host-detail-sec.sc-100 {
        padding: 30px 0px;
    }

    .hl-sidebar h1 {
        font-size: 30px;
    }

    .tb-search {
        position: relative;
        margin-bottom: 20px !important;
    }

    .hls-list ul {
        height: 350px;
    }

    .sc-1360 {
        overflow: auto;
        padding: 100px 0px;
    }


}

/* responsive for host dashboard */
@media only screen and (min-width: 1000px) and (max-width: 1600px) {
    .hs-property img {
        width: 60px;
    }

    .col-md-3.hs-col {
        width: 30%;
    }

    .col-md-9.hs-col-sc {
        width: 70%;
    }

    .hs-card-content p {
        font-size: 16px;
    }

    .hs-card-content h3 {
        font-size: 18px;
    }

    span.hs-card-icon img {
        width: 30px;
    }

    .host-btn {
        height: 45px;
        font-size: 18px;

    }

    .cont-hd h3 {
        font-size: 18px;
    }

    .cont-host p {
        font-size: 18px;
    }

    .cont-hd a {
        color: black;
        font-size: 16px;
        font-weight: 500;
    }

    .hs-property p {
        font-size: 14px;
        width: 200px;
    }

    .host-next-slide img {
        width: 100%;
        height: 140px;
        border-radius: 10px;
        object-fit: cover;
    }

    .hs-tb-sc h3 {
        font-size: 18px;
    }

    a.host-show-import {
        font-size: 18px;
    }

    .host-exp img {
        width: 18px;
    }

    .cont-host p {
        margin: 0;
    }

    .host-detail-sec {
        padding: 60px 0px;
    }

    .hs-tb-head h2 {
        font-size: 24px;
    }

    .hs-tb-actions a {
        font-size: 18px;
    }

    .host-toggle {
        width: 55px !important;
        height: 28px !important;
    }

    .req-url a h5 i {
        position: relative;
        top: 5px;
    }

    .rc-card-content p {
        font-size: 16px;
    }



}


@media only screen and (min-width: 1199px) and (max-width: 1300px) {
    .new-listing {
        overflow: auto !important;
        padding-top: 60px;
    }
}


@media (max-width:700px) {

    .step-one-row {
        flex-wrap: wrap-reverse;
    }

    .step-one-row .right-side {
        height: auto !important;
    }

    .step-one-row .left-side {
        height: auto !important;
    }

    .listing-footer {
        position: fixed;
        width: 100%;
        bottom: 0;
    }

    .listing-footer {
        height: 80px !important;
    }



    .new-listing {
        display: flex;
        align-items: center;
        /* align-items: self-start; */
        padding: 100px 0px;
    }

    .listing-category .category {
        cursor: pointer;
        width: 100%;
        margin-bottom: 20px;
    }

    .new-listing .right-side {
        height: auto;
    }

    .nav-listing {
        padding: 10px 2px;
    }

    label.category.w-100 {
        width: 100% !important;
    }

    .listing-map-inner {
        padding: 0px 15px;
    }

    .map-listing {
        height: 100vh;
        padding: 60px 0px;
        display: flex;
        align-items: center;
    }

    .listing-checkbox-wrapper {
        width: 45%;
    }

    .drop-zone {
        padding: 15px;
    }

    .col-md-3.allimages {
        width: 50%;
    }

    .check-innout {
        width: 70%;
        margin: 0px auto;
    }

    .check-innout .form-control {
        margin-bottom: 14px;
        text-align: center;
    }

    .check-innout label {
        text-align: center;
        display: block;
    }

    section.new-listing-question .p-0 {
        padding: 0px 15px !important;
    }

    /* .new-listing-rev {
        padding: 60px 0px;
    } */

    .review-prod {
        margin-bottom: 30px;
    }

    .what-next {
        padding-bottom: 100px;
    }

    .cont-listing h2 {
        font-size: 30px;
    }

    .get-cont h3 {
        font-size: 20px;
    }

    .get-cont p {
        font-size: 14px;
    }

    .new-listing {
        overflow: auto !important;
    }

    .black-btn img {
        position: inherit;
    }

    button.btn-save {
        border: 2px solid #707070;
        height: 35px;
        width: 110px;
        border-radius: 10px;
        background: transparent;
        font-size: 13px;
        background-color: #fff;
        font-weight: 500;
        color: black;
    }

    .step-img img {
        width: 80%;
    }

    .step-img {
        text-align: center;
    }

    .cont-listing h1 {
        font-size: 44px;
    }

    .mb-space {
        margin-bottom: 30px;
    }

    .loc-map-sc {
        padding: 100px 0px;
        justify-content: flex-start;
        height: auto;
    }

    .pac-container.pac-logo {
        transform: inherit !important;
        width: 250px !important;
    }

    .loc-lst img {
        width: 17px;
        position: absolute;
        top: 18px;
        left: 19px;
    }

    .loc-lst input {
        padding-left: 45px;

    }

    .black-btn img {
        position: relative;
        left: 1px !important;
        top: 4px;
    }

    section.new-listing-dropzone {
        padding: 120px 0px;
        overflow-y: auto;
        height: auto;
        overflow-x: hidden;
    }

    .listing-place-name {
        margin-bottom: 50px;
    }
/*
    .new-listing-rev {
        padding: 60px 0px;
        display: flex;
        align-items: flex-start;
    } */

    /* section.new-listing-rev .row {
        padding-top: 60px;
        padding-bottom: 60px;
    } */

    /*small modal behaviour bottom*/
    .modal.modal-dr-bottom-upload {
        padding: 0 !important;
    }

    .modal-dr-bottom-upload .modal-dailog-upload {
        margin: 0 !important;
        height: 100%;
    }

    .modal-dr-bottom-upload .modal-content {
        margin: auto 0 -1px 0;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }

    .modal-dailog-upload {
        position: fixed !important;
        bottom: 0 !important;
        left: 0% !important;
        right: 0% !important;
        margin-bottom: 0 !important;
    }

    .modal.fade .modal-dailog-upload {
        transform: translateY(100%);
        transition: transform 0.4s ease-in-out;
    }

    .modal.show .modal-dailog-upload {
        transform: translateY(0px);
        width: 100%;
    }

    .setcover .img-bg img {
        object-fit: cover;
        height: 150px;
        width: 100%;
    }

    .setcover .img-bg {
        height: 150px;
    }

    .drop-img button.promo-drop-btn {
        font-size: 22px;
        height: 36px;
        width: 36px;
    }

    .listing-checkbox {
        margin-right: 0;
    }


}


.slt-arrow {
    overflow: -moz-hidden-unscrollable;
    background: url(../icons/down-arrow.svg) 100% 50% no-repeat #fff !important;
    -webkit-appearance: none;
}

.day-reserv-box {
    margin-bottom: 15px;
}

.views span {
    line-height: normal;
}

.loadergif.btn-ld img {
    width: auto;
}

.ref-link {
    width: 130px;
    justify-content: center;
}



.block-user {
    margin-bottom: 19px;
    margin-top: 10px;
}


.block-user .form-check .form-check-input {
    position: relative;
    bottom: 3px;
}


.block-info p {
    margin: 0;
    color: #ff2222;
    font-weight: 400;
    font-size: 14px;
}

.block-info {
    background: #fbdede;
    padding: 15px 20px;
    border-radius: 7px;
    margin-top: 10px;
    width: fit-content;
}

/* Co host revert */
/* a.rev-lnk {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.rev-link-inner {
    display: flex;
    align-items: center;
}
 */
.rev-link-inner i {
    margin-right: 7px;
    display: none;
    color: #23bc4c;
}

a.rev-lnk i {
    display: none;
}

.warning-chat {

    padding: 5px 10px;
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
    border-radius: 5px;

}

.warning-chat i {
    position: relative;
    top: 3px;
}

/* co host sec */
div#phone-container {
    width: 100%;
}

.phone-number input {
    width: 100%;
}

.co-host-form .phone-number {
    border-radius: 6px !important;
}

.or-sc {
    text-align: center;
}

.or-sc:before {
    content: '';
    height: 1px;
    width: 46%;
    background: #0000002e;
    position: relative;
    z-index: 99999999;
    display: block;
}

.or-sc:after {
    content: '';
    height: 1px;
    width: 46%;
    background: #0000002e;
    position: relative;
    z-index: 99999999;
    display: block;
}

.or-sc {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 5px;
    font-weight: 500;
}

.co-host-form .form-control {
    border-radius: 5px;
    height: 55px;
}

.arrow-box {
    border: 1px solid #0000002e;
    padding: 20px;
    border-radius: 10px;
}

.arrow-box .row {
    align-items: center;
}

.arrow-box p {
    margin: 0;
}

.arrow-box h3 {
    margin-bottom: 5px;
    font-size: 22px;
}

a.arrow-box-href:hover .arrow-box {
    border-color: black;
    transition: 0.3s ease;
}

a.arrow-box-href {
    display: block;
    transition: 0.3s ease;
    margin-bottom: 15px;
}

.co-profile {
    display: flex;
    align-items: center;
}

.co-profile h5 {
    margin-left: 12px;
    margin-bottom: 0;
}

.invite-cohost {
    border: 1px solid #0000002e;
    padding: 20px;
    border-radius: 10px;
}

.co-assign-prop img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
}

.co-assign-prop {
    margin-top: 20px;
    margin-bottom: 20px;
}

.fw-600 {
    font-weight: 600;
}

.co-profile img {
    border-radius: 37px;
    width: 55px;
    height: 55px;
    overflow: hidden;
    object-fit: cover;
}

label.co-host {
    border: 2px solid #00000029;
    width: 100%;
    padding: 15px;
    border-radius: 10px;
    position: relative;
}

.co-host-content {
    display: flex;
    align-items: center;
}

.co-host-details {
    margin: 0px 15px;
}

label.co-host input {
    position: absolute;
    right: 14px;
    width: 20px;
    height: 20px;
    -webkit-filter: saturate(0%) grayscale(100%) brightness(69%) contrast(300%);
}

.co-host input[type=radio]:checked+label.co-host {
    background: black;
    color: #ffff;
    border-color: #000;
}

.co-host-content img {
    width: 53px;
}

.co-host-details h4 {
    margin: 0;
}

.fw-500 {
    font-weight: 500 !important;
}

section.prim-host {
    padding: 60px 0px;
}

.prim-user img {
    height: 80px;
    width: 80px;
    border: 1px solid;
    border-radius: 44px;
    object-fit: cover;
    margin-bottom: 12px;
}

.prim-badge {
    color: #23bc4c;
    font-weight: 500;
    margin-bottom: 8px;
}

.prim-user h3 {
    margin-bottom: 5px;
}

.prim-user h3 {
    margin-bottom: 5px;
}

.prime-connect a {
    font-weight: 400;
    font-size: 17px;
    margin-right: 10px;
    text-decoration: underline;
    color: gray;
}

button#swapBtn:hover {
    color: #fff !important;
}

.co-icn {
    width: 70px;
    height: 70px;
    border: 1px solid #a3a3a3;
    border-style: dashed;
    border-radius: 43px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 35px;
    color: #a3a3a3;
    margin: 0px auto;
    margin-bottom: 16px;
}

.ad-co h5 {
    color: #a3a3a3;
    font-weight: 400;
}

.co-host-box.add-ch {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

div#blockWordRender {
    overflow: hidden;
}


.theme-btn-light {
    background-color: #d1d1d1;
    border: unset;
    border-radius: 7px;
    line-height: 1.6;
    font-weight: 400;
    text-transform: capitalize;
    color: black !important;
    padding: 8px 30px;
    font-size: 18px;
    height: 47px;
}

img.rm-coupon {
    margin-bottom: 25px;
}

.dlt-cohost {
    position: absolute;
    right: 5px;
    top: 5px;
    background: transparent;
    border: 1px solid transparent;
    width: 40px;
    height: 40px;
    font-size: 23px;
    border-radius: 36px;
    padding: 3px;
    color: #ee404c;
    transition: 0.3s ease;
    opacity: 0;
    visibility: none;
}

.co-host-box {
    position: relative;
}

.co-host-box:hover .dlt-cohost {
    transition: 0.3s ease;
    opacity: 1;
    visibility: visible;

}

.dlt-cohost:hover {
    background: #e7e7e7;
}


.single-check {
    position: relative;
    margin-top: 10px;
    margin-bottom: 10px;
}

.single-check img {
    width: 18px;
}

.rules-am {
    background: #e3e3e3;
    border-radius: 25px;
    padding: 2px 14px;
    font-size: 16px;
    font-weight: 600;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

a.dlt-rule {
    position: relative;
    top: 2px;
    right: -6px;
}

button.removeAmenityBtn {
    border: none;
    background: transparent;
    padding: 0;
    font-size: 19px;
    height: 27px;
    font-weight: 600;
}

.drag-container>.dnd-sortable-item {
    position: relative;
    display: block;
}

#c3.drag-container>.dnd-sortable-item {
    cursor: move;
}

.dnd-sortable-item.dnd-sortable-closest {
    border-color: #00fa00;
    z-index: 5;
}
.dnd-sortable-item.dnd-sortable-dragged .upload__img-box {
    color: #fa0020;
    z-index: 10;
    opacity: 0.7;
    box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.4);
}

.dnd-sortable-placeholder {
    border: 1px dashed black;
    background: #efecec;
    opacity: 0.5;
}

.drag-container {
    position: relative;
}
.dnd-sortable-handle{
margin-bottom: 20px;
}
.dnd-sortable-handle .upload__img-box {
    z-index: -1;
    margin: 0;
}


.img-drg {
    height: 100%;
    width: 100%;
    background-size: cover;
    background-position: center;
}
ul.cs-amt {
    display: flex;
    flex-wrap: wrap;
}

ul.cs-amt li {
    display: flex !important;
    flex: none !important;
    background: #e7e7e7;
    border-radius: 32px;
    width: fit-content;
    margin-right: 6px;
    padding: 6px 12px;
    align-items: center !important;
    color: black !important;
}

ul.cs-amt li i {
    margin-right: 7px;
}
ul.cs-amt li p {
    position: relative;
    top: 1px;
}

.check-bx input {
    position: absolute;
    height: 0;
    width: 0;
}

.check-bx input {
    position: absolute;
    height: 0;
    width: 0;
    visibility: hidden;
}

label.category.check-bx {
    position: relative;
}


.landing-banner {

    height: 200px;
    background-position: center 26%;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: -1px 4px 7px 1px #00000026;
    background-size: cover;
}

.landing-banner h1 {
    color: #ffff;
    font-size: 52px !important;
    text-shadow: -2px 3px #00000059;
    position: relative;
    z-index: 2;
    font-weight: 600 !important;
}

.landing-banner p {
    color: #ffff;
}

.landing-banner:before {
    content: '';
    width: 100%;
    position: absolute;
    height: 100%;
    background: #0000005e;
    z-index: 1;
}

.check-bx .category-content {
    opacity: 0.6;
    filter: grayscale(1);
}
.check-bx input:checked ~ .category-content {
    opacity: 1;
    filter: none;
    border-color: #999999;
}
.right-content.align-start.cstm-wdt {
    display: block;
    padding-top: 20%;
}
.vu-form-control
{
    height: 45px !important ;
}

.bootstrap-datetimepicker-widget {
    z-index: 9999;
}


/* ilm-e-yakeen */
label.ilq-chk {
    border: 1px solid #dedede;
    width: 100%;
    border-radius: 7px;
    padding: 10px;
}
label.ilq-chk .form-check-input[type=radio] {
    margin-top: 2px;
}
.hide-inp{
    display: none;
}
.bootstrap-select>.dropdown-toggle:after {
    margin-left: -17px !important;
}

/* select all promo */
.cstm-slt-pick .btn-group.btn-group-sm {
    display: flex;
    margin-bottom: 10px;
}

/* tabby ui */
.btn-tabby {
    border: 1px solid #e9ecef;
    background: transparent;
    border-radius: 7px;
    padding: 12px;
    display: block;
    width: 100%;
    text-align: center;
}

.btn-tabby span {
    margin-right: 10px;
}


.btn-tabby:hover {
    background: linear-gradient(270deg, #00eba9 0%, #6bf 47.92%, #9450ff 100%);
    color: #fff;
}

.tb-how {
    text-align: left;
    margin-top: 25px;
}


.tb-how p {
    margin-bottom: 17px;
    display: flex;
}

span.tb-num {
    background: #e6e5e9;
    display: block;
    width: 25px;
    height: 25px;
    /* float: left; */
    text-align: center;
    color: black;
    border-radius: 20px;
    margin-right: 8px;
    font-weight: 500;
    font-size: 13px;
    padding: 4px;
}
.tb-card img {
    height: 30px;
    width: auto !important;
    margin-right: 10px;
}

.tb-card {
    text-align: right;
    padding-top: 20px;
}

h1.tb-hd {
    background: linear-gradient(270deg, #00eba9 0%, #6bf 47.92%, #9450ff 100%);
    -webkit-background-clip: text;
    background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
    text-transform: capitalize;
}

.tb-dt {margin-right: 15px;margin-left: 15px;position: relative;width: 25%;}

.tb-d {
    display: flex;
    margin-top: 20px;
    justify-content: space-around;
}

.tb-circle img {
    height: 41px;
    width: auto !important;
}

.tb-circle::after {
    content: '';
    width: 100%;
    height: 1px;
    background: #ffad3f;
    position: absolute;
    top: 20px;
}
.tb-circle.ls-child::after {
    display: none;
}
.tb-dt p {
    font-size: 12px;
    margin-top: 9px;
    line-height: 14px;
}

.tb-dt p b {
    color: #171717;
}
.add-ament .host-btn-black {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff !important;
}

.grecaptcha-badge {
    visibility: hidden;
}
.wallet-money {
    text-align: center;
    padding: 10px;
    background: #f5c33e;
}

button.theme-btn-white {
    height: 44px;
    font-size: 15px;
    border: unset;
    border-radius: 7px;
    line-height: 2.6;
    font-weight: 500;
    text-transform: capitalize;
    background-color: #ffff;
    width: 100%;
    margin-bottom: 10px;
    padding: 0px;

}

.walet-bg {
    background: #f5c33e;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 32px;
    margin-left: 110px;
    margin-right: 110px;
}
h6.transaction-detail {
    font-size: 20px;
    color: black;
    font-weight: 400;
}

.wl-value .transaction-detail {
    font-size: 20px;
}
.wl-bal {
    color: black;
    font-weight: 500;
    font-size: 40px !important;
}
.wl-value {
    padding-top: 14px;
}

.pay-sl-content img {
    width: 45px;
    /* margin-left: 12px; */
}
.input-withlable:first-child .pay-sl-content img{
    width: 70px;
}
label.pay-sec-label input#ch-card-pay {
    height: 25px;
    width: 20px;
    filter: grayscale(1);
}
#ch-apple-pay{
    height: 25px;
    width: 20px;
    filter: grayscale(1);
}
#ch-mada-pay{
    height: 25px;
    width: 20px;
    filter: grayscale(1);
}

label.pay-sec-label {
    display: flex;
}

.gst-rating {
    display: flex;
    padding: 12px 0px;
}

.gst-rating .reserv-input-lvl {
    width: 100%;
}

.cstm-slt2 span.select2-selection.select2-selection--single{
    display: block;
    width: 100%;
    padding: 8px 15px;
    font-size: 14px;
    color: var(--theme-middle);
    background-color: #fff;
    border: 1px solid var(--grey-one);
    border-radius: 17px;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    height: 44px;
    margin-bottom: 10px;
}
.cstm-slt2 .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
}



/* review modal */

.total-review {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
}
.rv-img img {
    height: 85px;
}

.rv-cnt {
    font-size: 56px;
    font-weight: 600;
    margin-bottom: 15px;
    color:#222222;
}

.revv-cont {
    text-align: center;
    margin-bottom: 20px;
}


.revv-pr {
    display: flex;
    align-items: center;
}

span.rv-pr-bar {
    display: block;
    width: 100%;
}

span.rv-num {
    display: block;
    margin-right: 10px;
    font-weight: 400;
}

span.rv-pr-bar .progress {
    height: 6px;
    border-radius: 7px;
}

span.rv-pr-bar .progress-bar {
    background: #212529;
}
.review-count-sc {
    padding-left: 20px;
    padding-right: 20px;
}

.ament-rev ul li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #c7c8c9;
    margin-bottom: 0;
    padding: 10px 0px;
}
.amn-rv-left {
    display: flex;
}

span.amn-img img {
    width: 25px;
}

.amn-rv-left {
    display: flex;
}

span.amn-img img {
    width: 20px;
}

span.amn-img {
    margin-right: 10px;
}

span.amn-cont,.amn-count {
    font-size: 15px;
    font-weight: 500;
}

.rev-points {
    padding: 15px 0px;
}

select.rev-slt {
    display: block;
    width: 100%;
    padding: 10px 25px;
    font-size: 14px;
    color: var(--theme-middle);
    background-color: #fff;
    border: 1px solid var(--grey-one);
    border-radius: 17px;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    height: 44px;
}
.review-view-sc {
    padding: 15px;
}

.rev-search {
    position: relative;
    margin-top: 10px;
}

.rev-search i {
    position: absolute;
    top: 6px;
    left: 14px;
    font-size: 22px;
}

.rev-search input.form-control {
    padding-left: 50px;
}

.rev-box {
    padding: 15px;
    border: 1px solid #eaeaea;
    border-radius: 10px;
    position: relative;
    transition: 0.3s ease;
}

h6.rev-date.rev-dt {
    position: absolute;
    right: 16px;
    top: 8px;
    font-size: 11px;
}
.rev-views-ppl {
    height: 600px;
    overflow-y: auto;
    padding-right: 6px;
    margin-top: 14px;
    padding-bottom: 10px;
    padding-top: 10px;
}

.rev-box:hover {
    transform: translate(0px, -1px);
    box-shadow: 1px 1px 1px 1px #0000000d;
    border-color: black;
}


a#clear i {
    position: initial;
}

a#clear {
    position: absolute;
    right: 17px;
    top: 7px;
    font-size: 21px;
    height: 30px;
    text-align: center;
    display: flex;
    align-items: center;
    background: white;
    width: 30px;
    justify-content: center;
}



/* copy calnedar url */
.copy-url-bx {
    position: relative;
}

.copy-url-bx input {
    width: 100%;
}

.copy-url-bx button {
    width: 76px;
    position: absolute;
    right: 5px;
    height: 34px;
    color: #ffffff;
    background: #222222;
    border: 1px solid #222222;
    padding: 3px;
    font-size: 1rem;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
    top: 3px;
}


/* import calendar listing page */
.imp-cal{
    text-align: center;
}
button.btn-import-cl {
    width: 50%;
    border: 2px solid #020202;
    height: 60px;
    border-radius: 7px;
    background: transparent;
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0px auto;
    transition: 0.3s ease;
}

button.btn-import-cl i {
    margin-right: 10px;
    font-size: 25px;
}
button.btn-import-cl:hover {
    background: black;
    color: #ffff;
}
.imp-img img {
    width: 100%;
}
.exp-cop{
    position: relative;
}
.exp-cop i {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 19px;
}
.home-lang-btn:hover{
    color: #fff;
}
.nav-header.nav-scrolled-fixed a.menubtn.header-btn.home-lang-btn:hover{
    color: #000;
}
.daterangepicker td.in-range, .dropdown-item.active, .dropdown-item:active{
    background-color: #fbe7b2;
}
.daterangepicker td.available:hover, .daterangepicker th.available:hover{
    background-color: #fbe7b2;
}
td.active.end-date.in-range.available{
    background-color: var(--theme-primary);
}
.daterangepicker td.active, .daterangepicker td.active:hover{
    background-color: var(--theme-primary);
}
.start-date.active{
    background-color: var(--theme-primary) !important;
}
.drp-buttons .btn:disabled{
    background-color: #fbe7b2;
    border-color: #fbe7b2;
}
.daterangepicker td.off.ends.available{
    color: #000;
}
.daterangepicker td.off.ends.in-range.available{
    background-color: #fbe7b2;
}
.daterangepicker td.off.ends.active.end-date.in-range.available{
    background-color: var(--theme-primary);
}
