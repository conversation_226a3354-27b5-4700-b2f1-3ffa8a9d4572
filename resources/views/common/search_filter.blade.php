<?php
use Carbon\Carbon;
$lang = Session::get('language');
$checkin_session = Session::get('header_checkin');
$checkout_session = Session::get('header_checkout');

$adult_guest_session = Session::get('adult_guest_session') == '' ? 1 : Session::get('adult_guest_session');
$child_guest_session = Session::get('child_guest_session') == '' ? 0 : Session::get('child_guest_session');
$guest = $child_guest_session + $adult_guest_session;
$cities = app('CITIES');
$default_city = app('DEFAULT_CITY');

use App\Models\Settings;
use App\Models\Properties;
?>
@push('css')
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css">
@endpush
@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            $('.daterangepicker').addClass('for-filter');
        });
    </script>
@endpush

<div class="filter-cont">
    <form action="{{ url('search') }}" id="filter-search-form">
        <div class="filter-box">
            <ul class="search-reservation">
                <li class="search-location">
                    <span class="text-left loadskull">{{ customTrans('header.where_to_go') }}</span>
                    <div class="head-miniheight search-location-inner loadskull">
                        <div class="input-field on-show" data-target="#popup1">
                            {{-- <p class="" >
                                    {{ isset($location) ? (strlen($location) > 5 ? substr($location, 0, 14) . '...' : $location) : trans('messages.header.select_location') }}
                                </p> --}}
                            <input type="hidden" class="onsearch on-show" name="location" id="front-search-field"
                                autocomplete="off" value="{{ $location ?? $default_city }}"
                                placeholder="Select location">

                            <select class="selectpicker search-filter-select show-tick" data-live-search="true"
                                data-size="8" data-live-search-placeholder="Search Location" data-search-icon="fas fa-search" id="city_select">
                                @if (isset($location))
                                    @foreach ($cities as $city)
                                        <option value="{{ $city->name }}"
                                            {{ $location == $city->name ? 'Selected' : '' }}>
                                            {{ $lang == 'ar' ? $city->name_ar : $city->name }}</option>
                                    @endforeach
                                @else
                                    @foreach ($cities as $city)
                                        <option value="{{ $city->name }}"
                                            {{ $default_city == $city->name ? 'Selected' : '' }}>
                                            {{ $lang == 'ar' ? $city->name_ar : $city->name }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>

                </li>

                @php

                    $checkin = isset($checkin_session) ? carbonDate($checkin_session) : Carbon::now()->format('m-d-Y');
                    $checkout = isset($checkout_session) ? carbonDate($checkout_session) : Carbon::now()->addDays()->format('m-d-Y');
                    // $checkout = adDaysinDate($checkin, $checkout, 1); //if checkin and checkout same then add days(3rd parameter) in checkout
                @endphp

                <li class="date ">
                    <span class="loadskull">{{ customTrans('header.check_in') }} /
                        {{ customTrans('header.check_out') }}</span>
                    <div class="heade-date head-miniheight">
                        <div class="input-field on-show loadskull">
                            <input type="text" name="headerdaterange" placeholder="Date"
                                class="date-modal dt-md heade-date" value="{{ $checkin . ' - ' . $checkout }}"
                                readonly="true" />
                        </div>
                    </div>
                    <input type="hidden" name="checkin" id="startDate" value="{{ $checkin }}">
                    <input type="hidden" name="checkout" id="endDate" value="{{ $checkout }}">
                </li>
                <li class="guest" style="display:none">
                    <span class="loadskull">{{ customTrans('header.guest') }}</span>

                    <div class="loadskull">
                        <div class="input-field-gst on-show loadskull" data-target="">
                            <div class="no-gst head-miniheight ">
                                <p class="no-guest">{{ customTrans('header.number_of_guests') }}</p>
                                <p>{{ $guest ?? '' }}</p>
                            </div>
                        </div>
                    </div>

                    </li>
                    <li class="filter-bt ">
                        <button id="search-filter-button" class="submit-header loadskull"><i class="ri-search-line"></i></button>
                    </li>
                </ul>
                {{-- <div id="popup1" class="dialog popup-main popup1">
                    <div class="popup search-location-popup">
                        <div class="row">
                            <div class="col-xl-5 col-lg-5 col-sm-5">
                                <input type="text" name="location" id="front-search-field" autocomplete="off"
                                    value="{{ $location ?? '' }}"
                                    placeholder="{{ customTrans('home.where_want_to_go') }}">
                            </div>
                            <div class="col-xl-12 col-lg-12 col-sm-12">
                                <h3>{{ customTrans('home.popular_location') }}</h3>
                                <div class="main ">
                                    @forelse ($popular_cities as $city)
                                        <div class="inner-main pop-img popularcity loc"
                                            data-cityname="{{ $city->name }}">
                                            <a>
                                                <div>
                                                    <img loading="lazy" src="{{ $city->image_url }}"
                                                        height="auto" width="auto"
                                                        alt="{{ $city->name }}">
                                                    <p>{{ app()->getLocale() == 'ar' ? $city->name_ar : $city->name }}
                                                    </p>
                                                </div>
                                            </a>
                                        </div>
                                    @empty
                                        <p>No location for now </p>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>
                </div> --}}

            <div id="popup2" class="dialog popup-main popup2">
                <div class="popup guest-popup">
                    <div class="total-guest">
                        <div class="content">
                            <h4>{{ customTrans('header.adult') }}</h4>
                            <p>{{ customTrans('header.more_than_13_y') }}</p>
                        </div>
                        <div class="guest-counter">
                            <span class="minus"><img src="{{ asset('icons/minus.svg') }}" height="auto"
                                    width="auto" loading="lazy" alt="minus icon"></span>
                            <input type="number" class="clickdisabled" name="adult_guest" id="adult_guest"
                                value="{{ $adult_guest_session ?? 1 }}"readonly />
                            <span class="plus" data-limit="100"><img src="{{ asset('icons/plus.svg') }}"
                                    height="auto" width="auto" loading="lazy" alt="plus icon"></span>
                        </div>
                    </div>

                <div class="total-guest">

                    <div class="content">
                        <h4>{{ customTrans('header.children') }}</h4>
                        <p>{{ customTrans('header.less_than_13_y') }}</p>
                    </div>
                    <div class="guest-counter">
                        <span class="minus"><img src="{{ asset('icons/minus.svg') }}" height="auto" width="auto"
                                loading="lazy" alt="minus icon"></span>
                        <input type="number" class="clickdisabled" name="child_guest" id="child_guest"
                            value="{{ $child_guest_session ?? 0 }}" readonly />
                        <span class="plus" data-limit="100"><img src="{{ asset('icons/plus.svg') }}" loading="lazy"
                                height="auto" width="auto" alt="plus icon"></span>
                    </div>
                    <input type="hidden" name="guest" id='front-search-guests' value="{{ $guest ?? 1 }}">
                </div>
            </div>

            </div>
        </div>
</div>
</form>
</div>

