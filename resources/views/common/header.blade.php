<!--================ Header Menu Area start =================-->
<?php
$lang = Session::get('language');
$checkin_session = Session::get('header_checkin');
$checkout_session = Session::get('header_checkout');

$adult_guest_session = Session::get('adult_guest_session') == '' ? 1 : Session::get('adult_guest_session');
$child_guest_session = Session::get('child_guest_session') == '' ? 0 : Session::get('child_guest_session');
$guest = $child_guest_session + $adult_guest_session;
$segments = Request::segment(1) . Request::segment(3);
$routeName = Route::currentRouteName();
$group = explode('.', $routeName);
// dd($routeName);
use App\Models\Settings;
use App\Models\Properties;

if (auth()->check()) {
    $unread_noti = auth()->user()->notifications()->take(5)->get()->map(
        fn($noti) => (object) [
            'message' => $noti->data['messages'][app()->getLocale()] ?? $noti->data['messages']['en'],
            'read_at' => $noti->read_at,
            'link' => $noti->data['link'],
            'created_at' => $noti->created_at->timezone(auth()->user()->timezone ?? config('app.timezone'))->diffForHumans(),
        ],
    );
}
?>
@if (request()->route()->getName() == 'home' || request()->route()->getName() == 'search' || request()->route()->getName() == 'lp.riyadh')
    <div class="download-app-top">
        <div class="container">
            <div class="in-down">
                <div class="inner-down">
                    <div class="dap-icon">
                        <img src="{{ asset('logo/logo-add.png') }}" alt="">
                    </div>
                    <div class="dap-content">
                        <h3>{{ customTrans('header.downloadapp') }}</h3>
                    </div>
                </div>
                <div class="inner-down">
                    <div class="dap-down">

                        {{-- @if (strstr(strtolower($_SERVER['HTTP_USER_AGENT']), 'iphone') || strstr(strtolower($_SERVER['HTTP_USER_AGENT']), 'ipad')) --}}
                        <!-- iOS download link -->
                        <a href="https://apps.apple.com/us/app/darent/id1661536049" class="desktop_sticky_bar" target="_blank">
                            <img src="{{ asset('images/app-store2.png') }}" alt="">
                        </a>
                        {{-- @else --}}
                        <!-- Android download link -->
                        <a href="https://play.google.com/store/apps/details?id=com.darent" class="desktop_sticky_bar" target="_blank">
                            <img src="{{ asset('images/googleplay.png') }}" alt="">
                        </a>
                        {{-- @endif --}}




                    </div>
                    <div class="dap-closed">
                        <i class="ri-close-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif



{{-- <input type="hidden" id="front_date_format_type" value="{{ Session::get('front_date_format_type') }}"> --}}


@if (request()->route()->getName() == 'home' || request()->route()->getName() == 'search' || request()->route()->getName() == 'lp.riyadh')
    <div class="download-app-top">
        <div class="container">
            <div class="in-down">
                <div class="inner-down">

                    <div class="dap-icon">
                        <img src="{{ asset('logo/logo-add.png') }}" alt="">
                    </div>
                    <div class="dap-content">
                        <h3>{{ customTrans('header.downloadapp') }}</h3>
                    </div>
                </div>
                <div class="inner-down">
                    <div class="dap-down">
                        <a href="https://apps.apple.com/us/app/darent/id1661536049" class="desktop_sticky_bar" target="_blank">
                            <img src="{{ asset('images/app-store2.png') }}" alt="">
                        </a>
                        <a href="https://play.google.com/store/apps/details?id=com.darent" class="desktop_sticky_bar" target="_blank">
                            <img src="{{ asset('images/googleplay.png') }}" alt="">
                        </a>
                    </div>
                    <div class="dap-closed">
                        <i class="ri-close-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif


@if (request()->route()->uri == 'listing/{id}/{step}' ||
        request()->route()->uri == '{lang?}/property/darStays' ||
        request()->route()->uri == 'property/addplace')
    <style>
        body.sp-ontop {
            margin-top: 0px !important;
        }
    </style>
    <header id="headerforPDF" class="nav-header nav-fixed nav-listing {{ request()->route()->uri() == '{lang?}/property/darStays' ? 'property-landing-header' : '' }}">
        <div class="container">
            <div class="header-inner">
                <div class="row align-items-center justify-content-between">


                    <div class="col-6">
                        <div class="text-left logo-main-listing">
                            <a href="{{ route('home') }}" class="wht-logo">
                                <img src="{{ asset('logo/logo.svg') }}" height="auto" width="auto" loading="lazy"
                                    class="logo" alt="logo">
                            </a>
                        </div>

                    </div>

                    <div class="col-xl-3 col-lg-3 col-6 text-right">
                        {{-- @php
                            dd(($segments))
                        @endphp --}}
                        
                        @if ($segments == 'listingphotos')
                            <a
                                href="{{ route('listingwithsteps', ['id' => $result->id, 'step' => 'photosUploading', 'purpose' => 'saveAndExit']) }}"><button
                                    class="btn-save">{{ customTrans('listing.save_exit') }}</button></a>
                        @elseif($segments == 'listingsetCover')
                            <a
                                href="{{ route('listingwithsteps', ['id' => $result->id, 'step' => 'setCover', 'purpose' => 'saveAndExit']) }}">
                                <button class="btn-save">{{ customTrans('listing.save_exit') }}</button></a>
                        @elseif($segments == 'listingphotosUploading')
                            <a href="{{ route('managehost.host_listings') }}"><button
                                    class="btn-save">{{ customTrans('listing.save_exit') }}</button></a>
                        @elseif(request()->route()->uri() == '{lang?}/property/darStays')
                            <div class="landing-page-dropdown d-none d-md-block">
                                <a class="menubtn header-btn loadskull cs-h home-lang-btn"
                                    data-menu="home-lang-menu">{{ customTrans('header.' . Str::lower(Session::get('language'))) }}/{{ customTrans('host_dashboard.sar') }}
                                    <i class="ri-arrow-down-s-line"></i>
                                </a>
                                <div class="navmenu" data-menu="home-lang-menu">
                                    <div class="nav-inner">
                                        <ul class="text-list cust-dropdown-menu">
                                            @foreach ($language as $key => $value)
                                                <li>
                                                    <a class="language_footer {{ Session::get('language') == $key ? 't' : '' }}"
                                                        data-lang="{{ $key }}">
                                                        <div class="tick-icon">
                                                            @if ($key == Session::get('language'))
                                                                <img height="auto" width="auto"
                                                                    src="{{ asset('icons/select.svg') }}"
                                                                    loading="lazy" alt="tick-icon">
                                                            @endif
                                                        </div>
                                                        {{ $value }}
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                        <ul class="text-list cust-dropdown-menu">
                                            @foreach ($currencies as $key => $value)
                                                <li>

                                                    <a href="javascript:void(0)"
                                                        class="currency {{ Session::get('currency') == $value->code ? 'currency-active' : '' }}"
                                                        id="currency_selector" data-curr="{{ $value->code }}">
                                                        <div class="tick-icon">
                                                            @if ($value->code == Session::get('currency'))
                                                                <img height="auto" width="auto"
                                                                    src="{{ asset('icons/select.svg') }}"
                                                                    alt="tick icon">
                                                            @endif
                                                        </div>
                                                        {{ $value->code }}
                                                        <!-- {-- {!! $value->org_symbol !!} --} -->
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        @else
                            <button class="btn-save"
                                id="saveExitButton">{{ customTrans('listing.save_exit') }}</button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </header>
@elseif(request()->route()->getName() == 'home')
    <header id="headerforPDF" class="nav-header nav-fixed">
        <div class="container">
            <div class="header-inner">
                <div class="row align-items-center fl-wr">

                    <div class="col-xl-5 col-lg-5 ">
                        <div class="nav">
                            <div class="currancy-dropdown ">
                                {{-- <a class="menubtn header-btn trans-bt loadskull cs-h">{{ strtoupper(Session::get('language')) }}/{{ customTrans('host_dashboard.sar') }} --}}
                                <a class="menubtn header-btn trans-bt loadskull cs-h home-lang-btn"
                                    data-menu="home-lang-menu">{{ customTrans('header.' . Str::lower(Session::get('language'))) }}/{{ customTrans('host_dashboard.sar') }}
                                    <i class="ri-arrow-down-s-line"></i>
                                </a>
                                <div class="navmenu" data-menu="home-lang-menu">
                                    <div class="nav-inner">
                                        <ul class="text-list cust-dropdown-menu">
                                            @foreach ($language as $key => $value)
                                                <li>
                                                    <a class="language_footer {{ Session::get('language') == $key ? 't' : '' }}"
                                                        data-lang="{{ $key }}">
                                                        <div class="tick-icon">
                                                            @if ($key == Session::get('language'))
                                                                <img height="auto" width="auto"
                                                                    src="{{ asset('icons/select.svg') }}"
                                                                    loading="lazy" alt="tick-icon">
                                                            @endif
                                                        </div>
                                                        {{ $value }}
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                        <ul class="text-list cust-dropdown-menu">
                                            @foreach ($currencies as $key => $value)
                                                <li>

                                                    <a href="javascript:void(0)"
                                                        class="currency {{ Session::get('currency') == $value->code ? 'currency-active' : '' }}"
                                                        id="currency_selector" data-curr="{{ $value->code }}">
                                                        <div class="tick-icon">
                                                            @if ($value->code == Session::get('currency'))
                                                                <img height="auto" width="auto"
                                                                    src="{{ asset('icons/select.svg') }}"
                                                                    alt="tick icon">
                                                            @endif
                                                        </div>
                                                        {{ $value->code }}
                                                        <!-- {-- {!! $value->org_symbol !!} --} -->
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            {{-- <a href="{{ route('becomehost') }}" class=" ">
                                <button
                                    class="host header-btn trans-bt loadskull cs-h">{{ customTrans('footer.become_a_host') }}</button></a> --}}

                        </div>
                    </div>
                    <div class="col-2 fl-on-mb ">
                        <div class="text-center logo-main">
                            <a href="{{ route('home') }}" class="wht-logo">
                                <img src="{{ asset('logo/logo.svg') }}" height="auto" width="auto"
                                    loading="lazy" class="logo" alt="logo">
                            </a>
                        </div>

                    </div>

                    <div class="col-xl-5 col-lg-5 ">
                        <div class="nav profile-nav">

                            @auth
                                @if (Session::get('user_mode') == 'user')
                                    <div class="wishlist-main ">
                                        <a href="{{ route('wishlists.listing') }}"
                                            class="trans-bt">{{ customTrans('header.wishlist') }}</a>
                                    </div>
                                @endif
                                <div class="notification-drop">
                                    <a href="#" class="dropdown-toggle" type="button" id="ntf-dropdown"
                                        data-bs-toggle="dropdown" aria-expanded="false" onclick="readAll(event)"
                                        {{ auth()->user()->unreadNotifications()->count() > 0 ? "data-read='1'" : '' }}>
                                        @if (auth()->user()->unreadNotifications()->count() > 0)
                                            <div class="notify"></div>
                                        @endif
                                        <img height="auto" width="auto" src="{{ asset('icons/bell.svg') }}"
                                            alt="bell icon">
                                    </a>
                                    <div class="dropdown-menu ntf-main" aria-labelledby="ntf-dropdown">
                                        <div class="ntf-header">
                                            <h6 class="mb-0">{{ customTrans('notification.notification') }}</h6>
                                            <span class="total-ntf">{{ auth()->user()->notifications()->count() }}</span>
                                        </div>
                                        <ul class="ntf-inner" id="notifications"
                                            {{ auth()->user()->notifications()->count() < 1 ? 'data-list="fasle"' : '' }}>
                                            @forelse ($unread_noti as $notification)
                                                <li>
                                                    <a href="{{ $notification->link }}" class="dropdown-item ntf-item">
                                                        <div class="ntf-image">
                                                            <img src="{{ asset('icons/user.svg') }}" alt="">
                                                        </div>
                                                        <div class="ntf-content">
                                                            <div class="sender-msg">
                                                                <p class="mb-0">
                                                                    {{ $notification->message }}
                                                                    {{ $notification->created_at }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </li>
                                            @empty
                                                <div class="no-ntf">
                                                    <div>
                                                        <img height="auto" width="auto"
                                                            src="{{ asset('icons/ntf-bel.svg') }}" alt="bell">
                                                        <p>No notifications</p>
                                                    </div>
                                                </div>
                                            @endforelse
                                        </ul>
                                        @if (auth()->user()->notifications()->count() > 5)
                                            <div class="ntf-see">
                                                <a
                                                    href="{{ route('user.notifications') }}">{{ customTrans('general.see_all') }}</a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endauth

                            @auth
                                <div class="user dropdown">
                                    <button class="nav-menu dropdown-toggle" type="button" id="dropdownMenuButton1"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                        <img height="auto" width="auto" loading="lazy"
                                            src="{{ asset(auth()->user()->profile_src ?? 'icons/user.svg') }}"
                                            class="user-profile rounded-circle">
                                        {{ auth()->user()->full_name }}
                                    </button>
                                    <ul class="dropdown-menu custom-dropdown-menu pr-list"
                                        aria-labelledby="dropdownMenuButton1">




                                        @if (Session::get('user_mode') == 'user')
                                            <li>
                                                <a class="dropdown-item" href="{{ route('user.profile.view') }}"><i
                                                        class="ri-account-box-line"></i>
                                                    {{ customTrans('header.account') }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" id="updateUserMode" data-usermode="host"><i
                                                        class="ri-switch-fill mb-al-icon"></i>{{ customTrans('account_mobile.switch_to_hosting') }}
                                                </a>
                                            </li>
                                        @else
                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('managehost.accountManage') }}"><i
                                                        class="ri-account-box-line"></i>
                                                    {{ auth()->user()->full_name }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" id="updateUserMode" data-usermode="user"><i
                                                        class="ri-switch-fill mb-al-icon"></i>
                                                    {{ customTrans('account_mobile.switch_to_travelling') }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ route('managehost.day') }}"><i
                                                        class="ri-list-settings-line"></i>
                                                    {{ customTrans('header.managelist') }}</a>
                                            </li>
                                        @endif
                                        <li>
                                            <a class="dropdown-item" href="{{ route('user.notifications') }}"><i
                                                    class="ri-notification-line"></i>
                                                {{ customTrans('notification.notification') }}</a>
                                        </li>
                                        @if (Session::get('user_mode') == 'host')
                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('properties.chat.view', ['type' => 'host']) }}"><i
                                                        class="ri-mail-unread-line"></i>
                                                    {{ customTrans('header.inbox') }}</a>
                                            </li>
                                        @else
                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('properties.chat.view', ['type' => 'guest']) }}"><i
                                                        class="ri-mail-unread-line"></i>
                                                    {{ customTrans('header.inbox') }}</a>
                                            </li>
                                        @endif
                                        @if (Session::get('user_mode') == 'user')
                                            <li>
                                                <a class="dropdown-item" href="{{ route('wishlists.listing') }}"><i
                                                        class="ri-heart-3-line"></i>
                                                    {{ customTrans('wishlist.wishlist') }}</a>
                                            </li>
                                            {{-- <li>
                                                <a class="dropdown-item" href="{{ route('guest_reservation') }}"><i
                                                        class="ri-heart-3-line"></i>
                                                    Guest New Reservation</a>
                                            </li> --}}
                                        @endif
                                        @if (auth()->user()->is_elm_verified)
                                            <li>
                                                <a class="dropdown-item" href="{{ route('guest_reservation') }}">
                                                    <i class="ri-plane-line"></i>
                                                    {{ customTrans('reservation.reservation') }}</a>
                                            </li>
                                        @else
                                            <li>
                                                <a class="dropdown-item" href="{{ route('ilm_yaqeen') }}">
                                                    <i class="ri-plane-line"></i>
                                                    {{ customTrans('reservation.reservation') }}</a>
                                                </a>
                                            </li>
                                        @endif
                                        <li>
                                            <a class="dropdown-item" href="{{ route('user.wallet') }}"><i
                                                    class="ri-wallet-3-line"></i>
                                                {{ customTrans('sidenav.wallet') }}</a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ route('tickets.ticket_list') }}"><i
                                                    class="ri-ticket-2-line"></i>
                                                {{ customTrans('listing.mytickets') }}</a>
                                        </li>
                                        @if (Session::get('user_mode') == 'host')
                                            {{-- <li>
                                                <a class="dropdown-item" href="{{ route('yourlisting') }}"><i
                                                        class="ri-list-check-2"></i>
                                                    {{ customTrans('listing.listing_management') }}</a>
                                            </li> --}}
                                            {{-- @if (strtolower(env('APP_ENV')) != 'prod') --}}
                                            <li>
                                                <a class="dropdown-item" href="{{ route('pages.promotions') }}"><i
                                                        class="ri-megaphone-line"><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="16" height="16">
                                                            <path
                                                                d="M9 17C9 17 16 18 19 21H20C20.5523 21 21 20.5523 21 20V13.937C21.8626 13.715 22.5 12.9319 22.5 12C22.5 11.0681 21.8626 10.285 21 10.063V4C21 3.44772 20.5523 3 20 3H19C16 6 9 7 9 7H5C3.89543 7 3 7.89543 3 9V15C3 16.1046 3.89543 17 5 17H6L7 22H9V17ZM11 8.6612C11.6833 8.5146 12.5275 8.31193 13.4393 8.04373C15.1175 7.55014 17.25 6.77262 19 5.57458V18.4254C17.25 17.2274 15.1175 16.4499 13.4393 15.9563C12.5275 15.6881 11.6833 15.4854 11 15.3388V8.6612ZM5 9H9V15H5V9Z">
                                                            </path>
                                                        </svg></i> {{ customTrans('promocode.promotions') }}</a>
                                            </li>



                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('pages.transaction_details') }}"><i
                                                        class="ri-exchange-dollar-line"></i>
                                                    {{ customTrans('account_transaction.transaction') }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ route('userreview') }}"><i
                                                        class="ri-star-half-line"></i>
                                                    {{ customTrans('sidenav.reviews') }}</a>
                                            </li>
                                        @endif
                                        <li>
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal"
                                                data-bs-target="#logout-modal"><i class="ri-shut-down-line"></i>
                                                {{ customTrans('header.logout') }}</a>
                                        </li>


                                    </ul>
                                </div>
                            @else
                                <div class="log-btn">


                                    <div>
                                        <a class="signup-in-btn loadskull" href="javascript:void(0)"
                                            data-bs-toggle="modal" {{-- data-bs-target="#staticBackdrop">{{ customTrans('header.getstarted') }}</a> --}}
                                            data-bs-target="#staticBackdrop">{{ customTrans('header.getstarted') }}</a>
                                    </div>
                                </div>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
@elseif($group[0] == 'managehost' || (isset($type) && $type == 'host'))
    <style>
        body.sp-ontop {
            margin-top: 0px !important;
        }
    </style>
    <header id="headerforPDF" class="nav-header nav-listing dubai-ff host-header">
        <div class="container-host">
            <div class="header-inner">
                <div class="row align-items-center justify-content-between">
                    <div class="col-2 fl-on-mb ">
                        <div class="text-left logo-main-listing">
                            <a href="{{ route('home') }}" class="wht-logo">
                                <img src="{{ asset('logo/logo.svg') }}" height="auto" width="auto"
                                    loading="lazy" class="logo" alt="logo">
                            </a>
                        </div>

                    </div>
                    @if ($routeName == 'managehost.guest.review.create')
                    @else
                        <div class="col-xl-5 col-lg-6">
                            <ul class="host-navbar">
                                <li>
                                    <a href="{{ route('managehost.day') }}"
                                        class="{{ request()->routeIs('managehost.day') ? 'host-active' : '' }}">
                                        {{ customTrans('host_dashboard.today') }}
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('properties.chat.view', ['type' => 'host']) }}"
                                        class="{{ request()->routeIs('properties.chat.view', ['type' => 'host']) ? 'host-active' : '' }} hs-incoming-mail">
                                        {{ customTrans('host_dashboard.messages') }}

                                        {{-- <div class="hs-incoming-mail-notify">
                                            <span>10</span>
                                        </div> --}}
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('managehost.host_calendar') }}"
                                        class="{{ request()->routeIs('managehost.host_calendar') ? 'host-active' : '' }}">
                                        {{ customTrans('host_dashboard.calendar') }}
                                    </a>
                                </li>
                                {{-- <li>
                                    <a href="#">
                                        Visions And Ideas
                                    </a>
                                </li> --}}
                                <li>
                                    <a class="menubtn host-list"
                                        data-menu="host-menu">{{ customTrans('host_dashboard.list') }} <i
                                            class="ri-arrow-down-s-line"></i></a>
                                    <div class="navmenu host-listings" data-menu="host-menu">
                                        <div class="nav-inner">
                                            <ul>
                                                <li><a
                                                        href="{{ route('managehost.host_listings') }}">{{ customTrans('host_dashboard.listing') }}</a>
                                                </li>
                                                <li><a
                                                        href="{{ route('managehost.all_reservation') }}">{{ customTrans('host_dashboard.reservation') }}</a>
                                                </li>
                                                <li><a
                                                        href="{{ route('starthosting') }}">{{ customTrans('host_dashboard.create_a_new_listing') }}</a>
                                                </li>
                                                {{-- <li><a href="">Transaction history</a></li> --}}

                                            </ul>
                                        </div>
                                    </div>

                                </li>
                            </ul>
                        </div>
                    @endif

                    <div class="col-xl-3 col-lg-4 text-right">
                        @if ($routeName == 'managehost.guest.review.create')
                            <button class="btn-save">{{ customTrans('listing.save_exit') }}</button>
                        @else
                            <div class="nav profile-nav">

                                @auth
                                    <div class="nav host-lang-btn">
                                        <div class="currancy-dropdown me-0 ms-0">
                                            {{-- <a class="menubtn header-btn trans-bt loadskull cs-h">{{ strtoupper(Session::get('language')) }}/{{ customTrans('host_dashboard.sar') }} --}}
                                            <a class="menubtn header-btn trans-bt cs-h"
                                                data-menu="host-lang">{{ customTrans('header.' . Str::lower(Session::get('language'))) }}/{{ customTrans('host_dashboard.sar') }}
                                                <i class="ri-arrow-down-s-line"></i>
                                            </a>
                                            <div class="navmenu" data-menu="host-lang">
                                                <div class="nav-inner">
                                                    <ul class="text-list cust-dropdown-menu">
                                                        @foreach ($language as $key => $value)
                                                            <li>
                                                                <a class="language_footer {{ Session::get('language') == $key ? 't' : '' }}"
                                                                    data-lang="{{ $key }}">
                                                                    <div class="tick-icon">
                                                                        @if ($key == Session::get('language'))
                                                                            <img height="auto" width="auto"
                                                                                src="{{ asset('icons/select.svg') }}"
                                                                                loading="lazy" alt="tick-icon">
                                                                        @endif
                                                                    </div>
                                                                    {{ $value }}
                                                                </a>
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                    <ul class="text-list cust-dropdown-menu">
                                                        @foreach ($currencies as $key => $value)
                                                            <li>

                                                                <a href="javascript:void(0)"
                                                                    class="currency {{ Session::get('currency') == $value->code ? 'currency-active' : '' }}"
                                                                    id="currency_selector"
                                                                    data-curr="{{ $value->code }}">
                                                                    <div class="tick-icon">
                                                                        @if ($value->code == Session::get('currency'))
                                                                            <img height="auto" width="auto"
                                                                                src="{{ asset('icons/select.svg') }}"
                                                                                alt="tick icon">
                                                                        @endif
                                                                    </div>
                                                                    {{ $value->code }}
                                                                    <!-- {-- {!! $value->org_symbol !!} --} -->
                                                                </a>
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="notification-drop">
                                        <a href="#" class="dropdown-toggle" type="button" id="ntf-dropdown"
                                            data-bs-toggle="dropdown" aria-expanded="false" onclick="readAll(event)"
                                            {{ auth()->user()->unreadNotifications()->count() > 0 ? "data-read='1'" : '' }}>
                                            @if (auth()->user()->unreadNotifications()->count() > 0)
                                                <div class="notify"></div>
                                            @endif
                                            <img height="auto" width="auto" class="hs-notification-btn"
                                                src="{{ asset('icons/host-notification.svg') }}" alt="bell icon">
                                        </a>
                                        <div class="dropdown-menu ntf-main hs-ntf-main" aria-labelledby="ntf-dropdown">
                                            <div class="ntf-header">
                                                <h6 class="mb-0">{{ customTrans('notification.notification') }}</h6>
                                                <span
                                                    class="total-ntf">{{ auth()->user()->notifications()->count() }}</span>
                                            </div>
                                            <ul class="ntf-inner" id="notifications"
                                                {{ auth()->user()->notifications()->count() < 1 ? 'data-list="fasle"' : '' }}>
                                                @forelse ($unread_noti as $notification)
                                                    <li>
                                                        <a href="{{ $notification->link }}"
                                                            class="dropdown-item ntf-item">
                                                            <div class="ntf-image">
                                                                <img src="{{ asset('icons/user.svg') }}" alt="">
                                                            </div>
                                                            <div class="ntf-content">
                                                                <div class="sender-msg">
                                                                    <p class="mb-0">
                                                                        {{ $notification->message }}
                                                                        {{ $notification->created_at }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </a>
                                                    </li>
                                                @empty
                                                    <div class="no-ntf">
                                                        <div>
                                                            <img height="auto" width="auto"
                                                                src="{{ asset('icons/ntf-bel.svg') }}" alt="bell">
                                                            <p>No notifications</p>
                                                        </div>
                                                    </div>
                                                @endforelse
                                            </ul>
                                            @if (auth()->user()->notifications()->count() > 5)
                                                <div class="ntf-see">
                                                    <a
                                                        href="{{ route('user.notifications') }}">{{ customTrans('general.see_all') }}</a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endauth

                                @auth
                                    <div class="user dropdown">
                                        <button class="nav-menu dropdown-toggle" type="button" id="dropdownMenuButton1"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            <img height="auto" width="auto" loading="lazy"
                                                src="{{ asset(auth()->user()->profile_src ?? 'icons/user.svg') }}"
                                                class="user-profile rounded-circle host-profile-icn">

                                        </button>
                                        <ul class="dropdown-menu custom-dropdown-menu pr-list"
                                            aria-labelledby="dropdownMenuButton1">


                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('managehost.accountManage') }}"><i
                                                        class="ri-account-box-line"></i>
                                                    {{ auth()->user()->full_name }}</a>
                                            </li>

                                            @if (Session::get('user_mode') == 'user')
                                                <li>
                                                    <a class="dropdown-item" id="updateUserMode" data-usermode="host"><i
                                                            class="ri-switch-fill mb-al-icon"></i>
                                                        {{ customTrans('account_mobile.switch_to_hosting') }}</a>
                                                </li>
                                            @else
                                                <li>
                                                    <a class="dropdown-item" id="updateUserMode" data-usermode="user"><i
                                                            class="ri-switch-fill mb-al-icon"></i>
                                                        {{ customTrans('account_mobile.switch_to_travelling') }}</a>
                                                </li>
                                            @endif
                                            <li>
                                                <a class="dropdown-item" href="{{ route('user.notifications') }}"><i
                                                        class="ri-notification-line"></i>
                                                    {{ customTrans('notification.notification') }}</a>
                                            </li>
                                            @if (Session::get('user_mode') == 'host')
                                                <li>
                                                    <a class="dropdown-item"
                                                        href="{{ route('properties.chat.view', ['type' => 'host']) }}"><i
                                                            class="ri-mail-unread-line"></i>
                                                        {{ customTrans('header.inbox') }}</a>
                                                </li>
                                            @else
                                                <li>
                                                    <a class="dropdown-item"
                                                        href="{{ route('properties.chat.view', ['type' => 'guest']) }}"><i
                                                            class="ri-mail-unread-line"></i>
                                                        {{ customTrans('header.inbox') }}</a>
                                                </li>
                                            @endif
                                            @if (Session::get('user_mode') == 'user')
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('wishlists.listing') }}"><i
                                                            class="ri-heart-3-line"></i>
                                                        {{ customTrans('wishlist.wishlist') }}</a>
                                                </li>
                                                {{-- <li>
                                                    <a class="dropdown-item" href="{{ route('guest_reservation') }}"><i
                                                            class="ri-heart-3-line"></i>
                                                        Guest New Reservation</a>
                                                </li> --}}
                                            @endif
                                            @if (auth()->user()->is_elm_verified)
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('guest_reservation') }}">
                                                        <i class="ri-plane-line"></i>
                                                        {{ customTrans('reservation.reservation') }}</a>
                                                </li>
                                            @else
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('ilm_yaqeen') }}">
                                                        <i class="ri-plane-line"></i>
                                                        {{ customTrans('reservation.reservation') }}</a>
                                                    </a>
                                                </li>
                                            @endif
                                            <li>
                                                <a class="dropdown-item" href="{{ route('user.wallet') }}"><i
                                                        class="ri-wallet-3-line"></i>
                                                    {{ customTrans('sidenav.wallet') }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ route('tickets.ticket_list') }}"><i
                                                        class="ri-ticket-2-line"></i>
                                                    {{ customTrans('listing.mytickets') }}</a>
                                            </li>
                                            @if (Session::get('user_mode') == 'host')
                                                {{-- <li>
                                                <a class="dropdown-item" href="{{ route('yourlisting') }}"><i
                                                        class="ri-list-check-2"></i>
                                                    {{ customTrans('listing.listing_management') }}</a>
                                            </li> --}}
                                                {{-- @if (strtolower(env('APP_ENV')) != 'prod') --}}
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('pages.promotions') }}"><i
                                                            class="ri-megaphone-line"><svg
                                                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                                width="16" height="16">
                                                                <path
                                                                    d="M9 17C9 17 16 18 19 21H20C20.5523 21 21 20.5523 21 20V13.937C21.8626 13.715 22.5 12.9319 22.5 12C22.5 11.0681 21.8626 10.285 21 10.063V4C21 3.44772 20.5523 3 20 3H19C16 6 9 7 9 7H5C3.89543 7 3 7.89543 3 9V15C3 16.1046 3.89543 17 5 17H6L7 22H9V17ZM11 8.6612C11.6833 8.5146 12.5275 8.31193 13.4393 8.04373C15.1175 7.55014 17.25 6.77262 19 5.57458V18.4254C17.25 17.2274 15.1175 16.4499 13.4393 15.9563C12.5275 15.6881 11.6833 15.4854 11 15.3388V8.6612ZM5 9H9V15H5V9Z">
                                                                </path>
                                                            </svg></i> {{ customTrans('listing.promotions') }}</a>
                                                </li>



                                                <li>
                                                    <a class="dropdown-item"
                                                        href="{{ route('pages.transaction_details') }}"><i
                                                            class="ri-exchange-dollar-line"></i>
                                                        {{ customTrans('account_transaction.transaction') }}</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('userreview') }}"><i
                                                            class="ri-star-half-line"></i>
                                                        {{ customTrans('sidenav.reviews') }}</a>
                                                </li>
                                            @endif
                                            <li>
                                                <a class="dropdown-item" href="#" data-bs-toggle="modal"
                                                    data-bs-target="#logout-modal"><i class="ri-shut-down-line"></i>
                                                    {{ customTrans('header.logout') }}</a>
                                            </li>


                                        </ul>
                                    </div>
                                @else
                                    <div class="log-btn">
                                        <div>
                                            <a class="signup-in-btn loadskull" href="javascript:void(0)"
                                                data-bs-toggle="modal" {{-- data-bs-target="#staticBackdrop">{{ customTrans('header.getstarted') }}</a> --}}
                                                data-bs-target="#staticBackdrop">{{ customTrans('header.getstarted') }}</a>
                                        </div>
                                    </div>
                                @endauth
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </header>
    @if (!request()->is('managehost/instruction'))
        @php($unVerifiedProperties = getUnverifiedPropertiesCount('web'))
        {{-- @if ($unVerifiedProperties > 0)
            <div class="property-license-alert">
                <div class="container">
                    <div class="d-flex align-items-center gap-4">
                        <img src="{{ asset('icons/alert-black.svg') }}" alt="">
                        <div class="">
                            <h5 class="mb-0">{{ $unVerifiedProperties }} {{ customTrans('host_listing.properties_need_license') }}</h5>
                            <p class="mb-0">
                                {{ str_replace(':unVerifiedProperties', $unVerifiedProperties, customTrans('host_listing.please_ensure_verifying_properties')) }}
                                <a href="{{ route('managehost.instruction') }}" class="">{{ customTrans('host_listing.learn_more') }}</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        @endif --}}
    @endif
@else
    <header id="headerforPDF" class="nav-header nav-inerpage">
        <div class="container">
            <div class="header-inner">
                <div class="row align-items-center fl-wr">

                    <div class="col-xl-5 col-lg-5 ">
                        <div class="nav">
                            <div class="currancy-dropdown">
                                {{-- <a class="menubtn header-btn trans-bt loadskull cs-h">{{ strtoupper(Session::get('language')) }}/{{ customTrans('host_dashboard.sar') }} --}}
                                <a class="menubtn header-btn trans-bt loadskull cs-h"
                                    data-menu="global-lang-menu">{{ customTrans('header.' . Str::lower(Session::get('language'))) }}/{{ customTrans('host_dashboard.sar') }}
                                    <i class="ri-arrow-down-s-line"></i>
                                </a>
                                <div class="navmenu" data-menu="global-lang-menu">
                                    <div class="nav-inner">
                                        <ul class="text-list cust-dropdown-menu">
                                            @foreach ($language as $key => $value)
                                                <li>
                                                    <a class="language_footer {{ Session::get('language') == $key ? 't' : '' }}"
                                                        data-lang="{{ $key }}">
                                                        <div class="tick-icon">
                                                            @if ($key == Session::get('language'))
                                                                <img height="auto" width="auto"
                                                                    src="{{ asset('icons/select.svg') }}"
                                                                    loading="lazy" alt="tick-icon">
                                                            @endif
                                                        </div>
                                                        {{ $value }}
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                        <ul class="text-list cust-dropdown-menu">
                                            @foreach ($currencies as $key => $value)
                                                <li>

                                                    <a href="javascript:void(0)"
                                                        class="currency {{ Session::get('currency') == $value->code ? 'currency-active' : '' }}"
                                                        id="currency_selector" data-curr="{{ $value->code }}">
                                                        <div class="tick-icon">
                                                            @if ($value->code == Session::get('currency'))
                                                                <img height="auto" width="auto"
                                                                    src="{{ asset('icons/select.svg') }}"
                                                                    alt="tick icon">
                                                            @endif
                                                        </div>
                                                        {{ $value->code }}
                                                        <!-- {-- {!! $value->org_symbol !!} --} -->
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            {{-- <a href="{{ route('becomehost') }}" class=" ">
                                <button
                                    class="host header-btn trans-bt loadskull cs-h">{{ customTrans('footer.become_a_host') }}</button></a> --}}

                        </div>
                    </div>
                    <div class="col-2 fl-on-mb ">
                        <div class="text-center logo-main">
                            <a href="{{ route('home') }}" class="wht-logo">
                                <img src="{{ asset('logo/logo.svg') }}" height="auto" width="auto"
                                    loading="lazy" class="logo" alt="logo">
                            </a>
                        </div>

                    </div>

                    <div class="col-xl-5 col-lg-5 ">
                        <div class="nav profile-nav">

                            @auth
                                @if (Session::get('user_mode') == 'user')
                                    <div class="wishlist-main ">
                                        <a href="{{ route('wishlists.listing') }}"
                                            class="trans-bt">{{ customTrans('header.wishlist') }}</a>
                                    </div>
                                @endif
                                <div class="notification-drop">
                                    <a href="#" class="dropdown-toggle" type="button" id="ntf-dropdown"
                                        data-bs-toggle="dropdown" aria-expanded="false" onclick="readAll(event)"
                                        {{ auth()->user()->unreadNotifications()->count() > 0 ? "data-read='1'" : '' }}>
                                        @if (auth()->user()->unreadNotifications()->count() > 0)
                                            <div class="notify"></div>
                                        @endif
                                        <img height="auto" width="auto" src="{{ asset('icons/bell.svg') }}"
                                            alt="bell icon">
                                    </a>
                                    <div class="dropdown-menu ntf-main" aria-labelledby="ntf-dropdown">
                                        <div class="ntf-header">
                                            <h6 class="mb-0">{{ customTrans('notification.notification') }}</h6>
                                            <span class="total-ntf">{{ auth()->user()->notifications()->count() }}</span>
                                        </div>
                                        <ul class="ntf-inner" id="notifications"
                                            {{ auth()->user()->notifications()->count() < 1 ? 'data-list="fasle"' : '' }}>
                                            @forelse ($unread_noti as $notification)
                                                <li>
                                                    <a href="{{ $notification->link }}" class="dropdown-item ntf-item">
                                                        <div class="ntf-image">
                                                            <img src="{{ asset('icons/user.svg') }}" alt="">
                                                        </div>
                                                        <div class="ntf-content">
                                                            <div class="sender-msg">
                                                                <p class="mb-0">
                                                                    {{ $notification->message }}
                                                                    {{ $notification->created_at }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </li>
                                            @empty
                                                <div class="no-ntf">
                                                    <div>
                                                        <img height="auto" width="auto"
                                                            src="{{ asset('icons/ntf-bel.svg') }}" alt="bell">
                                                        <p>No notifications</p>
                                                    </div>
                                                </div>
                                            @endforelse
                                        </ul>
                                        @if (auth()->user()->notifications()->count() > 5)
                                            <div class="ntf-see">
                                                <a
                                                    href="{{ route('user.notifications') }}">{{ customTrans('general.see_all') }}</a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endauth

                            @auth
                                <div class="user dropdown">
                                    <button class="nav-menu dropdown-toggle" type="button" id="dropdownMenuButton1"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                        <img height="auto" width="auto" loading="lazy"
                                            src="{{ asset(auth()->user()->profile_src ?? 'icons/user.svg') }}"
                                            class="user-profile rounded-circle">
                                        {{ auth()->user()->full_name }}
                                    </button>
                                    <ul class="dropdown-menu custom-dropdown-menu pr-list"
                                        aria-labelledby="dropdownMenuButton1">




                                        @if (Session::get('user_mode') == 'user')
                                            <li>
                                                <a class="dropdown-item" href="{{ route('user.profile.view') }}"><i
                                                        class="ri-account-box-line"></i>
                                                    {{ customTrans('header.account') }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" id="updateUserMode" data-usermode="host"><i
                                                        class="ri-switch-fill mb-al-icon"></i>{{ customTrans('account_mobile.switch_to_hosting') }}
                                                </a>
                                            </li>
                                        @else
                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('managehost.accountManage') }}"><i
                                                        class="ri-account-box-line"></i>
                                                    {{ auth()->user()->full_name }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" id="updateUserMode" data-usermode="user"><i
                                                        class="ri-switch-fill mb-al-icon"></i>
                                                    {{ customTrans('account_mobile.switch_to_travelling') }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ route('managehost.day') }}"><i
                                                        class="ri-list-settings-line"></i>
                                                    {{ customTrans('header.managelist') }}</a>
                                            </li>
                                        @endif
                                        <li>
                                            <a class="dropdown-item" href="{{ route('user.notifications') }}"><i
                                                    class="ri-notification-line"></i>
                                                {{ customTrans('notification.notification') }}</a>
                                        </li>
                                        @if (Session::get('user_mode') == 'host')
                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('properties.chat.view', ['type' => 'host']) }}"><i
                                                        class="ri-mail-unread-line"></i>
                                                    {{ customTrans('header.inbox') }}</a>
                                            </li>
                                        @else
                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('properties.chat.view', ['type' => 'guest']) }}"><i
                                                        class="ri-mail-unread-line"></i>
                                                    {{ customTrans('header.inbox') }}</a>
                                            </li>
                                        @endif
                                        @if (Session::get('user_mode') == 'user')
                                            <li>
                                                <a class="dropdown-item" href="{{ route('wishlists.listing') }}"><i
                                                        class="ri-heart-3-line"></i>
                                                    {{ customTrans('wishlist.wishlist') }}</a>
                                            </li>
                                            {{-- <li>
                                                <a class="dropdown-item" href="{{ route('guest_reservation') }}"><i
                                                        class="ri-heart-3-line"></i>
                                                    Guest New Reservation</a>
                                            </li> --}}
                                        @endif

                                        @if (auth()->user()->is_elm_verified)
                                            <li>
                                                <a class="dropdown-item" href="{{ route('guest_reservation') }}">
                                                    <i class="ri-plane-line"></i>
                                                    {{ customTrans('reservation.reservation') }}</a>
                                            </li>
                                        @else
                                            <li>
                                                <a class="dropdown-item" href="{{ route('ilm_yaqeen') }}">
                                                    <i class="ri-plane-line"></i>
                                                    {{ customTrans('reservation.reservation') }}</a>
                                                </a>
                                            </li>
                                        @endif
                                        <li>
                                            <a class="dropdown-item" href="{{ route('user.wallet') }}"><i
                                                    class="ri-wallet-3-line"></i>
                                                {{ customTrans('sidenav.wallet') }}</a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ route('tickets.ticket_list') }}"><i
                                                    class="ri-ticket-2-line"></i>
                                                {{ customTrans('listing.mytickets') }}</a>
                                        </li>
                                        @if (Session::get('user_mode') == 'host')
                                            {{-- <li>
                                                <a class="dropdown-item" href="{{ route('yourlisting') }}"><i
                                                        class="ri-list-check-2"></i>
                                                    {{ customTrans('listing.listing_management') }}</a>
                                            </li> --}}
                                            {{-- @if (strtolower(env('APP_ENV')) != 'prod') --}}
                                            <li>
                                                <a class="dropdown-item" href="{{ route('pages.promotions') }}"><i
                                                        class="ri-megaphone-line"><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="16" height="16">
                                                            <path
                                                                d="M9 17C9 17 16 18 19 21H20C20.5523 21 21 20.5523 21 20V13.937C21.8626 13.715 22.5 12.9319 22.5 12C22.5 11.0681 21.8626 10.285 21 10.063V4C21 3.44772 20.5523 3 20 3H19C16 6 9 7 9 7H5C3.89543 7 3 7.89543 3 9V15C3 16.1046 3.89543 17 5 17H6L7 22H9V17ZM11 8.6612C11.6833 8.5146 12.5275 8.31193 13.4393 8.04373C15.1175 7.55014 17.25 6.77262 19 5.57458V18.4254C17.25 17.2274 15.1175 16.4499 13.4393 15.9563C12.5275 15.6881 11.6833 15.4854 11 15.3388V8.6612ZM5 9H9V15H5V9Z">
                                                            </path>
                                                        </svg></i> {{ customTrans('promocode.promotions') }}</a>
                                            </li>



                                            <li>
                                                <a class="dropdown-item"
                                                    href="{{ route('pages.transaction_details') }}"><i
                                                        class="ri-exchange-dollar-line"></i>
                                                    {{ customTrans('account_transaction.transaction') }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ route('userreview') }}"><i
                                                        class="ri-star-half-line"></i>
                                                    {{ customTrans('sidenav.reviews') }}</a>
                                            </li>
                                        @endif
                                        <li>
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal"
                                                data-bs-target="#logout-modal"><i class="ri-shut-down-line"></i>
                                                {{ customTrans('header.logout') }}</a>
                                        </li>


                                    </ul>
                                </div>
                            @else
                                <div class="log-btn">


                                    <div>
                                        <a class="signup-in-btn loadskull" href="javascript:void(0)"
                                            data-bs-toggle="modal" {{-- data-bs-target="#staticBackdrop">{{ customTrans('header.getstarted') }}</a> --}}
                                            data-bs-target="#staticBackdrop">{{ customTrans('header.getstarted') }}</a>
                                    </div>
                                </div>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
@endif
<script>
    var darentKey = "in~311c4723";
</script>
@if (strtolower(env('APP_ENV')) == 'prod')
    <script>
        darentKey = "in~58adcbb5";
    </script>
@endif
{{-- WebEngage --}}
<script id='_webengage_script_tag' type='text/javascript'>
    var webengage;
    ! function(w, e, b, n, g) {
        function o(e, t) {
            e[t[t.length - 1]] = function() {
                r.__queue.push([t.join("."),
                    arguments
                ])
            }
        }
        var i, s, r = w[b],
            z = " ",
            l = "init options track screen onReady".split(z),
            a = "webPersonalization feedback survey notification notificationInbox".split(z),
            c = "options render clear abort".split(z),
            p = "Prepare Render Open Close Submit Complete View Click".split(z),
            u = "identify login logout setAttribute".split(z);
        if (!r || !r.__v) {
            for (w[b] = r = {
                    __queue: [],
                    __v: "6.0",
                    user: {}
                }, i = 0; i < l.length; i++) o(r, [l[i]]);
            for (i = 0; i < a.length; i++) {
                for (r[a[i]] = {}, s = 0; s < c.length; s++) o(r[a[i]], [a[i], c[s]]);
                for (s = 0; s < p.length; s++) o(r[a[i]], [a[i], "on" + p[s]])
            }
            for (i = 0; i < u.length; i++) o(r.user, ["user", u[i]]);
            setTimeout(function() {
                var f = e.createElement("script"),
                    d = e.getElementById("_webengage_script_tag");
                f.type = "text/javascript", f.async = !0, f.src = ("https:" == e.location.protocol ?
                        "https://widgets.in.webengage.com" : "http://widgets.in.webengage.com") +
                    "/js/webengage-min-v-6.0.js", d.parentNode.insertBefore(f, d)
            })
        }
    }(window, document, "webengage");
    webengage.init(darentKey);
</script>
{{-- WebEngage --}}

<!--================Header Menu Area =================-->
{{-- <div class="flash-container">
    @if (Session::has('message'))
        <div class="alert {{ Session::get('alert-class') }} text-center mb-0" role="alert">
            {{ Session::get('message') }}
            <a href="#" class="pull-right" class="alert-close" data-bs-dismiss="alert">&times;</a>
        </div>
    @endif

    <div class="alert alert-success text-center mb-0 d-none" id="success_message_div" role="alert">
        <a href="#" class="pull-right" class="alert-close" data-bs-dismiss="alert">&times;</a>
        <p id="success_message"></p>
    </div>

    <div class="alert alert-danger text-center mb-0 d-none" id="error_message_div" role="alert">
        <p><a href="#" class="pull-right" class="alert-close" data-bs-dismiss="alert">&times;</a></p>
        <p id="error_message"></p>
    </div>
</div> --}}
