@extends('admin.template')

@section('main')
<div class="content-wrapper">
    <section class="content-header">
        <h1>Property Request <small>Submit Property Request</small></h1>
        @include('admin.common.breadcrumb')
    </section>

    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box">
                    <form class="form-horizontal" id="requestSubmit" method="POST" action="{{ route('admin.property-management-requests.update',$management->id) }}">
                        @csrf
                        @method('PUT')
                        <div class="box-body">

                            <div class="form-group">
                                <label class="control-label col-sm-3">Full Name<span class="text-danger">*</span></label>
                                <div class="col-sm-6">
                                    <input type="text" name="owner_name" class="form-control" placeholder="Enter your full name" value="{{ old('owner_name',$management->owner_name) }}">
                                    @error('owner_name')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-sm-3">Email<span class="text-danger">*</span></label>
                                <div class="col-sm-6">
                                    <input type="email" name="email" class="form-control" placeholder="Enter your email" value="{{ old('email',$management->email) }}">
                                    @error('email')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-sm-3">Property Name<span class="text-danger">*</span></label>
                                <div class="col-sm-6">
                                    <input type="text" name="property_name" class="form-control" placeholder="Enter property name" value="{{ old('property_name',$management->property_name) }}">
                                    @error('property_name')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-sm-3">Number of Units</label>
                                <div class="col-sm-6">
                                    <input type="number" name="units" class="form-control" placeholder="0" value="{{ old('units',$management->units) }}">
                                    @error('units')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-sm-3">Location</label>
                                <div class="col-sm-6">
                                    <p><b>Location : </b> {{ $management->location }}</p>
                                    <input type="text" name="address_line_1" id="address_line_1" class="form-control" placeholder="Enter your location" value="{{ old('address_line_1',$management->location) }}">
                                    @error('address_line_1')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                    <div id="backgroundmap" class="map-view-full-location mt-2"></div>

                                    <input type="hidden" name='latitude' id='latitude' value="{{ old('latitude',$management->latitude) }}">
                                    <input type="hidden" name='longitude' id='longitude' value="{{ old('longitude',$management->longitude) }}">
                                    <input type="hidden" name="street" id="street_number" value="{{ old('street_number',$management->street_number) }}">
                                    <input type="hidden" name="route" id="route" value="{{ old('route',$management->route) }}">
                                    <input type="hidden" name="district" id="district" value="{{ old('district',$management->district) }}">
                                    <input type="hidden" name="state" id="state" value="{{ old('state',$management->state) }}">
                                    <input type="hidden" name="country" id="country" value="{{ old('country',$management->country) }}">
                                    <input type="hidden" name="postal_code" id="postal_code" value="{{ old('postal_code',$management->postal_code) }}">
                                    <input type="hidden" name="city" id="city" value="{{ old('city',$management->city) }}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-sm-3">City<span class="text-danger">*</span></label>
                                <div class="col-sm-6">
                                    <select name="city_id" id="city_id" class="form-control">
                                        <option disabled selected>Select City</option>
                                        @foreach ($cities as $city)
                                            <option value="{{ $city->id }}" {{ old('city_id',$management->city_id) == $city->id ? 'selected' : '' }}>{{ app()->getLocale() == 'ar' ? $city->name_ar : $city->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('city_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-sm-3">Contact Number<span class="text-danger">*</span></label>
                                <div class="col-sm-6">
                                    <div class="input-group">
                                        <input type="tel" name="contact_no" id="contact_no" class="form-control" value="{{ old('contact_no',$management->contact_no) }}" placeholder="+****************">
                                    </div>
                                    @error('contact_no')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                        </div>
                        <div class="box-footer">
                            <div class="col-sm-offset-3 col-sm-6">
                                <button type="submit" id="submitBtn" class="btn btn-primary" disabled>Submit Request</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
@push('scripts')
<script rel="preload" src="{{ asset('js/locationpicker.jquery.min.js') }}" as="script"></script>

    <script type="text/javascript">
        var get_lat;
        var get_lng;
        var permission;

        const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');

    const requiredFields = [
        'owner_name', 'email', 'property_name', 'units', 'address_line_1','city_id', 'contact_no'
    ];

    form.addEventListener('input', () => {
        let isValid = requiredFields.every(field => {
            const el = form.querySelector(`[name="${field}"]`);
            return el && el.value.trim() !== '';
        });

        const emailInput = form.querySelector('[name="email"]');
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const validEmail = emailPattern.test(emailInput.value.trim());

        submitBtn.disabled = !(isValid && validEmail);
    });

        function updateControls(addressComponents) {
            $('#street_number').val(addressComponents.streetNumber);
            $('#route').val(addressComponents.streetName);
            if (addressComponents.city) {
                $('#city').val(addressComponents.city);
            }
            $('#state').val(addressComponents.stateOrProvince);
            $('#district').val(addressComponents.district);
            $('#postal_code').val(addressComponents.postalCode);
            $('#country').val(addressComponents.country);
        }


        showPosition();

        function showPosition(position) {

            let oldlat = 24.531068538707988;
            let oldlng = 46.70622317031249;

            get_lat = oldlat != 0 ? oldlat : position.coords.latitude;
            get_lng = oldlng != 0 ? oldlng : position.coords.longitude;

            lat = parseFloat(get_lat);
            lng = parseFloat(get_lng);

            $('#backgroundmap').locationpicker({
                location: {
                    latitude: lat,
                    longitude: lng,
                },
                radius: 0,
                zoom: oldlat == '24.531068538708' ? 5 : 14,
                // zoom:14,
                enableAutocomplete: true,
                enableReverseGeocode: true,
                markerDraggable: true,
                accuracy: 100, // set the accuracy threshold to 100 meters

                addressFormat: "",
                inputBinding: {
                    latitudeInput: $('#latitude'),
                    longitudeInput: $('#longitude'),
                    locationNameInput: $('#address_line_1')
                },
                onchanged: function(currentLocation, radius, isMarkerDropped) {
                    var addressComponents = $(this).locationpicker('map').location.addressComponents;
                    updateControls(addressComponents);
                },
                oninitialized: function(component) {
                    var addressComponents = $(component).locationpicker('map').location.addressComponents;
                    updateControls(addressComponents);
                }
            });

            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({
                location: {
                    lat,
                    lng
                }
            }, (results, status) => {
                if (status === "OK") {
                    if (results[0]) {
                        // Extract the address components from the result
                        const addressComponents = results[0].address_components;
                        // updateControls(addressComponents);

                        const streetNumber = addressComponents.find(component => component.types
                            .includes("street_number"))?.long_name;
                        const streetName = addressComponents.find(component => component.types
                            .includes("route"))?.long_name;
                        const city = addressComponents.find(component => component.types.includes(
                            "locality"))?.long_name;
                        const state = addressComponents.find(component => component.types.includes(
                            "administrative_area_level_1"))?.short_name;
                        const country = addressComponents.find(component => component.types
                            .includes("country"))?.short_name;

                        const address = `${streetNumber??''} ${streetName??''} ${city}, ${state}`;

                        if (!$('#address_line_1').val()) {
                            $('#address_line_1').val(address);
                        }
                        $('#country').val(country);
                        $('#state').val(state);
                        if (city) {
                            $('#city').val(city);
                        }


                        // Use the address components as needed

                    } else {
                        console.log("No results found");
                    }
                } else {
                    console.log(`Geocoder failed due to: ${status}`);
                }
            });

        }

        function showError(params) {
            alert('Error Occured in Google Map!');
            console.log(params);
        }

</script>
@endpush
