@extends('admin.template')

@section('main')

    <div class="content-wrapper">
    <section class="content-header">
             <h1>DarStays<small>Control panel</small></h1>
             @include('admin.common.breadcrumb')
         </section>
        <section class="content">
        <div class="row">
                 <div class="col-xs-12">
                     <div class="box">
                         <div class="box-body">
                             <form class="form-horizontal" enctype='multipart/form-data' action="{{ route('admin.property-management-requests.index') }}"
                                 method="GET" accept-charset="UTF-8">
                                 {{ csrf_field() }}
                                 <div class="col-md-12  d-none">
                                     <input class="form-control" type="text" id="startDate" name="from"
                                         value="<?= isset($from) ? $from : '' ?>" hidden>
                                     <input class="form-control" type="text" id="endDate" name="to"
                                         value="<?= isset($to) ? $to : '' ?>" hidden>
                                 </div>
                                 <div class="col-md-12">
                                     <div class="">
                                         <div class="row">
                                             <div class="col-md-3 col-sm-4 col-xs-12">
                                                 <label>Date Range</label>
                                                 <div class="input-group  col-xs-12">
                                                     <button type="button" class="form-control" id="daterange-btn">
                                                         <span class="pull-left">
                                                             <i class="fa fa-calendar"></i> Pick a date range
                                                         </span>
                                                         <i class="fa fa-caret-down pull-right"></i>
                                                     </button>
                                                 </div>
                                             </div>

                                             <div class="col-md-1 col-sm-2 col-xs-4">
                                                 <br>
                                                 <button type="submit" name="btn"
                                                     class="btn btn-primary btn-flat">Filter</button>
                                             </div>

                                             <div class="col-md-1 col-sm-2 col-xs-4">
                                                 <br>
                                                 <button type="submit" name="reset_btn"
                                                     class="btn btn-primary btn-flat">Reset</button>
                                             </div>
                                         </div>
                                     </div>
                                 </div>
                             </form>
                         </div>
                     </div>
                 </div>
             </div>

             <div class="row">
                 <div class="col-xs-12">
                     <div class="box">
                         <div class="box-body">
                             <div class="row">
                                 <div class="col-md-6">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $total_requests }}</span><br>
                                             <span class="font-weight-bold total-customer">Total number of requests</span><br>
                                             {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-md-6">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $total_requests_today }}</span><br>
                                             <span class="font-weight-bold total-customer">Number of requests submitted today</span><br>
                                         </div>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="box box_info">
                        <div class="box-header">
                            <h3 class="box-title">Propery Management Requests</h3>
                        </div>
                        <div class="box-body">
                            {!! $dataTable->table() !!}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <form class="d-none" id="every-form" method="POST">@csrf</form>
@endsection

@push('scripts')
    <script src="{{ asset('DataTables-1.10.18/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('Responsive-2.2.2/js/dataTables.responsive.min.js') }}"></script>
    {!! $dataTable->scripts() !!}
    <script>
        $(document).ready(function() {
            var startDate = $('#startDate').val();
             var endDate = $('#endDate').val();
             dateRangeBtn(startDate, endDate, dt = 1);
             formDate(startDate, endDate);
            $('#dataTableBuilder_length').after(
                '<div id="exportArea" class="col-md-2 col-sm-2 "><div class="row mt-m-2"><div class="btn-group col-6"><button type="button" class="form-control dropdown-toggle" data-toggle="dropdown" aria-haspopup="true">Export</button><ul class="dropdown-menu"><li><a href="" title="CSV" id="csv">CSV</a></li><li><a href="" title="PDF" id="pdf">PDF</a></li></ul></div><div class="btn btn-group btn-refresh col-6"><a href="" id="tablereload" class="form-control"><span><i class="fa fa-refresh"></i></span></a></div></div></div>'
            );
        });

        document.getElementById("delete-modal-yes").addEventListener("click", function(e) {
            e.preventDefault();
            const form = document.getElementById("every-form")
            form.setAttribute('action', e.target.getAttribute("href"))
            form.insertAdjacentHTML("beforeend", `@method('DELETE')`)
            form.submit()
        })



    </script>
@endpush
