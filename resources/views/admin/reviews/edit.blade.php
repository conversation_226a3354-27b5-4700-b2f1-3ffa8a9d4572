@extends('admin.template')
@section('main')
@push('css')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
@endpush
<!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Main content -->
    <section class="content">
      <div class="row">
        <!-- right column -->
        <div class="col-md-8 col-sm-offset-2">
          <!-- Horizontal Form -->
          <div class="box box-info box_info">
            <div class="box-header with-border">
              <h3 class="box-title">Edit Review Form</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form id="rev_form" action="{{route("admin.update-review",["id"=>$result->id])}}" method="post">
                @csrf
                @method('PUT')

              <div class="box-body">
                <div class="form-group">
                  <label for="booking_id" class="col-sm-3 control-label">Booking Id</label>
                  <div class="col-sm-6">
                    <p>{{$result->booking_id}}</p>
                  </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group">
                  <label for="booking_id" class="col-sm-3 control-label">Property ID</label>
                  <div class="col-sm-6">
                    <p>{{$result->property_code}}</p>
                  </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group">
                  <label for="property_name" class="col-sm-3 control-label">Property Name</label>
                  <div class="col-sm-6">
                    <p>{{$result->property_name}}</p>
                  </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group">
                  <label for="sender" class="col-sm-3 control-label">Guest</label>
                  <div class="col-sm-6">
                    <p>{{$result->sender}}</p>
                  </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group">
                  <label for="receiver" class="col-sm-3 control-label">Host</label>
                  <div class="col-sm-6">
                    <p>{{$result->receiver}}</p>
                  </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group">
                  <label for="reviewer" class="col-sm-3 control-label">Reviewed By</label>
                  <div class="col-sm-6">
                    <p>{{$result->reviewer}}</p>
                  </div>
                </div>

                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">User (Guest)</label>
                  <div class="col-sm-6">
                    <div class="input-group d-flex">
                      <select class="selectpicker admn-custm-select" data-size="10" name="guest_id" data-live-search="true">
                          <option value="" selected>Select Guest</option>
                            @foreach ($users as $user)
                                <option value="{{$user->id}}" @selected($user->id==$result->sender_id)>{{$user->fullName}}</option>
                            @endforeach
                      </select>
                    </div>
                </div>
                </div>

                {{-- <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">User (Guest)</label>
                  <div class="col-sm-6">
                    <div class="input-group d-flex">
                        <select class="selectpicker admn-custm-select" data-size="10" name="guest_id">
                            <option value="" selected>Select Guest</option>
                            @foreach ($users as $user)
                                <option value="{{$user->id}}">{{$user->fullName}}</option>
                            @endforeach
                        </select>
                      </div>

                  </div>
                </div> --}}

                @if($result->reviewer == 'guest')
                <div class="clearfix"></div>
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Rating</label>
                  <div class="col-sm-6">
                    <input type="number" name="rating" min="1" max="5" class="form-control" value="{{$result->rating}}" readonly/>
                  </div>
                </div><br><br>
                   <div class="clearfix"></div>
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Accuracy</label>
                  <div class="col-sm-6">
                    <input type="number" name="accuracy" min="1" max="5" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{$result->accuracy}}" />
                    <span class="text-danger">{{ $errors->first('accuracy') }}</span>
                  </div>
                </div><br><br>
                   <div class="clearfix"></div>
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Location</label>
                  <div class="col-sm-6">
                    <input type="number" name="location" min="1" max="5" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{$result->location}}" />
                  </div>
                </div><br><br>
                   <div class="clearfix"></div> 
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Communication
                 </label>
                  <div class="col-sm-6">
                    <input type="number" name="communication" min="1" max="5" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{$result->communication}}" />
                    <span class="text-danger">{{ $errors->first('communication') }}</span>
                  </div>
                </div><br><br>
                   <div class="clearfix"></div>
                {{-- <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Check In</label>
                  <div class="col-sm-6">
                    <input type="number" name="checkin" min="0" max="5" class="form-control" value="{{$result->checkin}}" />
                  </div>
                </div><br><br>
                   <div class="clearfix"></div> --}}
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Cleanliness</label>
                  <div class="col-sm-6">
                    <input type="number" name="cleanliness" min="1" max="5" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{$result->cleanliness}}" />
                    <span class="text-danger">{{ $errors->first('cleanliness') }}</span>
                  </div>
                </div><br><br>
                   <div class="clearfix"></div>

                <!-- <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Darent Service</label>
                  <div class="col-sm-6">
                    <input type="number" name="darent_service" min="1" max="10" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{$result->darent_service}}" />
                    <span class="text-danger">{{ $errors->first('darent_service') }}</span>
                  </div>
                </div><br><br>
                   <div class="clearfix"></div>

                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Darent Recomended</label>
                  <div class="col-sm-6">
                    <input type="number" name="darent_recomended" min="1" max="10" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{$result->darent_recomended}}" />
                    <span class="text-danger">{{ $errors->first('darent_recomended') }}</span>
                  </div>
                </div><br><br>
                   <div class="clearfix"></div> -->
                {{-- <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Value</label>
                  <div class="col-sm-6">
                    <input type="number" name="value" min="0" max="5" class="form-control" value="{{$result->value}}" />
                  </div>
                </div><br><br> --}}

                {{-- <div class="clearfix"></div> --}}
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Created at</label>
                  <div class="col-sm-6">
                    @php
                      $formattedDate = Carbon\Carbon::parse($result->created_at)->format('Y-m-d');
                    @endphp
                    <input type="date" name="created_at" class="form-control" value="{{$formattedDate}}" />
                  </div>
                </div><br><br>


                <div class="clearfix"></div>
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">List/Unlist</label>
                  <div class="col-sm-6">
                    <select class="form-control" name="publish" required>
                        <option value="1" {{$result->ispublic == 1 ? 'selected' : ''}}>List</option>
                        <option value="0" {{$result->ispublic == 0 ? 'selected' : ''}}> Unlist</option>
                    </select>
                  </div>
                </div><br><br>

                @endif


                <div class="clearfix"></div>
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label">Message<em class="text-danger">*</em></label>
                  <div class="col-sm-6">
                    <textarea name="message" class="form-control">{{$result->message}}</textarea>
                    <span class="text-danger">{{ $errors->first('message') }}</span>
                  </div>
                </div>
               <br><br>
                <div class="clearfix"></div>



              </div>
              <!-- /.box-body -->
              <div class="box-footer">
                <button type="submit" class="btn btn-info btn-space" name="submit" value="submit">Submit</button>
                <button type="submit" class="btn btn-danger" name="cancel" value="cancel">Cancel</button>

              </div>
              <!-- /.box-footer -->
            </form>
          </div>
          <!-- /.box -->
        </div>
        <!--/.col (right) -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  @push('scripts')

  <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/js/bootstrap-select.min.js"></script>
<script>
  $('#input_dob').datepicker({ 'format': 'dd-mm-yyyy'});

  $(document).ready(function () {

            $('#rev_form').validate({
                rules: {
                    message: {
                        required: true
                    }

                }
            });

        });
</script>
@endpush
@stop


