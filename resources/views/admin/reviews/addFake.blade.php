@extends('admin.template')
@section('main')
@push('css')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
@endpush
<!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Main content -->
    <section class="content">
      <div class="row">
        <!-- right column -->
        <div class="col-md-8 col-sm-offset-2">
          <!-- Horizontal Form -->
          <div class="box box-info box_info">
            <div class="box-header with-border">
              <h3 class="box-title">Add Review Form</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form id="rev_form" action="{{url('admin/addReviewAdmin')}}" method="post">
            {{ csrf_field() }}

              <div class="box-body">
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Property</label> <small>(primary key | name | id )</small>
                  <div class="col-sm-6">
                      <div class="input-group d-flex">
                        <select class="selectpicker admn-custm-select" data-size="10" name="property_id" data-live-search="true" required>
                            <option value="" selected>Select Property</option>
                            @foreach ($properties as $property)
                                <option value="{{$property->id}}">{{$property->id.' | '. $property->name.' | '.$property->property_code}}</option>
                            @endforeach
                        </select>
                        <span class="text-danger">{{ $errors->first('property_id') }}</span>
                      </div>
                       {{-- <select class="form-control cust-sel" name="property_id" required>
                          <option value="" selected>Select Property</option>
                          @foreach ($properties as $property)
                              <option value="{{$property->id}}">{{$property->id.' | '. $property->name.' | '.$property->property_code}}</option>
                          @endforeach
                      </select> --}}
                  </div>
                </div>
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">User (Guest)</label>
                  <div class="col-sm-6">
                    <div class="input-group d-flex">
                        <select class="selectpicker admn-custm-select" data-size="10" data-live-search="true" name="guest_id">
                            <option value="" selected>Select Guest</option>
                            @foreach ($users as $user)
                            
                                <option value="{{$user->id}}">{{$user->fullName.' | '.$user->formatted_phone}}</option>
                            @endforeach
                        </select>
                        <span class="text-danger">{{ $errors->first('guest_id') }}</span>

                      </div>
                      {{-- <select class="form-control" name="guest_id" required>
                        <option value="" selected>Select Guest</option>
                        @foreach ($users as $user)
                            <option value="{{$user->id}}">{{$user->fullName}}</option>
                        @endforeach
                    </select> --}}
                  </div>
                </div>
                {{-- <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">User (Guest)</label>
                  <div class="col-sm-6">
                    <div class="input-group d-flex">
                      <input type="text" name="guest_name" class="form-control"  />
                      </div>
                      
                  </div>
                </div> --}}
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Rating</label>
                  <div class="col-sm-6">
                    <input type="number" name="rating" min="1" max="10" class="form-control" value="{{old('salary_rating', '')}}" disabled />
                    {{-- <span class="text-danger">{{ $errors->first('guest_id') }}</span> --}}
                  
                  </div>
                </div>
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Accuracy</label>
                  <div class="col-sm-6">
                    <input type="number" name="accuracy" min="1" max="10" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{old('accuracy', '')}}" />
                    <span class="text-danger">{{ $errors->first('accuracy') }}</span>
                  
                  </div>
                </div>
                 <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Location</label>
                  <div class="col-sm-6">
                    <input type="number" name="location" min="1" max="10" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{old('location', '')}}" />
                  </div>
                </div>
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Communication
                 </label>
                  <div class="col-sm-6">
                    <input type="number" name="communication" min="1" max="10" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{old('communication', '')}}" />
                    <span class="text-danger">{{ $errors->first('communication') }}</span>
                  
                  </div>
                </div>
                {{-- <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Check In</label>
                  <div class="col-sm-6">
                    <input type="number" name="checkin" min="1" max="10" class="form-control" value="{{old('checkin', '')}}" />
                  </div>
                </div> --}}
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Cleanliness</label>
                  <div class="col-sm-6">
                    <input type="number" name="cleanliness" min="1" max="5" oninput="if (this.value > 5) this.value = 5;"  class="form-control" value="{{old('cleanliness', '')}}" />
                    <span class="text-danger">{{ $errors->first('cleanliness') }}</span>
                  
                  </div>
                </div>
                
                <!-- <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Darent Service</label>
                  <div class="col-sm-6">
                    <input type="number" name="darent_service" min="1" max="10" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{old('darent_service', '')}}" />
                    <span class="text-danger">{{ $errors->first('darent_service') }}</span>
                  
                  </div>
                </div> -->
                <!-- <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Darent Recommend</label>
                  <div class="col-sm-6">
                    <input type="number" name="darent_recom" min="1" max="10" oninput="if (this.value > 5) this.value = 5;" class="form-control" value="{{old('darent_recom', '')}}" />
                    <span class="text-danger">{{ $errors->first('darent_recom') }}</span>
                  
                  </div>
                </div> -->
                {{-- <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Value</label>
                  <div class="col-sm-6">
                    <input type="number" name="value" min="1" max="10" class="form-control" value="{{old('value', '')}}" />
                  </div>
                </div> --}}
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">List/Unlist</label>
                  <div class="col-sm-6">
                    <select class="form-control" name="publish" required>
                        <option value="1" selected>List</option>
                        <option value="0"> Unlist</option>
                    </select>
                  </div>
                </div>
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Date</label>
                  <div class="col-sm-6">
                    <input type="date" name="created_at" class="form-control" value="{{old('created_at','')}}" />
                  </div>
                </div>
                <div class="form-group col-sm-12">
                  <label for="message" class="col-sm-3 control-label">Message<em class="text-danger">*</em></label>
                  <div class="col-sm-6">
                    <textarea name="message" class="form-control">{{old('message', '')}}</textarea>
                    <span class="text-danger">{{ $errors->first('message') }}</span>
                  </div>
                </div>
              </div>
              <!-- /.box-body -->
              <div class="box-footer">
                <button type="submit" class="btn btn-info btn-space" name="submit" value="submit">Submit</button>
                <button type="submit" class="btn btn-danger" name="cancel" value="cancel">Cancel</button>

              </div>
              <!-- /.box-footer -->
            </form>
          </div>
          <!-- /.box -->
        </div>
        <!--/.col (right) -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  @push('scripts')
  <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/js/bootstrap-select.min.js"></script>
<script>
  $('#input_dob').datepicker({ 'format': 'dd-mm-yyyy'});

  $(document).ready(function () {

            $('#rev_form').validate({
                rules: {
                    message: {
                        required: true
                    }

                }
            });

            $(document).ready(function() {

                $('#rev_form').validate({
                    rules: {
                        message: {
                            required: true
                        }

                    }
                });

            });
        </script>
    @endpush
@stop
