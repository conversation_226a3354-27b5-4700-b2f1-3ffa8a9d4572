<aside class="main-sidebar">
    <section class="sidebar">
        <ul class="sidebar-menu">
            <li class="{{ Route::current()->uri() == 'admin/dashboard' ? 'active' : '' }}"><a
                    href="{{ url('admin/dashboard') }}"><i class="ri-dashboard-line"></i>&nbsp;<span>Dashboard</span></a>
            </li>
            <li class="{{ Route::current()->uri() == 'admin/compitiion-dasboard' ? 'active' : '' }}"><a
                href="{{ url('admin/competition-dashboard') }}"><i class="ri-dashboard-line"></i>&nbsp;<span>Competition Dashboard</span></a>
            </li>
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'customers'))
                <li
                    class="{{ Route::current()->uri() == 'admin/customers' || Route::current()->uri() == 'admin/add-customer' || Route::current()->uri() == 'admin/edit-customer/{id}' || Route::current()->uri() == 'admin/customer/properties/{id}' || Route::current()->uri() == 'admin/customer/bookings/{id}' || Route::current()->uri() == 'admin/customer/payouts/{id}' || Route::current()->uri() == 'admin/customer/payment-methods/{id}' || Route::current()->uri() == 'admin/customer/wallet/{id}' ? 'active' : '' }} }}">
                    <a href="{{ url('admin/customers') }}"><i class="ri-group-2-line"></i>&nbsp;<span>Customers</span></a>
                </li>
            @endif
            @if (Auth::guard('admin')->user()->id == 1 || Auth::guard('admin')->user()->id == 25 || Auth::guard('admin')->user()->id == 40 || Auth::guard('admin')->user()->id == 15 || Auth::guard('admin')->user()->id == 41 || Auth::guard('admin')->user()->id == 70)
            <li
            class="treeview {{ Route::current()->uri() == 'admin/wallets' || Route::current()->uri() == 'admin/wallets/edit/{id}' || Route::current()->uri() == 'admin/wallets/{id}' ? 'active' : '' }} }}">
            <a href="#">
                <i class="ri-book-line"></i>&nbsp;<span>Wallets</span><i
                    class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li
                    class="{{ Route::current()->uri() == 'admin/wallets' || Route::current()->uri() == 'admin/wallets/edit/{id}' ? 'active' : '' }}">
                    <a href="{{ url('admin/wallets') }}"><span>Wallets</span></a>
                </li>
                <li class="{{ Route::current()->uri() == 'admin/wallets/logs' ? 'active' : '' }}">
                    <a href="{{ route('admin.wallets.logs') }}"><span>Wallets Logs</span></a>
                </li>
            </ul>
        </li>

            @endif
            <li
                class="{{ Route::current()->uri() == 'admin/travel-agents' || Route::current()->uri() == 'admin/travel-agents/create' ? 'active' : '' }} }}">
                <a href="{{ url('admin/travel-agents') }}"><i class="ri-wallet-2-fill"></i>&nbsp;<span>Travel
                        Agents</span></a>
            </li>

            <li
                class="{{ Route::current()->uri() == 'admin/transactions' || Route::current()->uri() == 'admin/transactions/edit/{id}' || Route::current()->uri() == 'admin/transactions/{id}' ? 'active' : '' }} }}">
                <a href="{{ url('admin/transactions') }}"><i
                        class="ri-bank-card-line"></i>&nbsp;<span>Transactions</span></a>
            </li>
            <li
                class="{{ Route::current()->uri() == 'admin/otp' || Route::current()->uri() == 'admin/otp/edit/{id}' || Route::current()->uri() == 'admin/otp/{id}' ? 'active' : '' }} }}">
                <a href="{{ url('admin/otp') }}"><i class="ri-key-2-fill"></i>&nbsp;<span>Otp</span></a>
            </li>

            <li class="{{ Route::current()->uri() == 'admin/scoring' ? 'active' : '' }} }}">
                <a href="{{ url('admin/scoring') }}"><i class="ri-award-fill"></i>&nbsp;<span>Scoring</span></a>
            </li>

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'properties'))
                <li
                    class="treeview {{ Route::current()->uri() == 'admin/properties' || Route::current()->uri() == 'admin/changes/{id}/{page}' || Route::current()->uri() == 'admin/approval-changes-list' || Route::current()->uri() == 'admin/properties/property-step' ? 'active' : '' }}">
                    <a href="#">
                        <i class="ri-building-line"></i>&nbsp;<span>Properties</span><i
                            class="fa fa-angle-left pull-right"></i>
                    </a>
                    <ul class="treeview-menu">
                        <li class="{{ Route::current()->uri() == 'admin/properties' ? 'active' : '' }}"><a
                                href="{{ url('admin/properties') }}"><span>All Properties</span></a></li>
                        @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'properties') &&
                                Helpers::has_permission(Auth::guard('admin')->user()->id, 'edit_properties'))
                            <li
                                class="{{ Route::current()->uri() == 'admin/approval-changes-list' || Route::current()->uri() == 'admin/changes/{id}/{page}' ? 'active' : '' }}">
                                <a href="{{ url('admin/approval-changes-list') }}"><span>Approvals for
                                        Changes</span></a>
                            </li>
                            <li
                                class="{{ Route::current()->uri() == 'admin/properties/property-step' ? 'active' : '' }}">
                                <a href="{{ route('admin.properties.propertystep') }}"><span>Properties
                                        Steps</span></a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_bookings'))
                {{-- <li
                    class="{{ Route::current()->uri() == 'admin/bookings' || Route::current()->uri() == 'admin/bookings/detail/{id}' ? 'active' : '' }}">
                    <a href="{{ url('admin/bookings') }}"><i
                            class="ri-bookmark-3-line"></i>&nbsp;<span>Bookings</span></a>
                </li> --}}
                <li
                    class="treeview {{ Route::current()->uri() == 'admin/bookings' || Route::current()->uri() == 'admin/bookings/detail/{id}' || Route::current()->uri() == 'admin/approval-changes-list' || Route::current()->uri() == 'admin/customers/bookings/data' ? 'active' : '' }}">
                    <a href="#">
                        <i class="ri-book-line"></i>&nbsp;<span>Bookings</span><i
                            class="fa fa-angle-left pull-right"></i>
                    </a>
                    <ul class="treeview-menu">
                        <li
                            class="{{ Route::current()->uri() == 'admin/bookings' || Route::current()->uri() == 'admin/bookings/detail/{id}' ? 'active' : '' }}">
                            <a href="{{ url('admin/bookings') }}"><span>Bookings</span></a>
                        </li>
                        <li class="{{ Route::current()->uri() == 'admin/customers/bookings/data' ? 'active' : '' }}">
                            <a href="{{ route('bookings.customerData') }}"><span>Booking's Host Data</span></a>
                        </li>
                        <li
                            class="{{ Route::current()->uri() == 'admin/bookings/invoices' || Route::current()->uri() == 'admin/bookings/invoices/create' ? 'active' : '' }}">
                            <a href="{{ url('admin/bookings/invoices') }}"><span>Booking Invoices</span></a>
                        </li>
                        <li class="{{ Route::current()->uri() == 'admin/bookings/insurance' }}">
                            <a href="{{ url('admin/bookings/insurance') }}"><span>Booking Insurance</span></a>
                        </li>
                    </ul>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_bookings'))
                <li
                    class="{{ Route::current()->uri() == 'admin/properties/otp-settings' ? 'active' : '' }}">
                    <a href="{{ route('admin.otp-settings') }}"><i
                            class="ri-bookmark-3-line"></i>&nbsp;<span>OTP Settings</span></a>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_bookings'))
                <li
                    class="{{ Route::current()->uri() == 'admin/properties/chats' || Route::current()->uri() == 'admin/properties/chats/{chat}/message' ? 'active' : '' }}">
                    <a href="{{ route('admin.property.chat.heads') }}"><i
                            class="ri-bookmark-3-line"></i>&nbsp;<span>Property Chats</span></a>
                </li>
            @endif
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_bookings'))
                <li
                    class="{{ Route::current()->uri() == 'admin/properties/chats' || Route::current()->uri() == 'admin/properties/chats/{chat}/message' ? 'active' : '' }}">
                    <a href="{{ route('admin.countries.index') }}"><i
                            class="ri-bookmark-3-line"></i>&nbsp;<span>Countries</span></a>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_bookings'))
                <li
                    class="{{ Route::current()->uri() == 'admin/properties/chats' || Route::current()->uri() == 'admin/properties/chats/{chat}/message' ? 'active' : '' }}">
                    <a href="{{ route('admin.cities.index') }}"><i
                            class="ri-bookmark-3-line"></i>&nbsp;<span>Cities</span></a>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_bookings'))
                <li
                    class="{{ Route::current()->uri() == 'admin/districts/' || Route::current()->uri() == 'admin/districts/' ? 'active' : '' }}">
                    <a href="{{ route('admin.districts.index') }}"><i
                            class="ri-bookmark-3-line"></i>&nbsp;<span>Districts</span></a>
                </li>
            @endif

            @if (Auth::guard('admin')->user()->id == 1 || Auth::guard('admin')->user()->id == 25 || Auth::guard('admin')->user()->id == 40 || Auth::guard('admin')->user()->id == 19 || Auth::guard('admin')->user()->id == 53)
                <li
                    class="{{ Route::current()->uri() == 'admin/payouts' || Route::current()->uri() == 'admin/payouts/details/{id}' || Route::current()->uri() == 'admin/payouts/edit/{id}' ? 'active' : '' }}">
                    <a href="{{ url('admin/payouts') }}"><i
                            class="ri-money-dollar-circle-line"></i>&nbsp;<span>Payouts</span></a>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_amenities'))
                <li
                    class="{{ Route::current()->uri() == 'admin/amenities' || Route::current()->uri() == 'admin/add-amenities' || Route::current()->uri() == 'admin/edit-amenities/{id}' ? 'active' : '' }}">
                    <a href="{{ url('admin/amenities') }}"><i
                            class="ri-home-gear-line"></i>&nbsp;<span>Amenities</span></a>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_pages'))
                <li
                    class="{{ Route::current()->uri() == 'admin/pages' || Route::current()->uri() == 'admin/add-page' || Route::current()->uri() == 'admin/edit-page/{id}' ? 'active' : '' }}">
                    <a href="{{ url('admin/pages') }}"><i class="ri-file-list-3-line"></i>&nbsp;<span>Static
                            Pages</span></a>
                </li>
                <li class="{{ Route::current()->uri() == 'admin/contact-listing' ? 'active' : '' }}"><a
                        href="{{ url('admin/contact-listing') }}"><i class="ri-article-line"></i>&nbsp;<span> Contact
                            Listing</span></a></li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_reviews'))
                <li
                    class="{{ Route::current()->uri() == 'admin/reviews' || Route::current()->uri() == 'admin/edit_review/{id}' ? 'active' : '' }}">
                    <a href="{{ url('admin/reviews') }}"><i class="ri-user-star-line"></i>&nbsp;<span>Manage
                            Reviews</span></a>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_testimonial'))
                <li
                    class="{{ Route::current()->uri() == 'admin/testimonials' || Route::current()->uri() == 'admin/edit-testimonials/{id}' || Route::current()->uri() == 'admin/add-testimonials' ? 'active' : '' }}">
                    <a href="{{ url('admin/testimonials') }}"><i
                            class="ri-double-quotes-l"></i>&nbsp;<span>Testimonials</span></a>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_admin'))
                <li
                    class="{{ Route::current()->uri() == 'admin/admin-users' || Route::current()->uri() == 'admin/add-admin' || Route::current()->uri() == 'admin/edit-admin/{id}' ? 'active' : '' }}">
                    <a href="{{ url('admin/admin-users') }}">
                        <i class="ri-group-line"></i>&nbsp;<span>Users</span>
                    </a>
                </li>
            @endif

            {{-- @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_messages'))
                <li
                    class="{{ Route::current()->uri() == 'admin/messages' || Route::current()->uri() == 'admin/messaging/host/{id}' || Route::current()->uri() == 'admin/send-message-email/{id}' ? 'active' : '' }}">
                    <a href="{{ url('admin/messages') }}">
                        <i class="ri-message-2-line"></i>&nbsp;<span>Messages</span>
                    </a>
                </li>
            @endif --}}


            {{-- @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'view_reports'))
			<li class="treeview {{ (Route::current()->uri() == 'admin/sales-report' || Route::current()->uri() == 'admin/sales-analysis' || Route::current()->uri() == 'admin/overview-stats') ? 'active' : ''  }}">
				<a href="#">
					<i class="ri-file-chart-line"></i>&nbsp;<span>Reports</span><i class="fa fa-angle-left pull-right"></i>
				</a>
				<ul class="treeview-menu">
				<li class="{{ (Route::current()->uri() == 'admin/overview-stats') ? 'active' : ''  }}"><a href="{{ url('admin/overview-stats') }}"><span>Overview & Stats</span></a></li>
				<li class="{{ (Route::current()->uri() == 'admin/sales-report') ? 'active' : ''  }}"><a href="{{ url('admin/sales-report') }}"><span>Sales Report</span></a></li>
				<li class="{{ (Route::current()->uri() == 'admin/sales-analysis') ? 'active' : ''  }}"><a href="{{ url('admin/sales-analysis') }}"><span>Sales Analysis</span></a></li>
				</ul>
			</li>
		@endif --}}
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'space_type_setting'))
                <li class="{{ Route::current()->uri() == 'admin/settings/space-type' ? 'active' : '' }}"><a
                        href="{{ url('admin/settings/space-type') }}"><i class="ri-user-add-line"></i>&nbsp;<span>Space
                            Type</span></a></li>
            @endif
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_testimonial'))
                <li
                    class="{{ Route::current()->uri() == 'admin/refunds' || request()->routeIs('securityrefund') ? 'active' : '' }}">
                    <a href="{{ url('admin/refunds') }}"><i class="ri-refund-2-line"></i>&nbsp;<span>Security
                            Refunds</span></a>
                </li>
            @endif
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_testimonial'))
                <li
                    class="{{ Route::current()->uri() == 'admin/customer/refunds' || request()->routeIs('refund.CustomerRefund') ? 'active' : '' }}">
                    <a href="{{ url('admin/customer/refunds') }}"><i class="ri-refund-2-line"></i>&nbsp;<span>Customer
                            Refunds</span></a>
                </li>
            @endif
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_testimonial'))
                <li
                    class="{{ Route::current()->uri() == 'admin/user/device' || request()->routeIs('userdevice') ? 'active' : '' }}">
                    <a href="{{ url('admin/user/device') }}"><i class="ri-refund-2-line"></i>&nbsp;<span>User Device</span></a>
                </li>
            @endif
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_testimonial'))
                <li class="{{ Route::current()->uri() == 'admin/customer/support' ? 'active' : '' }}"><a
                        href="{{ url('admin/customer/support') }}"><i
                            class="ri-customer-service-2-fill"></i>&nbsp;<span>Customer Support</span></a></li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_testimonial'))
                <li class="{{ Route::current()->uri() == 'admin/customer/support/notification/settings' ? 'active' : '' }}"><a
                        href="{{ url('admin/customer/support/notification/settings') }}"><i
                            class="ri-customer-service-2-fill"></i>&nbsp;<span>Customer Support Setting</span></a></li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_email_template'))
                <li class="{{ Route::current()->uri() == 'admin/email-template/{id}' ? 'active' : '' }}"><a
                        href="{{ url('admin/email-template/1') }}"><i class="ri-mail-unread-line"></i>&nbsp;<span>Email
                            Templates</span></a></li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'properties'))
                <li class="{{ Route::current()->uri() == 'admin/promocodes' ? 'active' : '' }}"><a
                        href="{{ url('admin/promocodes') }}"><i class="ri-percent-line"></i>&nbsp;<span>Promotions
                            Code</span></a></li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'properties'))
                <li class="{{ Route::current()->uri() == 'admin/prop-discounts' ? 'active' : '' }}"><a
                        href="{{ url('admin/prop-discounts') }}"><i
                            class="ri-price-tag-3-line"></i>&nbsp;<span>Property Discounts</span></a></li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_admin'))
                <li class="{{ Route::current()->uri() == 'admin/tickets' ? 'active' : '' }}"><a
                        href="{{ url('admin/tickets') }}"><i
                            class="ri-coupon-2-line"></i>&nbsp;<span>Tickets</span></a></li>
            @endif
            {{-- Booking Report - > Start --}}
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'booking_report'))
                <li class="{{ Route::current()->uri() == 'admin/booking-report' ? 'active' : '' }}"><a
                        href="{{ url('admin/booking-report') }}"><i class="ri-coupon-2-line"></i>&nbsp;<span>Booking
                            Report</span></a></li>
            @endif
            {{-- Booking Report - > End --}}
            {{-- {{dd(Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_support'))}} --}}
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_support'))
                <li class="{{ Route::current()->uri() == 'admin/agent/ticket' ? 'active' : '' }}"><a
                        href="{{ url('admin/agent/ticket') }}"><i class="ri-coupon-3-line"></i><span>My
                            Tickets</span></a></li>
            @endif
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'translation'))
                <li class="{{ Route::current()->uri() == 'admin/translation/keywords/add' ? 'active' : '' }}"><a
                        href="{{ route('addtranskeywords') }}"><i class="ri-translate"></i> <span>Translation Key
                            Words</span></a>
                </li>
            @endif
            {{-- @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_logs'))
                <li class="{{ Route::current()->uri() == 'admin/logs' ? 'active' : '' }}"><a
                        href="{{ route('admin.log') }}"><i class="ri-translate-2"></i> <span>Logs</span></a>
                </li>
            @endif --}}
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_logs'))
                <li class="{{ Route::current()->uri() == 'admin/platform-logs' ? 'active' : '' }}">
                    <a href="{{ route('admin.platform.log') }}">
                        <i class="ri-news-line"></i>
                        <span>Platform Logs</span>
                    </a>
                </li>
            @endif
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'notifications'))
                <li class="{{ Route::current()->uri() == 'admin/notifications' ? 'active' : '' }}"><a
                        href="{{ route('admin.notifications.index') }}"><i class="ri-notification-3-line"></i>
                        <span>Notifications</span></a>
                </li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_metas'))
                <li class="{{ Route::current()->uri() == 'admin/settings/metas' ? 'active' : '' }}"><a
                        href="{{ route('admin.metaList') }}">
                        <i class="ri-seo-line"></i> <span>SEO Metas</span></a>
                </li>
            @endif
            {{-- Delete Requests - > Start --}}
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_admin'))
                <li class="{{ Route::current()->uri() == 'admin/delete-requests' ? 'active' : '' }}"><a
                        href="{{ url('admin/delete-requests') }}"><i class="ri-coupon-2-line"></i>&nbsp;<span>Delete
                            Requests</span></a></li>
            @endif
            {{-- Delete Requests - > End --}}

            {{-- Campaign Pages - > Start --}}
            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'campaign_pages'))
                <li class="{{ Route::current()->uri() == 'admin/campaign-pages' ? 'active' : '' }}"><a
                        href="{{ url('admin/campaign-pages') }}"><i class="ri-coupon-2-line"></i>&nbsp;<span>Campaign
                            Pages</span></a></li>
            @endif
            {{-- Delete Requests - > End --}}

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'manage_bookings'))
                <li class="{{ Route::current()->uri() == 'admin/tawuniya' ? 'active' : '' }}"><a
                        href="{{ url('admin/tawuniya') }}"><i
                            class="ri-coupon-2-line"></i>&nbsp;<span>Tawuniya</span></a></li>
            @endif

            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'properties'))
                <li class="{{ Route::current()->uri() == 'admin/property-management-requests' ? 'active' : '' }}"><a
                        href="{{ route('admin.property-management-requests.index') }}"><i
                            class="ri-price-tag-3-line"></i>&nbsp;<span>DarStay Service Requests</span></a></li>
            @endif
            

            {{-- <li class="{{ Route::current()->uri() == 'admin/settings/all' ? 'active' : '' }}"><a
                href="{{ url('admin/settings/all') }}"><i class="ri-dashboard-line"></i>&nbsp;<span>Settings</span></a>
        </li> --}}
            <!-- Email Template Ends -->
            {{-- @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'general_setting'))
			<li class="{{ (Request::segment(2) == 'settings') ? 'active' : ''  }}"><a href="{{ url('admin/settings') }}"><i class="fa fa-gears"></i>&nbsp;<span>Settings</span></a></li>
		@endif --}}
        </ul>
    </section>
</aside>
