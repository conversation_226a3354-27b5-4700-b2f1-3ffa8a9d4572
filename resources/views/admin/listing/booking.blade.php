@extends('admin.template')
@section('main')
    <div class="content-wrapper">
        <!-- Main content -->
        <section class="content-header">
            <h1>
                Booking
                <small>Booking</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{{ url('/') }}/admin/dashboard"><i class="fa fa-dashboard"></i> Home</a></li>
            </ol>
        </section>

        <section class="content">
            <div class="row">
                <div class="col-md-3 settings_bar_gap">
                    @include('admin.common.property_bar')
                </div>
                <div class="col-md-9">
                    <div class="box box-info">
                        <div class="box-body">
                            <form method="post" action="{{ url('admin/listing/' . $result->id . '/' . $step) }}"
                                class='signup-form login-form' accept-charset='UTF-8'>
                                {{ csrf_field() }}
                                <div class="row">
                                    <div class="col-md-12">
                                        <h3>{{ customTrans('listing_book.booking_title') }}</h3>
                                        <p class="text-muted">{{ customTrans('listing_book.booking_data') }}.</p>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="col-md-12 min-height-div">
                                                <label class="label-large">{{ customTrans('listing_book.booking_type') }}
                                                    <span class="text-danger">*</span></label>
                                                <select name="booking_type" id="select-booking_type" class="form-control">
                                                    <option value="request"
                                                        {{ $result->booking_type == 'request' ? 'selected' : '' }}>
                                                        {{ customTrans('listing_book.review_request') }}</option>
                                                    <option value="instant"
                                                        {{ $result->booking_type == 'instant' ? 'selected' : '' }}>
                                                        {{ customTrans('listing_book.guest_instant') }}</option>
                                                </select>

                                                <!-- platform type -->
                                                <label class="label-large">{{ customTrans('listing_book.platform_type') }}
                                                    <span class="text-danger">*</span></label>
                                                    @php
                                                        $platforms = DB::table('platforms')
                                                            ->where('status', true)
                                                            ->where('type', 1)
                                                            ->where('id', '!=', 10)
                                                            ->get();
                                                    @endphp
                                                <select name="platform_id" id="select-platform_id" class="form-control">
                                                    <option value="0">Darent</option>
                                                    @foreach($platforms as $platform)
                                                    <option value="{{ $platform->id }}" {{ $result->platform_id == $platform->id ? 'selected' : '' }}>
                                                        {{ $platform->name }}
                                                    </option>
                                                @endforeach
                                                </select>
                                                <!-- end platform type -->
                                                 <div id="third-party-listing">
                                                     <label class="label-large">Third Party Listing ID
                                                         <span class="text-danger">*</span></label>
                                                     <input type="text" name="thirdparty_listing" value="{{$result->thirdparty_listing}}" id="thirdparty_listing" class="form-control">
                                                 </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="clear-both"></div>
                                    <br>
                                    <div class="col-md-12">
                                        <div class="col-md-9 col-sm-6 col-xs-6 l-pad-none text-left">
                                            <a data-prevent-default=""
                                                href="{{ url('admin/listing/' . $result->id . '/nightsandtime') }}"
                                                class="btn btn-large btn-primary">{{ customTrans('listing_description.back') }}</a>
                                        </div>
                                        <div class="col-md-3 col-sm-6 col-xs-6 text-right">
                                                @if ($result->rejection_reason)
                                                    <button id="reject_btn"
                                                        class="btn btn-large btn-danger next-section-button reject-property-warning"
                                                        disabled>
                                                        Rejected
                                                    </button>
                                                @else
                                                    <a id="reject_btn"
                                                        class="btn btn-large btn-danger next-section-button reject-property-warning">Reject
                                                    </a>
                                                @endif
                                                {{-- @if($result->license_verified_at == NULL && ($result->property_type == 1 || $result->property_type == 9)) --}}
                                                <!-- @if($result->license_verified_at == NULL && ($result->property_type == 1 || $result->property_type == 9))
                                                <p style="color: red;" >License is not verified
                                                </p>
                                                @else
                                                <button id="complete_btn" type="submit"
                                                    class="btn btn-large btn-primary next-section-button">Complete
                                                </button>
                                                @endif -->
                                                <button id="complete_btn" type="submit"
                                                    class="btn btn-large btn-primary next-section-button">Complete
                                                </button>
                                                <!-- @if($result->property_type == 22 || $result->property_address->city == "AlUla") -->
                                                
                                                <!-- @else
                                                <p style="color: red;" >License is not verified </p>
                                                @endif -->




                                        </div>
                                    </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@stop
@push('scripts')
    <script type="text/javascript">
        // webengage_admin.user.logout();
        webengage.user.login("{{ $result->users->uuid }}"); //9SBOkLVMWvPX is the unique user identifier being used here
        $('#complete_btn').on('click', function(e) {
            //-----------WebEngage Integration------------

            let today = new Date().toISOString().split('T')[0];

            let payload2 = {
                "Approval Date": today,
                "Property Code": "{{ $result->property_code }}",
                "Host ID": "{{ $result->users->uuid }}",
                "Host id": "{{ $result->users->uuid }}"
            }
            // webEngageTracking(HOST_PROPERTY_APPROVED, payload2);
            webengage.track("Host Property Approved", payload2);
            //-----------WebEngage Integration------------

            // setTimeout(() => {
            //     webengage_admin.user.logout();
            // }, 500);

        });

        $('#reject_btn').on('click', function(e) {
            $('#rejection_reason_error').html('')
        });

        $('#reject-property-modal-yes').on('click', function(e) {
            $('#rejection_reason_error').html('')
            let rejection_reason = $('#rejection_reason').val()
            $.ajax({
                type: "POST",
                url: APP_URL + "/admin/reject-property",
                data: {
                    "_token": "{{ csrf_token() }}",
                    'rejection_reason': $('#rejection_reason').val(),
                    'property_id': "{{ $result->id }}",
                },
                success: function(result) {
                    if (result.status == false) {
                        $('#rejection_reason_error').html(result.rejection_reason_error)
                    } else {
                        //-----------WebEngage Integration------------
                        let today = new Date().toISOString().split('T')[0];

                        let payload = {
                            "Host id": "{{ $result->users->uuid }}",
                            "Unit Code": "{{ $result->property_code }}",
                            "Rejection Date": today,
                            "Rejection Reason": rejection_reason
                        }
                        webEngageTracking(HOST_PROPERTY_REJECTED, payload);
                        //-----------WebEngage Integration------------
                        window.location.href = APP_URL + "/admin/properties";
                    }
                }
            });
        });
    </script>

<script>
    $(document).ready(function() {
        $('#select-platform_id').on('change', function() {

            if ($(this).val() == 5 || $(this).val() == 4 || $(this).val() == 6) {  // Check if Silkhaus is selected
                var listingID= '{{$result->thirdparty_listing}}';
                $('#third-party-listing').removeClass('d-none');
                $('#thirdparty_listing').val(listingID);
                $('#thirdparty_listing').attr('required', 'required');
            } else {
                $('#third-party-listing').addClass('d-none');  // Hide the input
                $('#thirdparty_listing').val('');  // Hide the input
                $('#thirdparty_listing').removeAttr('required');
            }
        });

        // Trigger the change event on page load to handle pre-selected values
        $('#select-platform_id').trigger('change');
    });
</script>
@endpush
