﻿@extends('admin.template')

@push('css')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="{{ asset('css/bootstrap-datetimepicker.css') }}">
@endpush
@php
if( $license->license_expiry ){
    $date = explode("-",$license->license_expiry);
    $gregorianDate = $license->license_expiry ? \GeniusTS\HijriDate\Hijri::convertToGregorian($date[2],$date[1],$date[0],-1)->format('Y-m-d') : '';
}

@endphp
@section('main')
    <div class="content-wrapper">
        <!-- Main content -->
        <section class="content-header">
            <h1>
                License
                <small>License</small>
            </h1>
            <ol class="breadcrumb">
                <li>
                    <a href="{{ url('/') }}/admin/dashboard"><i class="fa fa-dashboard"></i> Home</a>
                </li>
            </ol>
        </section>

        <section class="content">
            <div class="col-md-3 settings_bar_gap">
                @include('admin.common.property_bar')
            </div>

            <div class="col-md-9">
                <form id="list_des" method="post" action="{{ url('admin/listing/' . $result->id . '/' . $step) }}"
                    enctype="multipart/form-data" class='signup-form login-form' accept-charset='UTF-8'>
                    {{ csrf_field() }}

                    <div class="box box-info">
                        <div class="box-body">
                            <div class="license-option-main d-flex align-items-center justify-content-center mb-3">
                                <div class="license-option">
                                    <div class="license-option">
                                        <input type="radio" id="option-b" name="license_is_company" value="0"
                                            onclick="toggleInputs()"
                                            {{ old('license_is_company', $license->license_is_company ?? 0) == 0 ? 'checked' : '' }}>
                                        <label for="option-b">Individual</label>
                                        <input type="radio" id="option-a" name="license_is_company" value="1"
                                            onclick="toggleInputs()"
                                            {{ old('license_is_company', $license->license_is_company ?? 0) == 1 ? 'checked' : '' }}>
                                        <label for="option-a">Company</label>
                                    </div>
                                </div>
                            </div>

                            <!-- License Number and CR Number (For Option A) -->
                            <div class="row" id="option-a-inputs">
                                <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                    <label class="label-large">{{ customTrans('listing.licensenumber') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="text" name="license_no" class="form-control license_no"
                                        value="{{ old('license_is_company', $license->license_is_company) == 1 ? old('license_no', $license->license_no) : '' }}"
                                        placeholder="{{ customTrans('listing.licensenumber') }}" id="license_no">
                                    <span class="text-danger">
                                        @if (old('license_is_company', $license->license_is_company) == 1)
                                            {{ $errors->first('license_no') }}
                                        @endif
                                    </span>
                                </div>
                                <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                    <label class="label-large">{{ customTrans('listing.crnumber') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="text" name="cr_no" class="form-control cr_no"
                                        value="{{ old('license_is_company', $license->license_is_company) == 1 ? old('cr_no', $license->cr_no) : null }}"
                                        placeholder="{{ customTrans('listing.crnumber') }}" id="cr_no">
                                    <span class="text-danger">
                                        @if (old('license_is_company', $license->license_is_company) == 1)
                                            {{ $errors->first('cr_no') }}
                                        @endif
                                    </span>
                                </div>
                                <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                    <label class="label-large">{{ customTrans('listing.companyname') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="text" name="license_company_name"
                                        class="form-control license_company_name"
                                        value="{{ old('license_is_company', $license->license_is_company) == 1 ? old('license_company_name', json_decode($license->license_company_name, true)[session()->get('language', 'en')] ?? null) : null }}"
                                        placeholder="{{ customTrans('listing.companyname') }}" id="license_company_name">
                                    <span class="text-danger">
                                        @if (old('license_is_company', $license->license_is_company) == 1)
                                            {{ $errors->first('license_company_name') }}
                                        @endif
                                    </span>
                                </div>
                                <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                    {{-- @if (isset($license) && $license->license_expiry != null && $license->license_is_company == 1)
                                        <label class="label-large">{{ customTrans('listing.expirydate') }} <span
                                                class="text-danger">*</span></label>
                                        <input type="text" class="form-control cr_no" value="{{ $gregorianDate }}"
                                            placeholder="{{ customTrans('listing.idnumber') }}" id="cr_no">
                                    @else --}}
                                        <div class="row">
                                            <div class="col-md-6 col-sm-12">
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="dateType_elm-dob"
                                                        id="arabicRadio_elm-dob" value="1"
                                                        @if (!empty($elm_data)) disabled @endif>
                                                    <label class="form-check-label" for="arabicRadio_elm-dob">
                                                        Hijri Calendar <span class="text-danger">*</span>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6 col-sm-12">
                                                <div class="form-check form-check-inline" style="margin-left: 15px">
                                                    <input class="form-check-input" type="radio" name="dateType_elm-dob"
                                                        id="nonArabicRadio_elm-dob" value="0"
                                                        value="{{ old('license_is_company', $license->license_is_company) == 1 ? old('license_expiry', $license->license_expiry) : null }}"
                                                        checked
                                                        @if (!empty($elm_data)) disabled @endif>
                                                    <label class="form-check-label" for="nonArabicRadio_elm-dob">
                                                        Georgian Calendar <span class="text-danger">*</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="single-check mb-4 mt-2">
                                                <input type="text" name="license_expiry"
                                                    value="{{ old('license_is_company', $license->license_is_company) == 1 ? old('license_expiry', $license->license_expiry) : null }}"
                                                    class="form-control" id="elm-dob"
                                                    @if (!empty($license->license_expiry)) readonly @endif />
                                                <label for="elm-dob" class="check-icon cy-positon md-clndr-icon"
                                                    style="left: unset; right: 7px; !important; text-align: end;">
                                                    <img src="{{ asset('icons/calender.svg') }}" alt="">
                                                </label>
                                                <span id="dob-error"
                                                    class="text-danger">{{ $errors->first('license_expiry') ?? '' }}</span>
                                            </div>
                                        </div>
                                    {{-- @endif --}}
                                    {{-- <label class="label-large">{{ customTrans('listing.expirydate') }} </label>
                                    <input type="date" name="license_expiry" class="form-control license_expiry"
                                        value="{{ old('license_is_company', $license->license_is_company) == 1 ? old('license_expiry', $license->license_expiry) : null }}"
                                        placeholder="{{ customTrans('listing.expirydate') }}" id="license_expiry">
                                    <span class="text-danger">
                                        @if (old('license_is_company', $license->license_is_company) == 1)
                                            {{ $errors->first('license_expiry') }}
                                        @endif
                                    </span> --}}
                                </div>
                                <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                    {{-- @if ($license->license_is_company == 1 && $license->license_document != null)
                                        <div style="display: flex; align-items: center; justify-content: space-between;">
                                    @endif
                                    <label class="label-large">{{ customTrans('listing.document') }}</label>
                                    @if ($license->license_is_company == 1 && $license->license_document != null)
                                        <a href="{{ asset($license->license_document) }}" target="_blank">View
                                            Document</a>
                                    @endif --}}
                                </div>
                                {{-- <input type="file" name="license_document" class="form-control license_document"
                                    placeholder="{{ customTrans('listing.document') }}" id="license_document"
                                    accept="image/*,application/pdf">
                                <span class="text-danger">
                                    @if (old('license_is_company', $license->license_is_company) == 0)
                                        {{ $errors->first('license_document') }}
                                    @endif
                                </span> --}}
                            </div>
                        </div>

                        <!-- Permit Number and ID Number (For Option B) -->
                        <div class="row" id="option-b-inputs" style="display:none;">
                            <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                <label class="label-large">{{ customTrans('listing.permitnumber') }} <span
                                        class="text-danger">*</span></label>
                                <input type="text" name="license_no" class="form-control license_no"
                                    value="{{ old('license_is_company', $license->license_is_company) == 0 ? old('license_no', $license->license_no) : null }}"
                                    placeholder="{{ customTrans('listing.permitnumber') }}" id="license_no">
                                <span class="text-danger">
                                    @if (old('license_is_company', $license->license_is_company) == 0)
                                        {{ $errors->first('license_no') }}
                                    @endif
                                </span>
                            </div>
                            <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                <label class="label-large">{{ customTrans('listing.idnumber') }} <span
                                        class="text-danger">*</span></label>
                                <input type="text" name="cr_no" class="form-control cr_no"
                                    value="{{ old('license_is_company', $license->license_is_company) == 0 ? old('cr_no', $license->cr_no) : null }}"
                                    placeholder="{{ customTrans('listing.idnumber') }}" id="cr_no">
                                <span class="text-danger">
                                    @if (old('license_is_company', $license->license_is_company) == 0)
                                        {{ $errors->first('cr_no') }}
                                    @endif
                                </span>
                            </div>

                            <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                {{-- @if (isset($license) && $license->license_expiry != null)
                                    <label class="label-large">{{ customTrans('listing.expirydate') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control cr_no" value="{{ $gregorianDate }}"
                                        placeholder="{{ customTrans('listing.idnumber') }}" id="cr_no">
                                @else --}}
                                    <div class="row">
                                        <div class="col-md-6 col-sm-12">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="dateType_elm-dob-2"
                                                    id="arabicRadio_elm-dob-2" value="1"
                                                    value="{{ old('license_is_company', $license->license_is_company) == 0 ? old('license_expiry', $license->license_expiry) : null }}"
                                                    @if (!empty($elm_data)) disabled @endif>
                                                <label class="form-check-label" for="arabicRadio_elm-dob-2">
                                                    Hijri Calendar <span class="text-danger">*</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-12">
                                            <div class="form-check form-check-inline" style="margin-left: 15px">
                                                <input class="form-check-input" type="radio" name="dateType_elm-dob-2"
                                                    checked id="nonArabicRadio_elm-dob-2" value="0"
                                                    @if (!empty($elm_data)) disabled @endif>
                                                <label class="form-check-label" for="nonArabicRadio_elm-dob-2">
                                                    Georgian Calendar <span class="text-danger">*</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="single-check mb-4 mt-2">
                                            <input type="text" name="license_expiry"
                                                value="{{ old('license_is_company', $license->license_is_company) == 0 ? old('license_expiry', $license->license_expiry) : null }}"
                                                class="form-control" id="elm-dob-2"
                                                @if (!empty($license->license_expiry)) readonly @endif />
                                            <label for="elm-dob-2" class="check-icon cy-positon md-clndr-icon"
                                                style="left: unset; right: 7px; !important; text-align: end;">
                                                <img src="{{ asset('icons/calender.svg') }}" alt="">
                                            </label>
                                            <span id="dob-error"
                                                class="text-danger">{{ $errors->first('license_expiry') ?? '' }}</span>
                                        </div>
                                    </div>
                                {{-- @endif --}}
                                {{-- <label class="label-large">{{ customTrans('listing.expirydate') }} <span
                                        class="text-danger">*</span></label>
                                <input type="date" name="license_expiry" class="form-control license_expiry"
                                    value="{{ old('license_is_company', $license->license_is_company) == 0 ? old('license_expiry', $license->license_expiry) : null }}"
                                    placeholder="{{ customTrans('listing.expirydate') }}" id="license_expiry">
                                <span class="text-danger">
                                    @if (old('license_is_company', $license->license_is_company) == 0)
                                        {{ $errors->first('license_expiry') }}
                                    @endif
                                </span> --}}
                            </div>
                            <div class="col-md-6 col-sm-12 col-xs-12 mb20">
                                {{-- @if ($license->license_is_company == 0 && $license->license_document != null)
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                @endif
                                <label class="label-large">{{ customTrans('listing.document') }}</label>
                                @if ($license->license_is_company == 0 && $license->license_document != null)
                                    <a href="{{ asset($license->license_document) }}" target="_blank">View Document</a>
                                @endif --}}
                            </div>
                            {{-- <input type="file" name="license_document" class="form-control license_document"
                                placeholder="{{ customTrans('listing.document') }}" id="license_document"
                                accept="image/*,application/pdf">
                            <span class="text-danger">
                                @if (old('license_is_company', $license->license_is_company) == 0)
                                    {{ $errors->first('license_document') }}
                                @endif
                            </span> --}}
                        </div>

                        <p class="text-success mb20" @if ($license->license_is_company == 0 && $license->license_verified_at == null) style="display:none;" @endif>
                            <strong>License Verified Sucessfully</strong>
                        </p>
                    </div>

                    @if (session('error'))
                        <p class="error text-danger">{{ session('error') }}</p>
                    @endif
                    @if($errors->any())
                        {{ implode('', $errors->all('<div>:message</div>')) }}
                    @endif

                    <div style="display: flex; align-items: center;" class="form-switch">
                        <div class="permit-switch">
                            <input type="checkbox" id="permit_by_darent" name="permit_by_darent"
                                onchange="updatePermit(this)" value="1" role="switch"
                                @if ($result->permit_by_darent == 1) checked @endif />
                            <label for="permit_by_darent"></label>
                        </div>
                        <p class="mb-0 ml-2" style="margin-left: 7px;">Permit by Darent</p>
                    </div>
                    <br>


                    <div class="row">
                        <div class="col-md-6  col-sm-6 col-xs-6 text-left">
                            <a data-prevent-default="" href="{{ url('admin/listing/' . $result->id . '/basics') }}"
                                class="btn btn-large btn-primary">{{ customTrans('listing_description.back') }}</a>
                        </div>
                        <div class="col-md-6 col-sm-6 col-xs-6 text-right">
                            <button type="button" class="btn btn-large btn-primary next-section-button" data-toggle="modal"
                            data-target="#ClearConfirmation">
                            Clear
                            </button>
                            <button type="button" class="btn btn-default btn-sm">
                                <a href="{{ url('admin/listing/' . $result->id . '/booking') }}" class=""
                                    style="color: #000; text-decoration: none;">
                                    {{ customTrans('listing_basic.skip') }}
                                </a>
                            </button>
                            <button type="submit" class="btn btn-large btn-primary next-section-button">
                                    {{ customTrans('listing_basic.next') }}
                            </button>
                            <!-- <button type="button" class="btn btn-default btn-sm">
                                <a href="{{ url('admin/listing/' . $result->id . '/booking') }}" class=""
                                    style="color: #000; text-decoration: none;">
                                    {{ customTrans('listing_basic.next') }}
                                </a>
                            </button> -->
                            <!-- @if ($result->property_type == 22 || $result->property_address->city == "AlUla") -->
                                
                            <!-- @else
                                <button type="submit" class="btn btn-large btn-primary next-section-button">
                                    {{ customTrans('listing_basic.next') }}
                                </button>
                            @endif -->

                        </div>
                    </div>
            </div>
    </div>
    </form>

    </div>
    </section>
    <!-- /.content -->
    <div class="clearfix"></div>
    </div>

    {{-- success moda --}}
    <div class="modal fade admin-succces-modal" id="successModal" tabindex="-1" role="dialog"
        aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h4 class="modal-title w-100 font-weight-bold">Success</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body mx-3">
                    <!-- Success Message -->
                    <p class="text-center mb-0">Permit by Darent successfully!</p>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>


    {{-- Clear Confirmation Modal --}}
    <div class="modal fade" id="ClearConfirmation" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h4 class="modal-title w-100 font-weight-bold">Confirmation</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body mx-3">
                    <form action="{{ route('clearLicense') }}" method="post">
                        @csrf
                        <div class="md-form mb-5">
                            <p>Are You Sure You Want to Clear?</p>
                        </div>

                        <input type="hidden" id="id" name="id" value="{{$result->id}}">

                        <div class="modal-footer d-flex justify-content-center">
                            <button type="submit" class="btn btn-deep-orange">Submit</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
    {{-- Clear Confirmation Modal End --}}
@stop

@section('validate_script')
    <script src="https://cdn.jsdelivr.net/npm/moment-hijri@2.1.2/moment-hijri.min.js"></script>
    <script src="{{ asset('js/bootstrap-hijri-datetimepicker.js') }}"></script>
    <script type="text/javascript">
        $(document).ready(function() {

            $('#list_des').validate({
                rules: {
                    license_no: {
                        required: true,
                    },
                    cr_no: {
                        required: true
                    }
                }
            });

            function initializeHijriDatePicker(elmId, isHijri) {
                let elm = null;

                function changeTypeCal(hijri = true) {
                    if (elm) {
                        let newClass = hijri ? "hijri-date-input" : "geo-date-input";
                        let oldClass = hijri ? "geo-date-input" : "hijri-date-input";
                        const placeHolder = hijri ?
                            @json(customTrans('listing.hijricalendar')) :
                            @json(customTrans('listing.georgiancalendar'));

                        elm.data("HijriDatePicker").destroy();
                        const dobEl = document.getElementById(elmId);

                        if (dobEl.classList.contains(oldClass)) {
                            dobEl.classList.remove(oldClass);
                        }

                        dobEl.classList.add(newClass);
                        dobEl.value = ""; // Clear the value
                        dobEl.placeholder = placeHolder; // Set the placeholder
                    }

                    if (hijri) {
                        elm = $(`#${elmId}`).hijriDatePicker({
                            locale: "{{ app()->getLocale() == 'en' ? 'en-US' : 'ar-SA' }}",
                            hijriFormat: "iYYYY-iMM-iDD",
                            format: hijri ? "iYYYY-iMM-iDD" : "YYYY-MM-DD",
                            showSwitcher: false,
                            hijri: hijri,
                        });
                    } else {
                        elm = $(`#${elmId}`).hijriDatePicker({
                            locale: "{{ app()->getLocale() == 'en' ? 'en-US' : 'ar-SA' }}",
                            format: "YYYY-MM-DD",
                            showSwitcher: false,
                            hijri: false,
                        });
                    }
                }

                // Initial load
                const initialHijri = $(`#arabicRadio_${elmId}`).is(':checked');
                const dobEl = document.getElementById(elmId);
                dobEl.placeholder = initialHijri ?
                    @json(customTrans('listing.hijricalendar')) :
                    @json(customTrans('listing.georgiancalendar'));

                // Initialize the date picker
                changeTypeCal(initialHijri);

                // Change event
                $(`#arabicRadio_${elmId}, #nonArabicRadio_${elmId}`).change(function() {
                    changeTypeCal($(`#arabicRadio_${elmId}`).is(':checked'));
                });
            }

            // Usage for multiple elements
            initializeHijriDatePicker("elm-dob", true);
            initializeHijriDatePicker("elm-dob-2", false);
        });
    </script>

    @push('scripts')
        <script>
            function updatePermit(element) {
                $.ajax({
                    type: "POST",
                    url: "{{ route('admin.update.permit_by_darent') }}",
                    data: {
                        _token: "{{ csrf_token() }}",
                        property_id: "{{ $result->id }}",
                        permit: element.checked ? 1 : 0
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.permit == 1) $('#successModal').modal('show');
                    },
                });
            }

            $(document).ready(function() {
                toggleInputs();
            });

            $('.translate-description').on("click", function(e) {
                e.preventDefault();
                let ar_des = $('#summary_ar').val();
                let en_des = $('#summary').val();
                let translateTo = $(this).data('translateto');
                let description = translateTo == 'ar' ? en_des : ar_des;
                let dataURL = "{{ route('trans.englishnArabic') }}";


                let ele = $(this);

                $.ajax({
                    url: dataURL,
                    cache: false,
                    type: 'GET',
                    data: {
                        sentence: description,
                        translateTo: translateTo
                    },
                    beforeSend: function() {
                        if (translateTo == 'ar') {
                            $(".loadergif-toArb").removeClass('d-none');
                        } else {
                            $(".loadergif-toEng").removeClass('d-none');
                        }
                        ele.addClass('d-none');
                    },
                    success: function(result) {
                        console.log(result.data);
                        if (translateTo == 'ar') {
                            $('#summary_ar').val(result.data);
                            $(".loadergif-toArb").addClass('d-none');
                            $('#summary_ar').trigger('change');
                        } else {
                            $('#summary').val(result.data);
                            $(".loadergif-toEng").addClass('d-none');
                            $('#summary').trigger('change');
                        }
                        ele.removeClass('d-none');
                    },
                    error: function(request, error) {

                        if (translateTo == 'ar') {
                            $(".loadergif-toArb").addClass('d-none');
                        } else {
                            $(".loadergif-toEng").addClass('d-none');
                        }
                        ele.removeClass('d-none');
                    },
                    complete: function() {
                        if (translateTo == 'ar') {
                            $(".loadergif-toArb").addClass('d-none');
                        } else {
                            $(".loadergif-toEng").addClass('d-none');
                        }
                        ele.removeClass('d-none');
                    }
                });




            });

            function updateCharacterCount(inputElement, characterCountElement) {
                let characterCount = inputElement.val().length,
                    minimum = 0,
                    maximum = 1000,
                    remaining = maximum - characterCount;

                characterCountElement.text(characterCount + '/' + maximum);
            }

            function setupCharacterCount(elementSelector, characterCountClass) {
                $(elementSelector).on('input change', function() {
                    let inputElement = $(this),
                        characterCountElement = inputElement.siblings(characterCountClass);

                    updateCharacterCount(inputElement, characterCountElement);
                });
            }

            setupCharacterCount('#summary_ar', '.character-count-display_ar');
            setupCharacterCount('#summary', '.character-count-display');

            function toggleInputs() {
                const optionAInputs = document.getElementById('option-a-inputs');
                const optionBInputs = document.getElementById('option-b-inputs');
                const optionA = document.getElementById('option-a').checked;
                const optionB = document.getElementById('option-b').checked;

                if (optionA) {
                    optionAInputs.style.display = 'block';
                    optionBInputs.style.display = 'none';
                    disableInputs('option-b'); // Disable Option B inputs
                    enableInputs('option-a'); // Enable Option A inputs
                } else if (optionB) {
                    optionAInputs.style.display = 'none';
                    optionBInputs.style.display = 'block';
                    disableInputs('option-a'); // Disable Option A inputs
                    enableInputs('option-b'); // Enable Option B inputs
                }
            }

            function enableInputs(option) {
                const inputs = document.querySelectorAll(`#${option}-inputs input`);
                inputs.forEach(input => {
                    input.disabled = false;
                });
            }

            function disableInputs(option) {
                const inputs = document.querySelectorAll(`#${option}-inputs input`);
                inputs.forEach(input => {
                    input.disabled = true;
                });
            }
        </script>
    @endpush
@endsection
