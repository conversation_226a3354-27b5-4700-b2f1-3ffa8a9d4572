@extends('mobile.template')
@section('main')
    @push('css')
        <script>
            document.documentElement.style.overflow = 'hidden'
        </script>
        <link rel="stylesheet"
              href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
    @endpush
    {{-- {{ dd($__data) }} --}}
    @php
        $wishlist_modal = true;

        use App\Models\Settings;
        use App\Models\Properties;
        use App\Models\WishlistName;

        $wishlistExist = WishlistName::where('creater_id', Auth::id())->get();
        if (isset($wishlistExist)) {
            $existWishlistModal = true;
            $wishlist_modal = true;
        } else {
            $existWishlistModal = false;
            $wishlist_modal = true;
        }
    @endphp

    {{-- <div class="map-float"> // comnet becz of darent
        <button class="map-fl showMap" id="showMap">
            <img src="{{ 'icons/show-map-mb.svg' }}" alt="">
        </button>
        <button class="map-fl closeMap d-none" id="closeMap">
            <img src="{{ 'icons/show-list-mb.svg' }}" alt="">
        </button>
    </div> --}}
    <section class="services mt-3">


        <div class="container fl-container">

            <div class="cstm-property">
                <div class="row">
                    <div class="aft-map grid">
                        <div class="property">

                            {{-- <div id="loader" class="d-none loader-img text-center">
                                <div>
                                    <img src="{{ asset('icons/loader.gif') }}" alt="loader">
                                    <h4>{{ app()->getLocale() == 'ar' ? 'جار التحميل' : ' Loading...' }}</h4>
                                </div>
                            </div> --}}

                            <div class="row" id="properties_show">
                                {{-- SEARCH RESULT WILL SHOWN HERE --}}
                            </div>
                        </div>
                        <div id="loader-more" class="d-none loader-img text-center pt-4">
                            <div>
                                {{-- <img src="{{ asset('icons/loader.gif') }}" alt="loader"> --}}
                                <div class="dots"></div>
                                <h4>{{ app()->getLocale() == 'ar' ? 'جار التحميل' : ' Loading...' }}</h4>
                            </div>
                        </div>
                        <!-- Pagination start -->

                    </div>
                    <div id="map_view" class="map-view">
                    </div>
                </div>
            </div>


        </div>
        {{-- Wishlits Listing Modal --}}
        <div class="modal fade dubai-ff" id="whishlist-listing" data-bs-backdrop="static" data-bs-keyboard="false"
             tabindex="-1" aria-labelledby="whishlist-listing" aria-hidden="true">
            <div
                class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable bt-grey-scroller">
                <div class="modal-content cm-bd-content">
                    <div class="modal-header cm-bd-header">
                        <h5 class="w-100 text-center mb-0" id="whishlist-listing">
                            {{ customTrans('wishlist.your_wishlists') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body cm-bd-body">
                        <form>
                            {{ csrf_field() }}
                            <input type="hidden" name="property_id" id="wishlist-property-id" value="">
                            {{-- {{dd($wishlistExist)}} --}}
                            @forelse ($wishlistExist as $wishlist)
                                <div class="wishlist-listing">
                                    @php
                                        $wishlistProperties = $wishlist->wishlistProperties;
                                    @endphp
                                    @if (count($wishlistProperties) > 0)
                                        <div class="wlisting-inner" id="existWishlist"
                                             data-wishlist-name-id="{{ $wishlist->id }}">
                                            <img
                                                src="{{ asset($wishlistProperties[0]->property->getCoverPhotoAttribute()) }}"
                                                alt="">
                                            <h5 class="mb-0 ellipsis-oneline">{{ $wishlist->name }}</h5>
                                        </div>
                                    @else
                                        <div class="wlisting-inner" id="existWishlist"
                                             data-wishlist-name-id="{{ $wishlist->id }}">
                                            <img src="{{ asset('images/default-image-not-exist.png') }}" alt="">
                                            <h5 class="mb-0 ellipsis-oneline">{{ $wishlist->name }}</h5>
                                        </div>
                                    @endif
                                </div>
                            @empty
                                <p class="mb-0 text-center">To save your favorite places
                                    and Experiences to a wishlist.
                                </p>
                            @endforelse
                        </form>
                    </div>
                    <div class="modal-footer cm-bd-footer">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                data-bs-target="#create-whishlist">{{ customTrans('wishlist.create_new_wishlist') }}</button>
                    </div>
                </div>
            </div>
        {{-- Wishlits Listing Modal End --}}
    </section>

@stop
{{-- {{ dd(auth()->user()) }} --}}
@push('jslibs')
    <script type="text/javascript" src="{{ asset('js/bootstrap-slider.min.js') }}"></script>
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>
@endpush
@push('scripts')
    {{-- <script src="{{ asset('cdns/js/daterangepicker.min.js') }}"></script> --}}
    <script type="text/javascript">
      $(document).ready(function() {
            $('#float-form').submit(function(e) {
                e.preventDefault();
                const urlParams = new URLSearchParams(window.location.search);
                const formData = $(this).serializeArray();
                formData.forEach(item => {
                    urlParams.set(item.name, item.value);
                });

                const baseUrl = window.location.pathname;
                const newUrl = baseUrl + '?' + urlParams.toString();

                window.location.href = newUrl;
            });
        });

        var loadPage = "{{ route('search.resultV2') }}"

        $(document).ready(function () {
            localStorage.removeItem('selectDistrictArray') // Removing Districts from local storage

            function updateQueryParams(param, value) {
                // get current search
                const search = document.location.search.replace('?', '')
                // update it
                let chunks = search.split('&')
                if (!chunks.find((chunk) => chunk.startsWith(param + '='))) {
                    chunks.push(param + '=')
                }

                chunks = chunks.filter((item) => !!item).map((chunk) => {
                    return chunk.startsWith(param + '=') ? (param + '=' + value) : chunk
                })

                // glow everything to one string
                chunks = chunks.join('&')
                // push to history
                window.history.pushState({}, '', '?' + chunks)
            }

            var markers = []
            var allowRefresh = true
            var currentInfoWindow = null
            var sortBy = (new URLSearchParams(window.location.search)).get('sort')
            var rb = (new URLSearchParams(window.location.search)).get('rb') || 'Any'
            var rbtwo = (new URLSearchParams(window.location.search)).get('rbtwo') || 'Any'
            var rbthree = (new URLSearchParams(window.location.search)).get('rbthree') || 'Any'
            var d = (new URLSearchParams(window.location.search)).get('d') || ''
            var pc = (new URLSearchParams(window.location.search)).get('pc') || ''
            var a = (new URLSearchParams(window.location.search)).get('a') || ''

            function setMapOnAll(map) {
                for (var i = 0; i < markers.length; i++) {
                    markers[i].setMap(map)
                }
            }

            function clearMarkers() {
                setMapOnAll(null)
            }

            function deleteMarkers() {
                clearMarkers()
                markers = []
            }

            var finished = false
            var isLoading = false
            var page = 1
            var upcomingresult = 0

            function getPropertiesV2(onLoad = true) {
                if (!loadPage) {
                    return
                }
                let range = (new URLSearchParams(window.location.search)).get('range') || '';
                let d = (new URLSearchParams(window.location.search)).get('d') || null;

                let property_type_id = (new URLSearchParams(window.location.search)).get('property_type') || null;
                if(property_type_id){
                    $(`#property_type_${property_type_id}`).prop('checked', true);
                    $(`#property_type${property_type_id}`).prop('checked', true);
                }
                let a = (new URLSearchParams(window.location.search)).get('a') || null;
                if (a) {
                    const inputs = a.split(',')
                    
                    for (const input of inputs) {
                        $('input[name="amenities1[]"][value="' + input + '"]').prop('checked', true)
                        
                    }
                }

                let guestParam = (new URLSearchParams(window.location.search)).get('guest') || null;


                if (range === '') {
                    range = $('#price-range').attr('data-value')
                }

                const range_parts = range.split(',')
                var location = $('#front-search-field').val()

                $('#header-search-form').val(location)
                var searchNights = '{{ $searchNights }}'
                var min_price = range_parts[0]
                var max_price = range_parts[1]
                $('#minPrice').html(min_price)
                $('#maxPrice').html(max_price)

                var amenities = getCheckedValueArray('amenities')
                var property_type = getCheckedValueArray('property_type')
                var book_type = getCheckedValueArray('book_type')
                var space_type = getCheckedValueArray('space_type')
                var bedrooms = $('#map-search-min-bedrooms').val()
                var beds = $('#map-search-min-beds').val()
                var bathrooms = $('#map-search-min-bathrooms').val()
                var checkin = $('#startDate').val()
                var checkout = $('#endDate').val()
                var guest = $('#front-search-guests').val()
                var adult_guest = $('input[name="adult_guest"]').val()
                var child_guest = $('input[name="child_guest"]').val()
                var property_code = $('#property_code').val()
                var districts = $('select[name="districts"]').val()
                if (d != null && onLoad) {
                    districts = d.split(',')
                }

                let selectDistrictArray = []
                if ($('#more_filters').css('display') != 'none') {
                    $.ajax({
                        url: loadPage,
                        cache: false,
                        type: 'post',
                        data: {
                            '_token': "{{ csrf_token() }}",
                            'location': location,
                            'min_price': min_price,
                            'max_price': max_price,
                            'amenities': amenities,
                            'property_type': property_type,
                            'book_type': book_type,
                            'space_type': space_type,
                            'beds': beds,
                            'bathrooms': bathrooms,
                            'bedrooms': bedrooms,
                            'checkin': checkin,
                            'checkout': checkout,
                            'searchNights': searchNights,
                            'guest': guest,
                            'adult_guest': adult_guest,
                            'child_guest': child_guest,
                            'sortBy': sortBy,
                            'property_code': property_code,
                            'districts': districts,
                            'page': page
                        },
                        dataType: 'json',
                        beforeSend: function () {
                            $('.property_types_image').prop('onclick', null).off('click')
                            $('.listing-checkbox-input').prop('disabled', true)
                            isLoading = true // Set loading to true before making the AJAX request
                            show_loader()
                        },
                        success: function (result) {
                            // ----------This Block is specific for Multi Select listing in filter modal -> Start ----------
                            if (result.districts.length > 0) {
                                // Handle the Districts data here
                                const selectDistrict = document.getElementById('district-picker')
                                selectDistrict.onchange = function () {
                                    selectDistrictArray = Array.from(selectDistrict
                                        .selectedOptions).map(option => option.value)
                                    localStorage.setItem('selectDistrictArray', JSON.stringify(
                                        selectDistrictArray))
                                }
                                selectDistrict.innerHTML = ''
                                let storedDistricts = JSON.parse(localStorage.getItem(
                                    'selectDistrictArray'))
                                if (d) {
                                    storedDistricts = d.split(',')
                                }
                                var parts = ((new URLSearchParams(window.location.search)).get('range') || '').split(',')
                                var minPrice = parts[0] || {{ $min_price }};
                                var maxPrice = parts[1] || {{ $max_price }};
                                $('#min-price').val(minPrice);
                                $('#max-price').val(maxPrice);



                                if (storedDistricts !== null) {
                                    // If Data exists in localStorage
                                    result.districts.forEach(district => {
                                        const option = document.createElement('option')
                                        option.value = district
                                        option.textContent = district
                                        if (storedDistricts.includes(district)) {
                                            option.selected = true
                                        }
                                        selectDistrict.appendChild(option)
                                    })
                                    $(selectDistrict).selectpicker('refresh')
                                } else {
                                    // If Data doesnot exists in localStorage
                                    result.districts.forEach(district => {
                                        const option = document.createElement('option')
                                        option.value = district
                                        option.textContent = district

                                        selectDistrict.appendChild(option)
                                    })
                                    $(selectDistrict).selectpicker('refresh')
                                }
                            }
                            // ----------This Block is specific for Multi Select listing in filter modal -> Start ----------
                            setTimeout(() => {
                                $('.loadskull').removeClass('loadskull')
                                var lazyloadImages = document.querySelectorAll('img.lazy')
                                var lazyloadThrottleTimeout

                                function lazyload() {
                                    if (lazyloadThrottleTimeout) {
                                        clearTimeout(lazyloadThrottleTimeout)
                                    }

                                    lazyloadThrottleTimeout = setTimeout(function () {
                                        var scrollTop = window.pageYOffset
                                        lazyloadImages.forEach(function (img) {
                                            if (img.offsetTop < (window
                                                    .innerHeight + scrollTop
                                            )) {
                                                img.src = img.dataset.src
                                                img.classList.remove(
                                                    'lazy')
                                            }
                                        })
                                        if (lazyloadImages.length == 8) {
                                            document.removeEventListener('scroll',
                                                lazyload)
                                            window.removeEventListener('resize',
                                                lazyload)
                                            window.removeEventListener(
                                                'orientationChange', lazyload)

                                        }
                                    }, 20)
                                }

                                lazyload()
                            }, 500)

                            // trackEvent('view_item_list', {
                            //     items: result.pagedata.data.map((item, index) => {
                            //         return {
                            //             item_id: item.id,
                            //             affiliation: '',
                            //             discount: Number.parseFloat(item.before_discount - item.total_price).toFixed(2),
                            //             index: (index + 1) * page,
                            //             item_type: item.property_type_name,
                            //             item_city_name: item.city_name,
                            //             item_host_id: item.host_id,
                            //             price: Number.parseFloat(item.day_price).toFixed(2),
                            //             quantity: item.number_of_days,
                            //             total_price: Number.parseFloat(item.total_price).toFixed(2),
                            //         }
                            //     }),
                            // }, 'ga')

                            const content = result.pagedata.data.map(item => ({
                                content_id: item.property_code,
                                quantity: item.number_of_days,
                                content_type: 'product',
                                content_name: item.name
                            }))

                            // trackEvent('ViewItemList', {
                            //     content_ids: result.pagedata.data.map(item => item.property_code),
                            //     content_type: 'Property',
                            //     contents: content,
                            // }, ['tik'])

                            // trackEvent('LIST_VIEW', {
                            //     item_ids: result.pagedata.data.map(item => item.property_code),
                            //     item_category: 'product',
                            //     currency: '{{ Session::get('currency') }}',

                            //     number_items: content[0]?.quantity ?? 0,

                            //     user_email: '{{ auth()->user()?->email }}',
                            //     user_phone_number: '{{ auth()->user()?->phone }}',
                            // }, ['snap'])

                            allowRefresh = false

                            var properties = result.pagedata.data
                            var room_point = []
                            for (var key in properties) {
                                let applang = "{{ app()->getLocale() }}"

                                let description = '---------'
                                let currencyCode = properties[key].code
                                let price = properties[key].price
                                let rating = properties[key].rating_avg ?? 0
                                if (properties[key].summary) {
                                    description = properties[key].summary
                                } else {
                                    description = applang == 'en' ?
                                        'No description available in English' :
                                        'لا يوجد وصف متوفر باللغة العربية'
                                }

                                if (properties.hasOwnProperty(key)) {
                                    room_point[key] = {
                                        latitude: properties[key].latitude,
                                        longitude: properties[key].longitude,
                                        title: properties[key].name,

                                        content: '<a href="' + APP_URL + '/properties/' +
                                            properties[key]
                                                .slug + '?checkin=' + checkin + '&checkout=' +
                                            checkout +
                                            '&guests=' + guest +
                                            '" class="media-cover" target="_blank">' +
                                            '<img height="100" class="map-property-img" src="' +
                                            properties[key]
                                                .cover_photo + '"alt="' + properties[key].name +
                                            '">' +
                                            '</a>' +
                                            '<a href="' + APP_URL + '/properties/' +
                                            properties[key]
                                                .slug + '?checkin=' + checkin + '&checkout=' +
                                            checkout +
                                            '&guests=' + guest +
                                            '" class="media-cover" target="_blank">' +
                                            '<div class="map-property-name">' +
                                            '<div class="col-xs-12 p-1">' +
                                            '<div class="title"><h5>' + properties[key].name +
                                            '</h5>' +
                                            '<p class="product-rate"><img src="" height="auto" width="auto" alt="">' +
                                            '<span dir="ltr">'+ rating +'/</span>' + '<span dir="ltr">5</span></p>' +
                                            '</div>' +
                                            '<p class="product-content-map">' + description +
                                            '</p>' +
                                            '<b class="fw-600">' + price + ' ' + currencyCode +
                                            '</b>' +
                                            '</div>' +
                                            '</div>' +
                                            '</a>'
                                    }

                                }
                            }
                            isLoading = false

                            if (result.pagedata.total) {
                                upcomingresult = result.current_count

                                $('#properties_show').append(result
                                    .proerties_html) // proerties_html -- "ajax-result.blade.php"
                                $('.listing-checkbox-input').prop('disabled', false)
                                $('.property_types_image').prop('onclick', false)

                                if (upcomingresult < 8 && result.pagedata.last_page == 1) {
                                    finished = true //turn off more loading
                                }

                                if (result.pagedata.current_page == result.pagedata.last_page) {
                                    page = 1
                                } else {
                                    page += 1
                                }

                            } else {
                                finished = true //turn off more loading
                                $('#properties_show').append(result
                                    .proerties_html) // proerties_html -- "ajax-result.blade.php"
                                page = 1
                            }

                            $('.property_types_image').prop('onclick', true).on('click', function (
                                e) {
                                e.preventDefault()
                                let ele = $(this)
                                onClickPropertyFilter(ele)
                            })
                        },
                        error: function (request, error) {
                            allowRefresh = false
                            isLoading = false

                            $('.property_types_image').prop('onclick', true).on('click', function (
                                e) {
                                e.preventDefault()
                                let ele = $(this)
                                onClickPropertyFilter(ele)
                            })
                        },
                        complete: function () {
                            isLoading = false
                            hide_loader()
                            document.documentElement.style.overflow = 'auto'
                            $(window).scroll(function () {
                                var scrollTop = $(this).scrollTop()
                                var windowHeight = $(this).height()

                                if (scrollTop + windowHeight >= $(document).height() -
                                    120) {

                                    if (!isLoading) {
                                        if (!finished) {
                                            // console.log('LOAD NOW !');
                                            // getProperties($('#map_view').locationpicker('map').map);
                                            getPropertiesV2()
                                        } else {
                                            // console.log('NO MORE DATA...');
                                        }
                                    }
                                }
                                windowHeight = scrollTop
                            })
                        }
                    })
                }
            }

            function clearSearchData() {
                deleteMarkers()
                $('#properties_show').html('')
                finished = false
                page = 1
            }

            // Hide the map_view element on document ready
            $('#map_view').css('visibility', 'hidden')
            $('#map_view').addClass('result-map')

            $('#header-search-form').on('change', function () {
                allowRefresh = true
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
            })

            function onClickPropertyFilter(ele) {
                let url = "{{route('get.property.type', ':property_type_id')}}"
                let cb = ele.find('.listing-checkbox-input')

                // change page Url
                updateQueryParams('property_type', cb.val())

                $.ajax({
                    url: url.replace(':property_type_id', cb.val()),
                    type: 'get',
                    success: function (data) {
                        try {
                            //-----------WebEngage Integration------------
                            let user = DEFAULT_USER
                            // let comment = DEFAULT_USER;

                            let authcheck = '{{ auth()->check() }}'
                            if (authcheck) {
                                // user_id = "{{ Auth::id() }}";
                                @auth
                                var isHost = @json(auth()->user()->is_host);
                                @endauth
                                    user = isHost == true ? 'Host' : DEFAULT_USER
                            }

                            payload = {
                                'Category Name': data.name,
                                'User': user
                            }
                            webEngageTracking(CATEGORY_SELECTED, payload)
                            //-----------WebEngage Integration------------
                        } catch (error) {
                            // Handle any errors that occur within the try block
                            console.error('Error:', error)
                        }
                    },
                    error: function (error) {
                        // Handle any errors that occur with the AJAX request
                        console.error('AJAX Error:', error)
                    }
                })

                cb.prop('checked', !cb.prop('checked'))
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
            }

            $('.property_types_image').on('click', function (e) {
                e.preventDefault()
                let ele = $(this)
                onClickPropertyFilter(ele)
            })

            $('.priceFilter').on('click', function () {
                sortBy = $(this).data('value')
                updateQueryParams('sort', sortBy)
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
            })

            $('#btnBook, #btnRoom, #btnPrice, .filter-apply').on('click', function () {
                event.preventDefault()

                let checkin = new Date($('#startDate').val())
                let checkout = new Date($('#endDate').val())

                // trackEvent('search', {
                //     search_term: $('#front-search-field').val(),
                //     search_month: checkin.toLocaleString('default', { month: 'long' }),
                //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
                //     email: '{{ auth()->user()?->email }}',
                //     phone: '{{ auth()->user()?->phone }}'
                // }, 'ga')

                // trackEvent('Search', {
                //     contents: [],
                //     query: $('#front-search-field').val(),
                //     currency: '{{ Session::get('currency') }}',
                //     search_month: checkin.toLocaleString('default', { month: 'long' }),
                //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
                //     email: '{{ auth()->user()?->email }}',
                //     phone: '{{ auth()->user()?->phone }}'
                // }, 'tik')

                // trackEvent('SEARCH', {
                //     search_month: checkin.toLocaleString('default', { month: 'long' }),
                //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
                //     item_category: 'product',
                //     search_string: $('#front-search-field').val(),
                //     user_email: '{{ auth()->user()?->email }}',
                //     user_phone_number: '{{ auth()->user()?->phone }}'
                // }, ['snap'])

                deleteMarkers()
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
                // $('.room_filter').addClass('d-none');
                $('#filter').modal('hide')
                // $('#more_filters').show();
                $('.dropdown-menu-price').removeClass('show')
            })

            $('.filter-apply').on('click', function () {
                if (document.getElementById('city-picker')) {
                    var locationModal = document.getElementById('city-picker')
                    var cityDefault = locationModal.options[locationModal.selectedIndex].value
                    if (cityDefault !== null) {
                        $('#front-search-field').val(cityDefault)
                    }
                }
            })

            function getCheckedValueArray(field_name) {
                // console.log(field_name);
                var array_Value = ''
                array_Value = $('input[name="' + field_name + '[]"]:checked').map(function () {
                    return this.value
                })
                    .get()
                    .join(',')

                return array_Value
            }

            $(document.body).on('click', '#map_view', function () {
                allowRefresh = true
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
            })

            $('#map_view').locationpicker({
                location: {
                    latitude: {{ "$lat" }},
                    longitude: {{ "$long" }}
                    // latitude: 24.8607,
                    // longitude: 67.0011
                },
                radius: 0,
                zoom: {{ "$zoom" }},
                addressFormat: '',
                markerVisible: false,
                markerInCenter: false,
                inputBinding: {
                    latitudeInput: $('#latitude'),
                    longitudeInput: $('#longitude'),
                    locationNameInput: $('#address_line_1')
                },
                enableAutocomplete: true,
                draggable: true,
                onclick: function (currentLocation, radius, isMarkerDropped) {
                    if (allowRefresh == true) {
                        //    getProperties($(this).locationpicker('map').map);
                    }
                },

                oninitialized: function (component) {
                    var addressComponents = $(component).locationpicker('map').location
                        .addressComponents
                }
            })

            $('.slider-selection').trigger('click')

            function show_loader() {
                $('#loader-more').removeClass('d-none')
                $('#pagination').hide()
            }

            function hide_loader() {
                $('#loader-more').addClass('d-none')
                $('#pagination').show()
            }

            // Map show
            $('.showMap').on('click', function () {
                $('.map-view').removeClass('v-none')
                $('.closeMap').removeClass('d-none')
                $('.showMap').addClass('d-none')
                $('.aft-map').addClass('d-none')
                $('.services').addClass('mp-height')
                $('#map_view').css('visibility', 'visible')
                $('#map_view').removeClass('result-map')


                allowRefresh = true
                clearSearchData()

                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
            })

            // Map Close
            $('.closeMap').on('click', function () {
                $('.map-view').addClass('v-none')
                $('.showMap').removeClass('d-none')
                $('.closeMap').addClass('d-none')
                $('.aft-map').removeClass('d-none')
                $('.services').removeClass('mp-height')


                allowRefresh = true
                clearSearchData()

                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()

            })

            // $(window).on("load", function() {
            $(document).ready(function () {
                allowRefresh = true
                setTimeout(function () {
                    // getProperties($('#map_view').locationpicker('map').map);
                    getPropertiesV2(true)
                }, 1000)
            })

            $(document).on('click', '.dropdown-menu-price', function (e) {
                e.stopPropagation()
            })
            @auth $(document).on('click', '.book_mark_change', function (event) {
                event.preventDefault()
                var property_id = $(this).data('id')
                var property_status = $(this).data('status')
                var user_id = "{{ Auth::id() }}"
                var dataURL = APP_URL + '/add-edit-book-mark'
                var that = this
                if (property_status == '1') {
                    var title = "{{ customTrans('favourite.remove') }}"

                } else {

                    var title = "{{ customTrans('favourite.add') }}"
                }

                swal({
                    title: title,
                    icon: 'warning',
                    buttons: {
                        cancel: {
                            text: "{{ customTrans('general.no') }}",
                            value: null,
                            visible: true,
                            className: 'btn btn-outline-danger text-16 font-weight-700 pt-3 pb-3 pl-5 pr-5',
                            closeModal: true
                        },
                        confirm: {
                            text: "{{ customTrans('general.yes') }}",
                            value: true,
                            visible: true,
                            className: 'btn vbtn-outline-success text-16 font-weight-700 pl-5 pr-5 pt-3 pb-3 pl-5 pr-5',
                            closeModal: true
                        }
                    },
                    dangerMode: true
                })
                    .then((willDelete) => {
                        if (willDelete) {

                            $.ajax({
                                url: dataURL,
                                data: {
                                    '_token': "{{ csrf_token() }}",
                                    'id': property_id,
                                    'user_id': user_id
                                },
                                type: 'post',
                                dataType: 'json',
                                success: function (data) {

                                    $(that).removeData('status')
                                    if (data.favourite.status == 'Active') {
                                        $(that).css('color', '#fcb23e')
                                        $(that).attr('data-status', 1)
                                        swal('success',
                                            '{{ customTrans('success.favourite_add_success') }}'
                                        ).then(function () {
                                            location.reload()
                                        })

                                    } else {
                                        $(that).css('color', 'black')
                                        $(that).attr('data-status', 0)
                                        swal('success',
                                            '{{ customTrans('success.favourite_remove_success') }}'
                                        ).then(function () {
                                            location.reload()
                                        })


                                    }
                                }
                            })

                        }
                    })
            })
            @endauth

            $('#closeMap').addClass('d-none')

            if (a) {
                const inputs = a.split(',')
                for (const input of inputs) {
                    $('input[name="amenities[]"][value="' + input + '"]').prop('checked', true)
                }
            }

            $('input[name=\'rb-fillter\'][value=' + rb + ']').prop('checked', true)
            $('input[name=\'rb-fillter-two\'][value=' + rbtwo + ']').prop('checked', true)
            $('input[name=\'rb-fillter-three\'][value=' + rbthree + ']').prop('checked', true)
            $('select[name="d"]').value = d
            $('input[name="property_code"]').value = pc

            $('input[name="rb-fillter"]').on('change', (e) => {
                updateQueryParams('rb', e.target.value)
            })

            $('input[name="rb-fillter-two"]').on('change', (e) => {
                updateQueryParams('rbtwo', e.target.value)
            })

            $('input[name="rb-fillter-three"]').on('change', (e) => {
                updateQueryParams('rbthree', e.target.value)
            })

            $('select[name="districts"]').on('change', (e) => {
                updateQueryParams('d', e.target.value)
            })

            $('select[name="property_code"]').on('change', (e) => {
                updateQueryParams('pc', e.target.value)
            })

            $('input[name="amenities[]"]').on('change', () => {
                updateQueryParams('a', getCheckedValueArray('amenities'))
            })

            var parts = ((new URLSearchParams(window.location.search)).get('range') || '').split(',')
            var minPrice = parts[0] || {{ $min_price }};
            var maxPrice = parts[1] || {{ $max_price }};

            var aboveValue = 5000
            var priceRangeSlider = $('#price-range').slider({
                min: 5,
                max: aboveValue,
                values: [Number(minPrice), Number(maxPrice)],
                tooltip: 'hide'
            })

            priceRangeSlider.on('change', function (event) {
                updateQueryParams('range', event.target.dataset.value)
            })

            function updateSliderAndInputs() {
                var minPriceInput = parseInt($('#min-price').val())
                var maxPriceInput = parseInt($('#max-price').val())
                if (minPriceInput >= 5000) {
                    $('#min-price').val(5000 + '+')
                }
                if (maxPriceInput >= 5000) {
                    $('#max-price').val(5000 + '+')
                }

                updateQueryParams('range', minPriceInput + ',' + maxPriceInput)
                priceRangeSlider.slider('setValue', [minPriceInput, maxPriceInput])
            }

            $('#min-price, #max-price').on('input', function () {
                updateSliderAndInputs()
            })
            priceRangeSlider.on('slide', function (slideEvt) {
                var minPrice = slideEvt.value[0]
                var maxPrice = slideEvt.value[1]
                if (minPrice >= 5000) {
                    $('#min-price').val(5000 + '+')
                } else {
                    $('#min-price').val(minPrice)
                }
                if (maxPrice >= 5000) {
                    $('#max-price').val(5000 + '+')
                } else {
                    $('#max-price').val(maxPrice)
                }
            })

            function setInputFieldsFromSlider() {
                var sliderValues = priceRangeSlider.slider('getValue')
                if (sliderValues[0] >= 5000) {
                    $('#min-price').val(5000 + '+')
                } else {
                    $('#min-price').val(sliderValues[0])
                }
                if (sliderValues[1] >= 5000) {
                    $('#max-price').val(5000 + '+')
                } else {
                    $('#max-price').val(sliderValues[1])
                }
            }

            setInputFieldsFromSlider()
        })

        // dropdown overlay
        $(document).ready(function () {
            $(document).on('click', '.fav-icon', function () {

                var itemId = $(this).data('item-id')
                const elem = this
                // Set the item ID as the value of the hidden input field in the modal
                $('#item_id').val(itemId)
                $('#item_before_discount').val(elem.dataset.beforeDiscount);
                $('#item_total_price').val(elem.dataset.totalPrice);
                $('#item_property_type_name').val(elem.dataset.propertyTypeName);
                $('#item_city_name').val(elem.dataset.cityName);
                $('#item_host_id').val(elem.dataset.hostId);
                $('#item_day_price').val(elem.dataset.dayPrice);
                $('#item_number_of_days').val(elem.dataset.numberOfDays);
                $('#item_property_code').val(elem.dataset.propertyCode);
                $('#wishlist-property-id').val(itemId)
            })

            // Clear the hidden input field value when the modal is closed
            $('#create-whishlist').on('hidden.bs.modal', function () {
                $('#item_id').val('')
                $('#wishlist-property-id').val('')
            })
            $('#whishlist-listing').on('hidden.bs.modal', function () {
                $('#wishlist-property-id').val('')
            })
            $('#priceFilter').on('click', function () {
                if ($(this).hasClass('show')) {
                    $('.overlay').show()
                } else {
                    $('.overlay').hide()
                }
            })

            $(document).on('click', function (event) {
                var target = $(event.target)
                if (!target.closest('#priceFilter').length) {
                    $('.overlay').hide()
                }
            })
        })
    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            // Capture the click event on the heart icon


        })

        $(document).on('click', '#wishlistBtn', function (e) {
            e.preventDefault()
            $(this).addClass('disabled')

            var name = $('#name').val()
            var property_id = $('#item_id').val()
            var token = $('input[name="_token"]').val()
            $.ajax({
                type: 'POST',
                url: "{{ route('createWishlist') }}",
                data: {
                    '_token': token,
                    'name': name,
                    'property_id': property_id
                },
                success: function (data) {
                    if (data == 'Success') {
                        setTimeout(function () {
                            $('#create-whishlist').modal('hide')
                            $('#success').modal('show')
                        }, 1000)

                    }

                }
            })
        })
        $(document).on('click', '.successmodalbtn', function (e) {
            window.location.reload(true)
        })
        $(document).on('click', '#existWishlist', function (e) {
            e.preventDefault()
            $(this).addClass('disabled')
            var property_id = $('#wishlist-property-id').val()
            var wishlist_name_id = $(this).data('wishlist-name-id')
            var token = $('input[name="_token"]').val()
            $.ajax({
                type: 'POST',
                url: "{{ route('addRemoveWishlist') }}",
                data: {
                    '_token': token,
                    'wishlist_name_id': wishlist_name_id,
                    'property_id': property_id
                },
                success: function (data) {
                    if (data.msg == 'Success') {
                        $('#whishlist-listing').modal('hide')
                        //-----------WebEngage Integration------------
                        let user = DEFAULT_USER
                        // let comment = DEFAULT_USER;
                        let authcheck = '{{ auth()->check() }}'
                        if (authcheck) {
                            // user_id = "{{ Auth::id() }}";
                            @auth
                            var isHost = @json(auth()->user()->is_host);
                            @endauth
                                user = isHost == true ? 'Host' : DEFAULT_USER
                        }

                        payload = {
                            'Name': data.property.name,
                            'Unit Code': data.property.property_code,
                            'Cost Per Night': data.property.property_price.price,
                            'Category Name': data.property.property_type.name,
                            'User': user
                        }
                        webEngageTracking(MARK_AS_WISHLIST, payload)
                        //-----------WebEngage Integration------------
                        window.location.reload()

                    }

                }
            })
        })
        $(document).on('click', '#toggleWishlist', function (e) {
            let authcheck = '{{ auth()->check() }}'
            if (authcheck) {
                e.preventDefault()
                $(this).addClass('disabled')
                // var property_id = $('#wishlist-property-id').val();
                var user_id = "{{ Auth::id() }}"
                var property_id = $(this).data('item-id')
                var $icon = $(this)
                var token = $('input[name="_token"]').val()

                $.ajax({
                    type: 'POST',
                    url: "{{ route('toggleWishlist') }}",
                    data: {
                        '_token': token,
                        'user_id': user_id,
                        'property_id': property_id
                    },
                    success: function (data) {
                        if (data.msg == 'Success') {
                            $icon.removeAttr('id')
                            $icon.find('.heart_icon').removeClass('active')
                            $icon.find('.heart_icon').attr('data-status', 0)
                            $icon.attr('data-bs-target',
                                "{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                            )
                            $icon.removeClass('disabled')
                            // window.location.reload();
                        }
                    }
                })
            }
        })
        $(document).on('click', '#guestWishlist', function () {
            $('#staticBackdrop').modal('show')
        })

        $(document).on('click', '.go-to-single', function(e) {
            e.preventDefault()

            const elem = e.target

            // trackEvent('select_item', {
            //     item_id: elem.dataset.id,
            //     affiliation: '',
            //     discount: Number.parseFloat(elem.dataset.beforeDiscount - elem.dataset.totalPrice).toFixed(2),
            //     index: elem.dataset.index,
            //     item_type: elem.dataset.propertyTypeName,
            //     item_city_name: elem.dataset.cityName,
            //     item_host_id: elem.dataset.hostId,
            //     price: Number.parseFloat(elem.dataset.dayPrice).toFixed(2),
            //     quantity: elem.dataset.numberOfDays,
            //     total_price: Number.parseFloat(elem.dataset.totalPrice).toFixed(2),
            // }, 'ga')

            // trackEvent('SelectItem', {
            //     index: elem.dataset.index,
            //     content_id: elem.dataset.propertyCode,
            //     content_type: 'product',
            //     quantity: elem.dataset.numberOfDays,
            //     currency: '{{Session::get('currency')}}',
            //     value: elem.dataset.totalPrice,
            // }, ['tik'])

            window.location.href = 'properties/' + elem.dataset.slug + '?checkin={{Session::get('header_checkin')}}&checkout={{Session::get('header_checkout')}}';
        })
    </script>

    <script>
         document.addEventListener("change", function (e) {
        if (e.target.classList.contains("amenity-sync")) {
            const value = e.target.value;
            const isChecked = e.target.checked;

            // Sync all checkboxes with the same value
            document.querySelectorAll(`.amenity-sync[value="${value}"]`).forEach(cb => {
                cb.checked = isChecked;
            });
        }
    });

    const appLocale = "{{ app()->getLocale() }}";
    const assetBaseUrl = "{{ asset('icons') }}";
    const amenitiesSelected = @json($amenities_selected);

    document.addEventListener("DOMContentLoaded", function () {
        const container = document.getElementById("recommended-amenities");

        function loadAmenities(propertyTypeId) {
            updateQueryParams('property_type', propertyTypeId)
            const url = `/get-amenities/${propertyTypeId}`;
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    container.innerHTML = ''; // Clear previous

                    if (data.length === 0) {
                        container.innerHTML = '<div class="col-12">No amenities found.</div>';
                        return;
                    }

                    data.forEach(function (amenity) {
                        const checked = amenitiesSelected.includes(amenity.id) ? 'checked' : '';
                        const name = appLocale === 'ar' ? amenity.title_ar : amenity.title;
                        const imageUrl = amenity.icon_image ? `${assetBaseUrl}/${amenity.icon_image}` : 'https://img.icons8.com/emoji/48/question-mark.png';
                        const html = `
                            <div class="col-6 px-2">
                                <input type="checkbox" value="${amenity.id}" name="amenities1[]" id="amenity-${amenity.id}" class="cust-form-check-input recommended-checkbox amenity-sync" ${checked}>

                                <label for="amenity-${amenity.id}" class="recommended-label">
                                    <img src="${imageUrl}">
                                </label>
                                <p class="recommended-content">${name}</p>
                            </div>
                        `;
                        container.insertAdjacentHTML('beforeend', html);
                    });
                })
                .catch(error => {
                    console.error("Error fetching amenities:", error);
                });
        }


        document.querySelectorAll(".property-type-radio").forEach(function (radio) {
            radio.addEventListener("change", function () {
                $('input[name="amenities[]"]').prop('checked', false);
                loadAmenities(this.value);
            });
        });


        const selectedRadio = document.querySelector(".property-type-radio:checked");
        if (selectedRadio) {
            loadAmenities(selectedRadio.value);
        }
    });
</script>

@endpush
