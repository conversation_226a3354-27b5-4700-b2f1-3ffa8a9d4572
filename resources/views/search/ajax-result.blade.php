@section('properties_data')
    @php
        use App\Models\WishlistName;

        $wishlistExist = WishlistName::where('creater_id', Auth::id())->get();
        if (isset($wishlistExist)) {
            $existWishlistModal = true;
            $wishlist_modal = true;
        } else {
            $existWishlistModal = false;
            $wishlist_modal = true;
        }
    @endphp
    @forelse ($search_result as $prop)
        <div class="col-cstm col-md-3 col-xs-12">
            <div class="product">
                <div class="image">
                    <div class="loadskull view-img">
                        <swiper-container class="host-slider " pagination="true" pagination-dynamic-bullets="true"
                            navigation="true">

                            <swiper-slide>
                                <a href="{{ route('property.single', ['slug' => $prop->slug]) }}"
                                    aria-label="{{ $prop->name }}" class="">
                                    <img src="{{ asset('images/loader.gif') }}" height="auto" width="auto"
                                        class="product-img lazy" data-src="{{ asset($prop->cover_photo) }}" loading="lazy"
                                        alt="{{ $prop->name }}">
                                </a>
                            </swiper-slide>
                            @foreach ($prop->property_photos as $filterphoto)
                                @if ($filterphoto->cover_photo != 1)
                                    <swiper-slide>
                                        <a href="{{ route('property.single', ['slug' => $prop->slug]) }}"
                                            aria-label="{{ $prop->name }}" class="">
                                            <img src="{{ asset('images/loader.gif') }}" height="auto" width="auto"
                                                class="product-img lazy lazy-loading" data-src="{{ asset($filterphoto->photo) }}"
                                                loading="lazy" alt="{{ $prop->name }}">
                                        </a>
                                    </swiper-slide>
                                @endif
                            @endforeach


                        </swiper-container>
                    </div>

                    <div class="fav-icon" data-bs-toggle="modal"
                        @auth @if ($prop->wishlist == true) id="toggleWishlist"
                         @else data-bs-target="{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                         @endif @endauth
                        @guest id="guestWishlist" @endguest data-item-id="{{ $prop->id }}"
                        data-before-discount="{{ $prop->before_discount }}" data-total-price="{{ $prop->total_price }}"
                        data-property-type-name="{{ $prop->property_type_name }}" data-city-name="{{ $prop->city_name }}"
                        data-host-id="{{ $prop->host_id }}" data-day-price="{{ $prop->day_price }}"
                        data-number-of-days="{{ $number_of_days }}" data-property-code="{{ $prop->property_code }}">
                        <div class="fav-in {{ $prop->wishlist == true ? 'active' : '' }}  heart_icon"
                            data-status="{{ $prop->wishlist }}" data-id="{{ $prop->id }}">
                            <img src="{{ asset('icons/Path 125.svg') }}" height="auto" width="auto" alt="heart"
                                class="fav-blank">
                            <img src="{{ asset('icons/fill-heart.svg') }}" height="auto" width="auto" alt="heart"
                                class="fav-fill">
                        </div>
                    </div>
                    @if (!$prop->available || $prop->max_nights < request()->searchNights)
                        <p class="not-available-tag loadskull">{{ customTrans('general.not_available') }}</p>
                    @endif
                    @if($prop->property_price?->weekly_discount !=0 && $prop->property_price?->monthly_discount !=0)
                            <div class="mw-discount-property">{{ customTrans('property_single.monthly_and_weekly_discount') }} </div>
                        @elseif($prop->property_price?->monthly_discount !=0)
                            <div class="mw-discount-property">{{ customTrans('property_single.monthly_discount_available') }} {{$prop->property_price?->monthly_discount}}%</div>
                        @elseif($prop->property_price?->weekly_discount !=0)
                            <div class="mw-discount-property">{{ customTrans('property_single.weekly_discount_available') }} {{$prop->property_price?->weekly_discount}}%</div>
                        @else
                    @endif
                </div>
                <div class="product-detail">
                    <a href="#" aria-label="{{ $prop->name }}" class="float-anc go-to-single"
                        data-slug="{{ $prop->slug }}" data-id="{{ $prop->id }}"
                        data-before-discount="{{ $prop->before_discount }}" data-total-price="{{ $prop->total_price }}"
                        data-property-type-name="{{ $prop->property_type_name }}" data-city-name="{{ $prop->city_name }}"
                        data-host-id="{{ $prop->host_id }}" data-day-price="{{ $prop->day_price }}"
                        data-number-of-days="{{ $number_of_days }}"
                        data-index="{{ ($loop->index + 1) * request()->input('page') }}"
                        data-property-code="{{ $prop->property_code }}"></a>
                    <div class="title">
                        @if (!!$prop->slug)
                            <a href="{{ route('property.single', ['slug' => $prop->slug]) }}"
                                aria-label="{{ $prop->name }}" class="webEngageBtn">
                                <h4 class="loadskull">{{ propertyTitleForListing($prop) }}</h4>
                            </a>
                        @endif


                        <p class="product-rate loadskull"><img src="{{ asset('icons/rate-2.svg') }}" height="auto"
                                width="auto" alt="">
                            @if ($prop->rating_avg)
                                <span dir="ltr">{{ $prop->average_rating }}/</span><span dir="ltr">{{ '5' }}</span>
                                ({{ isset($prop->reviews_count) && !empty($prop->reviews_count) ? $prop->reviews_count : 0 }})
                            @else
                                <span dir="ltr">{{ '0' }}/</span><span dir="ltr">{{ '5' }}</span>({{ '0' }})
                            @endif
                        </p>
                    </div>

                    {{-- getDescription in helpers.php --}}
                    @php $description = getDescriptionForListing($prop->property_description_summary_ar, $prop->property_description_summary); @endphp
                    <div class="pr-code">
                        <p class="mb-0 loadskull">
                            @if (app()->getLocale() == 'ar')
                                {{ isset($prop->property_address_district_ar) ? $prop->property_address_district_ar . ',' : $prop->property_address_district . ',' }}

                                {{ isset($prop->property_address_city_ar) ? $prop->property_address_city_ar : $prop->property_address_city }}
                            @else
                                {{ isset($prop->property_address_district) ? $prop->property_address_district . ',' : '' }}

                                {{ isset($prop->property_address_city) ? $prop->property_address_city : '' }}
                            @endif
                        </p>
                    </div>
                    <p class="product-content loadskull">
                        {{-- shortString in helpers.php --}}
                        {{ shortString($description, 70) }}
                        @if (strlen($description) > 70)
                            <span class="rd-more">{{ customTrans('property_single.read_more') }}</span>
                        @endif
                    </p>
                    <p class="product-content loadskull">
                        {{ digitsToArabic($prop->beds) . ' ' . customTrans('property_single.bed') }}
                    </p>
                    <div>
                        <p class="product-price loadskull">
                            @if ($prop->total_before_discount > $prop->total_after_discount)
                                <style>
                                    .old-price {
                                        text-decoration: line-through;
                                        font-size: .9em;
                                    }
                                </style>
                                <span class="old-price">
                                    {{ number_format($prop->total_before_discount / $number_of_days, 2, '.', '') }}
                                </span>
                                {{ number_format($prop->total_after_discount / $number_of_days, 2, '.', '') }}
                                <span class="sar-pr">{{ customTrans('utility.sar') }}</span>/ Night
                            @else
                                {{ number_format($prop->total_before_discount / $number_of_days, 2, '.', '') }}
                                <span class="sar-pr">{{ customTrans('utility.sar') }}</span>/ Night
                            @endif
                        </p>
                        <p>
                            {{ customTrans('search.total_of') }}
                            ({{ $number_of_days }} {{ customTrans('search.nights') }}
                            ) {{ number_format($prop->total_after_discount, 2, '.', '') }} <span class="sar-pr">{{ customTrans('utility.sar') }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="text-center justify-content-center w-100 position-center">
            <img width="400" src="{{ asset('icons/no-data-found.svg') }}" class="img-fluid not-found" alt="not-found">
            <h4 class="text-center text-20 font-weight-700">
                {{ customTrans('search.no_result_found') }}
            </h4>
        </div>
    @endforelse
@endsection
