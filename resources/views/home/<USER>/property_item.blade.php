<div class="col-xl-3 col-lg-4 col-sm-6 col-12">
    <div class="product">
        <div class="image">
            @if ($property->property_discount)
                <div class="discount-tag">
                    <span>{{ $property->property_discount->discount }}</span>%
                </div>
            @endif
            <div class="loadskull view-img">
                <div class="swiper host-slider mb-3">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            @if (!!$property->slug)
                                <a href="{{ route('property.single', ['slug' => $property->slug]) }}"
                                    aria-label="{{ $property->name }}" class="webEngageBtn">
                                    <img src="{{ asset('images/loader.gif') }}" height="auto"
                                        width="auto" class="product-img lazy"
                                        data-src="{{ asset($property->cover_photo) }}"
                                        loading="lazy" alt="{{ $property->name }}">
                                </a>
                            @endif
                        </div>
                        @php $filteredPhotos = $property->property_photos->where('cover_photo', '!=', 1)->take(5); @endphp
                        @foreach ($filteredPhotos as $filterphoto)
                            <div class="swiper-slide">
                                @if (!!$property->slug)
                                    <a href="{{ route('property.single', ['slug' => $property->slug]) }}"
                                        aria-label="{{ $property->name }}" class="webEngageBtn">
                                        <img src="{{ asset('images/loader.gif') }}" height="auto"
                                            width="auto" class="product-img lazy"
                                            data-src="{{ asset($filterphoto->photo) }}"
                                            loading="lazy" alt="{{ $property->name }}">
                                    </a>
                                @endif
                            </div>
                        @endforeach
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-hp-prev"></div>
                    <div class="swiper-button-hp-next"></div>
                </div>
            </div>
            <div class="fav-icon " data-bs-toggle="modal"
                @auth
                    @if ($property->wishlist == true) id="toggleWishlist"
                    @else
                        data-bs-target="{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                    @endif @endauth
                @guest id="guestWishlist" @endguest
                 data-item-id="{{ $property->id }}"
                 data-before-discount="{{ $property->before_discount }}"
                 data-total-price="{{ $property->total_price }}"
                 data-property-type-name="{{ $property->property_type_name }}"
                 data-city-name="{{ $property->city_name }}"
                 data-host-id="{{ $property->host_id }}"
                 data-day-price="{{ $property->day_price }}"
                 data-number-of-days="{{ $property->number_of_days }}"
                 data-property-code="{{ $property->property_code }}"
            >
                <div class="fav-in {{ $property->wishlist == true ? 'active' : '' }}  heart_icon"
                    data-status="{{ $property->wishlist }}" data-id="{{ $property->id }}">
                    <img src="{{ asset('icons/Path 125.svg') }}" height="auto" width="auto"
                        alt="heart" class="fav-blank">
                    <img src="{{ asset('icons/fill-heart.svg') }}" height="auto" width="auto"
                        alt="heart" class="fav-fill">
                </div>
            </div>
            @if($property->property_price?->weekly_discount !=0 && $property->property_price?->monthly_discount !=0)
                    <div class="mw-discount-property">{{ customTrans('property_single.monthly_and_weekly_discount') }} </div>
                @elseif($property->property_price?->monthly_discount !=0)
                    <div class="mw-discount-property">{{ customTrans('property_single.monthly_discount_available') }} {{$property->property_price?->monthly_discount}}%</div>
                @elseif($property->property_price?->weekly_discount !=0)
                    <div class="mw-discount-property">{{ customTrans('property_single.weekly_discount_available') }} {{$property->property_price?->weekly_discount}}%</div>
                @else
            @endif
        </div>
        <div class="product-detail">
            <div class="title">
                @if (!!$property->slug)
                    <a href="{{ route('property.single', ['slug' => $property->slug]) }}"
                        aria-label="{{ $property->name }}" class="webEngageBtn">
                        <h4 class="loadskull">
                            @if (app()->environment('local'))
                            {{ propertyTitle($property->id, $property->propertyType) }}
                            @else
                                {{ propertyTitle($property->id, $property->propertyType) }}
                            @endif
                        </h4>
                    </a>
                @endif
                <p class="product-rate loadskull"><img src="{{ asset('icons/rate-2.svg') }}"
                        alt="">
                    @if ($property->guest_review)
                        <span dir="ltr">{{ $property->average_rating }}/</span><span dir="ltr">{{ '5' }}</span>
                        ({{ $property->reviews_count }})
                    @else
                        <span dir="ltr">{{ '0' }}/</span><span dir="ltr">{{ '5' }}</span>({{ '0' }})
                    @endif

                </p>

            </div>

            @php $description = getDescription($property->property_description); @endphp
            <p class="product-content loadskull">
                {{ shortString($description, 70) }}
                @if (strlen($description) > 70)
                    <a href="{{ route('property.single', ['slug' => $property->slug]) }}">
                        <span class="rd-more">{{ customTrans('property_single.read_more') }}</span>
                    </a>
                @endif
            </p>


            <p class="product-price loadskull">
                {{ $property->property_price->price }} <span><span
                        class="sar-pr">{{ customTrans('utility.sar') }}</span>/{{ customTrans('payment.night') }}</span>
            </p>
            <div class="pr-code">

                <p class="mb-0 loadskull">
                    @if (app()->getLocale() == 'ar')
                        {{ isset($property->property_address->district_ar) ? $property->property_address->district_ar . ',' : $property->property_address->district . ',' }}

                        {{ isset($property->property_address->city_ar) ? $property->property_address->city_ar : $property->property_address->city }}
                    @else
                        {{ isset($property->property_address->district) ? $property->property_address->district . ',' : '' }}

                        {{ isset($property->property_address->city) ? $property->property_address->city : '' }}
                    @endif
                </p>
            </div>
        </div>
    </div>
</div>
