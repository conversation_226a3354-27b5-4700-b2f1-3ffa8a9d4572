@extends('template')
@push('css')
    <!-- Add International Telephone Input CSS -->
    <link rel="stylesheet" href="{{ asset('intlInput/css/intlTelInput.css') }}" />
    <style>
        .checkout-registeration .phone-number .iti {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .checkout-registeration .iti__selected-flag {
            display: flex;
            align-items: center;
        }

        .checkout-registeration #phone-input-signup {
            padding-left: 15px !important;
        }
    </style>
@endpush
@php
    $data = explode(' - ', $decrypted['daterange']);
    $propertyTitle = propertyTitleForListing($grid_property);
    $symbol = Session::get('currency');
    $property_type_name = $result->property_type_name;
    $property_id = $result->id;
    $guestCheck = $user->is_guest;
    $emailCheck = $user->is_guest || empty($user->email) ? true : false;

@endphp

@section('main')
    <section class="checkout-page">
        <div class="container">
            <form action="{{ url('payments/book/' . $property_id) }}" id="booking_form" method="POST">
                @csrf
                <input type="hidden" name="session_token" value="{{ $token }}">
                <h1>{{ customTrans('booking_summary.booking_details') }}</h1>
                <div class="row">
                    <div class="col-lg-7 p-xl-4">
                        <div class="checkout-registeration mb-4">
                            <h5 class="mb-3">{{ customTrans('users_profile.personal_info') }}</h5>
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3">
                                    <input type="text" class="form-control" required name="first_name"
                                        placeholder="{{ customTrans('sign_up.first_name') }}"
                                        {{ $guestCheck ? '' : 'readonly value=' . $user->first_name }}>
                                    @error('first_name')
                                        <div class="invalid-feedback show">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="text" class="form-control" required name="last_name"
                                        placeholder="{{ customTrans('sign_up.last_name') }}"
                                        {{ $guestCheck ? '' : 'readonly value=' . $user->last_name }}>
                                    @error('last_name')
                                        <div class="invalid-feedback show">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-12 mb-3">
                                    <input type="email" class="form-control" required name="email"
                                        placeholder="{{ customTrans('sign_up.your_email') }}"
                                        {{ $emailCheck ? '' : 'readonly value=' . $user->email }}>
                                    @error('email')
                                        <div class="invalid-feedback show">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-12 mb-3">
                                    <div class="after-end">
                                        <div class="phone-number">
                                            <input id="phone-input-signup" name="phone" type="tel" placeholder="Phone"
                                                disabled value="{{ $user->phone }}" />
                                            <input type="hidden" name="cohost" id="cohost_token" />
                                            <input type="hidden" name="token" id="phone_token" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if ($result->booking_type == 'instant')
                            <div class="checkout-payment mb-2">
                                <h5 class="mb-3">{{ customTrans('general.payment_option') }}</h5>
                                <div class="prop-payment-option">
                                    <div class="row">
                                        <div class="col-xl-2 col-lg-3 col-md-4 col-6 mb-2">
                                            <label class="pay-sec-label" for="ch-card-pay">
                                                <input type="radio" name="pay" id="ch-card-pay" class="cardPay"
                                                    checked />
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('images/visa-master-pay.png') }}" alt="">
                                                </div>
                                            </label>
                                        </div>
                                        <div class="col-xl-2 col-lg-3 col-md-4 col-6 mb-2">
                                            <label class="pay-sec-label" for="ch-stc-pay">
                                                <input type="radio" name="pay" id="ch-stc-pay" class="stcPay" />
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('images/stc-pay.png') }}" alt="">
                                                </div>
                                            </label>
                                        </div>
                                        <div class="col-xl-2 col-lg-3 col-md-4 col-6 mb-2">
                                            <label class="pay-sec-label" for="ch-mada-pay">
                                                <input type="radio" id="ch-mada-pay" name="pay" class="madaPay" />
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('images/mada-pay.png') }}" alt="">
                                                </div>
                                            </label>
                                        </div>
                                        @if ($showPayOpts)
                                            <div class="col-xl-2 col-lg-3 col-md-4 col-6 mb-2">
                                                <label class="pay-sec-label" for="ch-apple-pay">
                                                    <input type="radio" id="ch-apple-pay" name="pay"
                                                        class="applePay" />
                                                    <div class="pay-sl-content">
                                                        <img src="{{ asset('images/apple_pay.png') }}" alt="">
                                                    </div>
                                                </label>
                                            </div>
                                        @endif
                                        <div class="col-xl-2 col-lg-3 col-md-4 col-6 mb-2">
                                            <label class="pay-sec-label" for="ch-tabby-pay">
                                                <input type="radio" id="ch-tabby-pay" name="pay" class="tabbyPay " />
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('icons/tabby-logo-en.svg') }}" alt="">
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="checkout-wallet mb-4">
                                <h5 class="mb-3">{{ customTrans('sidenav.wallet') }}</h5>
                                <div class="wallet-payment-option" id="walletPaymentOption">
                                    <div class="form-check">
                                        <input class="form-check-input cust-form-check-input" id="wallet-pay"
                                            type="checkbox" name="pay_by_wallet" data-value={{ $user_wallet?->balance }}
                                            @if (isset($user_wallet) && $user_wallet?->balance <= 0) disabled @endif>
                                        <label class="form-check-label" for="wallet-pay">
                                            <h6 class="mb-0">{{ customTrans('general.wallet_pay') }}</h6>
                                            <div class="wallet-balance">
                                                <span>Available balance: </span>
                                                <span class="wallet-amount">{{ $user_wallet?->balance }} <span
                                                        class="currency">{{ Session::get('currency') }}</span></span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @php
                            $hasdiscount = $result->property_discount ? true : false;
                        @endphp
                        @if ($result->booking_type == 'instant' && Auth::check() && !$hasdiscount)
                            <div class="checkout-coupon-code mb-4 coupon-main">
                                <h5 class="mb-3">{{ customTrans('property_single.coupon_code') }}</h5>
                                <div class="gr-code">
                                    <input type="text" name="coupon" id="coupon" class="form-control"
                                        placeholder="{{ customTrans('host_dashboard.code') }}">
                                    <button class="pd-promo-btn apply_coupon"
                                        id="apply_coupon_btn">{{ customTrans('payment.apply_code') }}</button>
                                    <button type="button" class="pd-promo-btn remove_coupon d-none"
                                        onclick="removeCoupon()">{{ customTrans('payment.remove_code') }}</button>
                                </div>
                                <div id="coupon-res-message"></div>
                            </div>
                        @endif
                        @if ($result->booking_type == 'instant')
                            <button type="button" id="go_to_payment"
                                class="theme-btn">{{ customTrans('booking_summary.proceed_to_payment') }}</button>
                        @else
                            <button type="button" id="go_to_payment"
                                class="theme-btn">{{ customTrans('property_single.request_book') }}</button>
                        @endif
                    </div>

                    <div class="col-lg-5 p-xl-4">
                        <div class="checkout-detail">
                            <a href="{{ route('property.single', ['slug' => $result->slug]) }}"
                                class="cd-property-detail">
                                <img src="{{ isset($result->cover_photo) ? asset($result->cover_photo) : asset('images/default-image.png') }}?t={{ time() }}"
                                    alt="{{ $propertyTitle }}">
                                <div class="cd-pd-content">
                                    <h5>{{ $propertyTitle }}</h5>
                                    <p class="mb-0">
                                        {{ isset($result->property_address->city) ? transStateCity($result->property_address->city, app()->getlocale()) . ', ' : '' }}
                                        {{ isset($result->property_address->state) ? transStateCity($result->property_address->state, app()->getlocale()) . ', ' : '' }}
                                        {{ isset($result->property_address->countries->name) ? (app()->getlocale() == 'ar' ? $result->property_address->countries->name_ar : $result->property_address->countries->name) : '' }}
                                    </p>
                                </div>
                            </a>
                            <div class="col-xl-12 mb-4">
                                @if ($result->property_price?->weekly_discount != 0 && $result->property_price?->monthly_discount != 0)
                                    <div class="mw-discount">
                                        {{ customTrans('property_single.monthly_and_weekly_discount') }}
                                    </div>
                                @elseif($result->property_price?->monthly_discount != 0)
                                    <div class="mw-discount">
                                        {{ customTrans('property_single.monthly_discount_available') }}</div>
                                @elseif($result->property_price?->weekly_discount != 0)
                                    <div class="mw-discount">
                                        {{ customTrans('property_single.weekly_discount_available') }}</div>
                                @else
                                @endif
                            </div>
                            <div class="checkout-trip">
                                <h5 class="checkout-detail-title">{{ customTrans('header.your_trip') }}</h5>
                                <div class="ct-content">
                                    <h6 class="mb-0">{{ customTrans('account_transaction.date') }}</h6>
                                    <p>
                                        {{ date('F d, Y', strtotime($data[0])) }} -
                                        {{ date('F d, Y', strtotime($data[1])) }}
                                    </p>
                                    <input type="hidden" name="property_slug" id="property_slug"
                                        value="{{ $result->slug }}">
                                    <input type="hidden" name="property_id" id="property_id"
                                        value="{{ $result->id }}">
                                    <input type="hidden" id="room_blocked_dates" value="">
                                    <input type="hidden" id="calendar_available_price" value="">
                                    <input type="hidden" id="room_available_price" value="">
                                    <input type="hidden" id="price_tooltip" value="">
                                    <input type="hidden" name="url_checkin" id="url_checkin"
                                        value="{{ $decrypted['url_checkin'] }}">
                                    <input type="hidden" name="url_checkout" id="url_checkout"
                                        value="{{ $decrypted['url_checkout'] }}">
                                    <input type="hidden" name="number_of_guests" id="number_of_guests"
                                        value='{{ $decrypted['number_of_guests'] }}'>
                                    <input type="hidden" name="booking_type" id="booking_type"
                                        value="{{ $decrypted['booking_type'] }}">
                                    <input type="hidden" name="booking_type_1" id="booking_type_1"
                                        value="{{ $decrypted['booking_type_1'] }}">
                                    <input type="hidden" name="paymentMethodId" id="paymentMethodId"
                                        value="{{ $decrypted['paymentMethodId'] }}">
                                    <input type="hidden" name="daterange" id="daterange"
                                        value="{{ $decrypted['daterange'] }}">
                                    <h6 class="mb-0">{{ customTrans('listing_sidebar.checkin_checkout') }}</h6>
                                    <p>{{ date('h:i A', strtotime($result->checkinTime)) }} - {{ date('h:i A', strtotime($result->checkoutTime)) }}</p>
                                </div>
                            </div>
                            <div class="col-xl-12 text-center d-none" id="book_it_disabled">
                                <div class="not-available-detail-tag-main" id="book_it_disabled_message">
                                    <div class="not-available-detail-tag">
                                        {{ customTrans('property_single.date_not_available') }}</div>
                                </div>

                                <a href="{{ URL::to('/') }}/search?location={{ $result->property_address->city }}"
                                    class="btn btn-large btn-block text-14"
                                    id="view_other_listings_button">
                                    {{ customTrans('property_single.view_other_list') }}
                                </a>
                            </div>

                            <div class="checkout-summary">
                                <h5 class="checkout-detail-title">{{ customTrans('booking_summary.price_summary') }}
                                </h5>

                                <table class="table table-bordered price_table d-none" id="table-checkout">
                                    <tbody>
                                        <div id="append_date">

                                        </div>
                                        <tr>
                                            <td class="pl-4 w-50">
                                                <span id="total_night_count" value="">0</span>
                                                {{ customTrans('property_single.night') }}
                                                <span dir="ltr">x</span>
                                                <span id="per_night_price" value="">0</span>
                                            </td>
                                            <td class="pl-4 text-right"><span id="total_night_price" value="">
                                                    0
                                                </span>
                                                <span id="custom_price" class="fa fa-info-circle secondary-text-color"
                                                    data-html="true" data-toggle="tooltip" data-placement="top"
                                                    title=""></span>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td class="pl-4">
                                                {{ customTrans('property_single.service_fee') }}
                                            </td>
                                            <td class="pl-4 text-right"><span id="service_fee" value=""> 0
                                                </span></td>
                                        </tr>


                                        <tr class="additional_price">
                                            <td class="pl-4">
                                                {{ customTrans('property_single.additional_guest_fee') }}
                                            </td>
                                            <td class="pl-4 text-right"><span id="additional_guest" value=""> 0
                                                </span>
                                            </td>
                                        </tr>

                                        <tr class="security_price">
                                            <td class="pl-4">
                                                {{ customTrans('property_single.security_fee') }}
                                            </td>
                                            <td class="pl-4 text-right"><span id="security_fee" value=""> 0
                                                </span></td>
                                        </tr>

                                        <tr class="cleaning_price">
                                            <td class="pl-4">
                                                {{ customTrans('property_single.cleaning_fee') }}
                                            </td>
                                            <td class="pl-4 text-right"><span id="cleaning_fee" value=""> 0
                                                </span></td>
                                        </tr>

                                        <tr class="iva_tax">
                                            <td class="pl-4">
                                                {{ customTrans('property_single.iva_tax') }}
                                            </td>
                                            <td class="pl-4 text-right"> <span id="iva_tax" value=""> 0
                                                </span>
                                            </td>
                                        </tr>


                                        <tr class="accomodation_tax">
                                            <td class="pl-4">
                                                {{ customTrans('property_single.accommodatiton_tax') }}
                                            </td>
                                            <td class="pl-4 text-right"> <span id="accomodation_tax" value="">
                                                    0
                                                </span>
                                            </td>
                                        </tr>

                                        {{-- @if ($result->property_discount) --}}
                                        <tr id="yousaved_main" style="display:none">
                                            <td class="pl-4">
                                                @if ($result->property_price?->weekly_discount != 0 && $result->property_price?->monthly_discount != 0)
                                                    {{ customTrans('property_single.monthly_and_weekly_discount_mw') }}
                                                @elseif($result->property_price?->monthly_discount != 0)
                                                    {{ customTrans('property_single.monthly_discount_available_mw') }}
                                                @elseif($result->property_price?->weekly_discount != 0)
                                                    {{ customTrans('property_single.weekly_discount_available_mw') }}
                                                @else
                                                @endif
                                            </td>
                                            <th class="pl-4 text-right text-danger"><span
                                                    class="text-danger">-</span><span id="yousaved" data-total=""
                                                    value="">0</span></th>
                                        </tr>
                                        {{-- @endif --}}
                                        <tr class="d-none disamount_row">
                                            <th class="pl-4">
                                                {{ customTrans('property_single.you_saved') }}</th>
                                            <th class="pl-4 text-right text-danger">
                                                <span class="text-danger">-</span>
                                                <span id="disvalue" class="text-danger">0</span>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td class="pl-4">
                                                Wallet
                                            </td>
                                            <td class="pl-4 text-right" style="color:red"><span id="wallet-value"
                                                    value="">0</span>
                                            </td>
                                        </tr>
                                        <tr class="summary-total">
                                            <th class="pl-4">
                                                {{ customTrans('property_single.total_include_vat') }}
                                            </th>
                                            <th class="pl-4 text-right"><span id="total" data-total=""
                                                    data-wallet-total='' value="">0</span></th>
                                        </tr>
                                        <tr class="d-none finalamount_row">
                                            <th class="pl-4">
                                                {{ customTrans('property_single.total_final_amount') }}
                                            </th>
                                            <th class="pl-4 text-right"><span id="final_amount" data-total=""
                                                    value="">0</span></th>
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="selectedPaymentId" id="selectedPaymentId" value="">
                <input id="hosting_id" name="hosting_id" type="hidden" value="{{ $result->host_id }}">
                <input id="userlogin" name="userlogin" type="hidden" value="{{ $user->id }}">
            </form>
        </div>
    </section>
@stop
@push('scripts')
    <script>
        const checkbox = document.getElementById('wallet-pay');
        const paymentOption = document.getElementById('walletPaymentOption');
        let bookingType = "{{ $result->booking_type }}";
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    paymentOption.classList.add('checked');
                } else {
                    paymentOption.classList.remove('checked');
                }
            });

            // Optional: ensure correct class on page load
            if (checkbox.checked) {
                paymentOption.classList.add('checked');
            }
        }

        function price_calculation(checkin = null, checkout = null) {
            var platformId = {{ $result->platform_id }};
            var get_checkin = checkin || $('#url_checkin').val() || moment().format('DD-MM-YYYY')
            var get_checkout = checkout || $('#url_checkout').val() || moment().format('DD-MM-YYYY')
            var property_id = $('#property_id').val()
            var dataURL = "{{ route('property.v2.price') }}"
            var guest = 1
            allothers = 0

            let _inDate = (typeof get_checkin === 'string') ? get_checkin : moment(get_checkin).format('DD-MM-YYYY')
            let _outDate = (typeof get_checkout === 'string') ? get_checkout : moment(get_checkout).format('DD-MM-YYYY')
            $.ajax({
                    url: dataURL,
                    data: {
                        '_token': "{{ csrf_token() }}",
                        'checkin': _inDate,
                        'checkout': _outDate,
                        'guest_count': guest,
                        'property_id': property_id
                    },
                    type: 'post',
                    dataType: 'json',
                    success: function(result) {
                        console.log(result);
                        if (result.total_night_price) {
                            if (platformId == 4) {
                                $('#nightPrice').text(result.total_night_price + result.cleaning_fee + ' ریال')
                            } else {
                                $('#nightPrice').text(result.total_night_price_after_discount_with_symbol)
                            }
                            $('#cleaningFee').text(result.cleaning_fee_with_discount_with_symbol)
                        }
                        $('.append_date').remove()
                        if (result.status == 'Not available') {
                            $('#max_nights').addClass('d-none')
                            $('#min_nights').addClass('d-none')
                            $('.book_btn').addClass('d-none')
                            $('#go_to_payment, .checkout-summary').addClass('d-none')
                            $('#book_it_disabled').removeClass('d-none')
                        } else if (result.status == 'minimum stay') {
                            $('.book_btn').addClass('d-none')
                            $('#go_to_payment, .checkout-summary').addClass('d-none')
                            $('#book_it_disabled').addClass('d-none')
                            $('#minimum_disabled').removeClass('d-none')
                            $('#minimum_disabled_message').text(result.minimum)
                        } else if (result.status == 'nights become min') {
                            $('.book_btn').addClass('d-none')
                            $('#book_it_disabled').addClass('d-none')
                            $('#go_to_payment, .checkout-summary').addClass('d-none')
                            $('#max_nights').addClass('d-none')
                            $('#min_nights').removeClass('d-none')
                            $('#min_night_message').text(result.min_nights)
                        } else if (result.status == 'nights become max') {
                            $('.book_btn').addClass('d-none')
                            $('#book_it_disabled').addClass('d-none')
                            $('#min_nights').addClass('d-none')
                            $('#go_to_payment, .checkout-summary').addClass('d-none')
                            $('#max_nights').removeClass('d-none')
                            $('#max_night_message').text(result.min_nights)
                        } else {
                            //showing custom price in info icon
                            if (!jQuery.isEmptyObject(result.different_price_dates)) {
                                var output = "{{ customTrans('listing_price.custom_price') }} <br />"
                                for (var ical_date in result.different_price_dates) {
                                    output += "{{ __('messages.account_transaction.date') }}: " + ical_date +
                                        " | {{ __('messages.utility.price') }}:" + "{{ $symbol }}" +
                                        result.different_price_dates[ical_date] + ' <br>'
                                }

                                $('#custom_price').attr('data-original-title', output)
                                $('#custom_price').tooltip({
                                    'placement': 'top'
                                })
                                $('#custom_price').show()

                            } else {
                                $('#custom_price').addClass('d-none')
                            }

                            var append_date = ''

                            for (var i = 0; i < result.date_with_price.length; i++) {
                                var dateParts = result.date_with_price[i]['date'].split('-')
                                var year = dateParts[0]
                                var month = dateParts[1]
                                var day = dateParts[2]

                                // Create the Date object using the parsed values
                                var date = new Date(year, month - 1, day)

                                if (!isNaN(date.getTime())) {
                                    var formattedDate = ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date
                                        .getMonth() + 1)).slice(-2) + '-' + date.getFullYear()

                                    append_date += '<tr class="append_date">' + '<td class="pl-4">' +
                                        formattedDate + '</td>' +
                                        '<td class="pl-4 text-right"> <span  id="" value="">' +
                                        result.date_with_price[i]['price'] + '</span></td>' + '</tr>'
                                }

                            }

                            var tableBody = $('table tbody')
                            // tableBody.first().prepend(append_date);


                            $('.additional_price').removeClass('d-none')
                            $('.security_price').removeClass('d-none')
                            $('.cleaning_price').removeClass('d-none')
                            $('.iva_tax').removeClass('d-none')
                            $('.accomodation_tax').removeClass('d-none')
                            $('#total_night_count').html(result.total_nights)
                            $('#per_night_price').html(result.per_night_price_with_symbol)

                            // $("#total_night_count").html(`${result.total_nights}` + `${result.per_night_price_with_symbol}`);
                            // $('#total_night_price').html(result.total_night_price_with_symbol);
                            $('#total_night_price').html(result
                                .total_day_discounted_price_not_weekly_monthly_discount_with_symbol)
                            // $('#total_night_price').html(result.total_night_price_after_discount_with_symbol)
                            // Check if the hidden input tag already exists
                            if ($('input[name="total_night_price"]').length > 0) {
                                // Update the value of the existing hidden input tag
                                $('input[name="total_night_price"]').val(result.total_night_price)
                                // Trigger click event on the "apply_coupon" tag
                                // $('#apply_coupon').trigger('click');
                            } else {
                                // Create a new hidden input element
                                var hiddenInput = $('<input>').attr({
                                    type: 'hidden',
                                    id: 'total_night_price_hidden',
                                    value: result.total_night_price
                                })

                                // Append the hidden input after the element with ID "hosting_id"
                                $('#hosting_id').after(hiddenInput)
                            }
                            if (result.yousaved_without_discount !== 0) {
                                $('#yousaved_main').css('display', 'table-row')
                                $('#yousaved').html(result.yousaved)
                            } else {
                                $('#yousaved_main').css('display', 'none')
                            }


                            if (result.service_fee_with_discount) {
                                $('#service_fee').html(result.service_fee_with_discount_with_symbol)
                            } else {
                                $('#service_fee').html(result.service_fee_with_symbol)
                            }

                            $('#discount').html(result.discount_with_symbol)


                            if (result.iva_tax != 0) {
                                $('#iva_tax').html(result.iva_tax_with_symbol)
                                allothers += result.iva_tax
                                consolelog('result.iva_tax= ' + result.iva_tax)
                            } else $('.iva_tax').addClass('d-none')

                            if (result.accomodation_tax != 0) {
                                $('#accomodation_tax').html(result.accomodation_tax_with_symbol)
                                allothers += result.accomodation_tax
                                consolelog('result.accomodation_tax=' + result.accomodation_tax)
                            } else $('.accomodation_tax').addClass('d-none')

                            if (result.additional_guest != 0) {
                                $('#additional_guest').html(result.additional_guest_fee_with_symbol)
                                allothers += result.additional_guest
                                consolelog('result.additional_guest= ' + result.additional_guest)
                            } else $('.additional_price').addClass('d-none')

                            if (result.security_fee != 0) {
                                $('#security_fee').html(result.security_fee_with_symbol)
                                allothers += result.security_fee + result.service_fee_security
                                consolelog('result.security_fee= ' + result.security_fee)
                            } else $('.security_price').addClass('d-none')

                            if (result.cleaning_fee != 0) {
                                $('#cleaning_fee').html(result.cleaning_fee_with_symbol)
                                cf_after_Service_charges = result.cleaning_fee + result.service_fee_cleaning
                                allothers += cf_after_Service_charges
                            } else $('.cleaning_price').addClass('d-none')

                            $('#total').html(result.total_with_discount_with_symbol)
                            $('#total').data('total', result.total_with_discount)
                            $('#total').data('wallet-total', result.total_with_discount)

                            consolelog('allothers' + allothers)
                            //$('#total_night_price').html(result.total_night_price);
                            $('#go_to_payment, .checkout-summary').removeClass('d-none')
                            $('#book_it_disabled').addClass('d-none')
                            $('#minimum_disabled').addClass('d-none')
                            $('#max_nights').addClass('d-none')
                            $('#min_nights').addClass('d-none')
                            $('.book_btn').removeClass('d-none')

                            if ($('#coupon').val()) {
                                applycoupon()
                            }
                        }

                        var host = "{{ $result->host_id == @Auth::guard('users')->user()->id ? '1' : '' }}"
                        if (host == '1') $('.book_btn').addClass('d-none')

                        //-----------WebEngage Integration------------
                        let user = DEFAULT_USER
                        let authcheck = '{{ auth()->check() }}'
                        if (authcheck) {
                            @auth
                            var isHost = @json(auth()->user()->is_host);
                        @endauth
                        user = isHost == true ? 'Host' : DEFAULT_USER
                        if(result.status == 'available'){
                            $('#table-checkout').removeClass('d-none');
                        }else{
                            $('#table-checkout').addClass('d-none');
                        }
                    }
                    placeSelectedPayload = {
                        'Name': '{{ $result->name }}',
                        'Unit Code': '{{ $result->property_code }}',
                        'Cost Per Night': result.per_night,
                        'Category Name': '{{ $property_type_name }}',
                        'User': user
                    }
                    $("#wallet-pay").trigger("change");
                },
                error: function(request, error) {
                    console.log(request, error);
                    // This callback function will trigger on unsuccessful action
                    $('#go_to_payment').prop('disabled', true);
                },
                complete: function() {
                    $('.price_table').removeClass('d-none')
                }
            })
        }

        function removeCoupon() {
            if (!!$('#coupon').val()) {
                price_calculation('', '', '')
            }
            $('#coupon').prop('readonly', false)
            $('.remove_coupon').addClass('d-none')
            $('.apply_coupon').removeClass('d-none')
            $('#coupon').val('')
            $('#coupon-res-message').addClass('text-success')
            $('#coupon-res-message').removeClass('text-danger')
            $('#coupon-res-message').text('')
            $('.discountTable').addClass('d-none')
            $('.disamount_row').addClass('d-none')
            $('.finalamount_row').addClass('d-none')
            allothers = 0
            handleWalletPayChange()

            // var handleWalletInterval = setInterval(() => {
            //     handleWalletPayChange()
            // }, 700);

            // setTimeout(function() {
            //     clearInterval(handleWalletInterval); // Stop the interval
            //     console.log("Timer stopped after 1 seconds");
            // }, 1000);
        }

        function applycoupon() {
            let userlogin = $('#userlogin').val()
            let amount = $('#total_night_price_hidden').val()
            let couponNumber = $('#coupon').val()
            let _this = $('.apply_coupon')
            let checkin = $('#url_checkin').val()
            let checkout = $('#url_checkout').val()

            if (couponNumber) {
                $.ajax({
                    url: "{{ route('couponCheck') }}",
                    type: 'post',
                    data: {
                        '_token': "{{ csrf_token() }}",
                        'couponNumber': couponNumber,
                        'propertyid': "{{ $result->id }}",
                        'amount': amount,
                        'user': userlogin,
                        'checkin': checkin,
                        'checkout': checkout
                    },
                    beforeSend: function() {
                        _this.next('.loadergif').removeClass('d-none')
                        _this.addClass('d-none')
                    },
                    success: function(res) {
                        if (res.status == 'success' || res.status == 'campaign') {
                            $('#coupon').prop('readonly', true)

                            if (document.querySelector('.remove_coupon').classList.contains('d-none')) {
                                $('.remove_coupon').removeClass('d-none')
                                $('.apply_coupon').addClass('d-none')
                            }
                            $('#coupon-res-message').text('')
                            $('#coupon-res-message').removeClass('text-danger')
                            $('#coupon-res-message').addClass('text-success')

                            if (res.status == 'campaign') {
                                $('#coupon-res-message').text(
                                    "{{ customTrans('payment.campaign_coupon_applied') }}")
                            } else {
                                $('#coupon-res-message').text("{{ customTrans('payment.coupon_applied') }}")
                            }


                            //Set value in table
                            const newVal = (res.amount_after_discount + allothers).toFixed(2)
                            $('#afteramount').text(`${newVal} SAR`)
                            $('#distype').text(res.discount_type)
                            $('#disvalue').text(res.discount_value)

                            $('#service_fee').text(`${res.new_servicefee} SAR`)
                            $('#total').text(`${newVal} SAR`)
                            $('#total').data('wallet-total', newVal)

                            $('.disamount_row').removeClass('d-none')
                            // $('.finalamount_row').removeClass('d-none')
                            $('#final_amount').text(`${newVal} SAR`)

                            $('.discountTable').removeClass('d-none')

                            handleWalletPayChange()


                        } else {
                            $('#coupon-res-message').addClass('text-danger')
                            $('#coupon-res-message').removeClass('text-success')
                            $('#coupon-res-message').text(res.message)
                            $('.discountTable').addClass('d-none')
                            $('.disamount_row').addClass('d-none')
                            $('.finalamount_row').addClass('d-none')
                            _this.removeClass('d-none')
                            $('.remove_coupon').addClass('d-none')
                        }

                        _this.next('.loadergif').addClass('d-none')
                        // _this.removeClass('d-none');
                    },
                    error: function(reject) {
                        if (reject.status === 422) {
                            var errors = $.parseJSON(reject.responseText)
                            $.each(errors, function(key, val) {
                                // $("#" + key + "_error").text(val[0]);
                                console.log(key, val)
                            })
                        }
                        _this.next('.loadergif').addClass('d-none')
                        _this.removeClass('d-none')
                        alert('Internet Connection Error!')
                    }
                })
            } else {
                $('#coupon-res-message').addClass('text-danger')
                $('#coupon-res-message').removeClass('text-success')
                $('#coupon-res-message').text('Please Enter Coupon Code')
            }
        }
        $(document).ready(function() {

            price_calculation($('#url_checkin').val(), $('#url_checkout').val(), $('#number_of_guests').val())
        });

        $(document).on('click', '#go_to_payment', function(event) {

            var couponMessage = $('#coupon-res-message');
            if ("{{ auth()->check() }}") {
                if ($('.apply_coupon').hasClass('d-none')) {
                    applyCouponPromise()
                        .then(response => {
                            proceedWithBooking(); // Call function to continue if coupon is successfully applied
                        })
                        .catch(error => {
                            return; // Continue booking process even if coupon fails
                        });
                } else {
                    proceedWithBooking(); // If no valid coupon, continue normally
                }
            } else {
                $('#reservationModal').modal('hide');
                $('#staticBackdrop').modal('show');
            }
        });

        function applyCouponPromise() {
            return new Promise((resolve, reject) => {
                let userlogin = $('#userlogin').val()
                let amount = $('#total_night_price_hidden').val()
                let couponNumber = $('#coupon').val()
                let _this = $('.apply_coupon')
                let checkin = $('#url_checkin').val()
                let checkout = $('#url_checkout').val()

                if (couponNumber) {
                    $.ajax({
                        url: "{{ route('couponCheck') }}",
                        type: 'post',
                        data: {
                            '_token': "{{ csrf_token() }}",
                            'couponNumber': couponNumber,
                            'propertyid': "{{ $result->id }}",
                            'amount': amount,
                            'user': userlogin,
                            'checkin': checkin,
                            'checkout': checkout
                        },
                        beforeSend: function() {
                            _this.next('.loadergif').removeClass('d-none');
                            _this.addClass('d-none');
                        },
                        success: function(res) {
                            if (res.status === 'success' || res.status === 'campaign') {
                                $('#coupon').prop('readonly', true)

                                if (document.querySelector('.remove_coupon').classList.contains(
                                        'd-none')) {
                                    $('.remove_coupon').removeClass('d-none');
                                    $('.apply_coupon').addClass('d-none');
                                }
                                $('#coupon-res-message').text('').removeClass('text-danger').addClass(
                                    'text-success');

                                if (res.status === 'campaign') {
                                    $('#coupon-res-message').text(
                                        "{{ customTrans('payment.campaign_coupon_applied') }}");
                                } else {
                                    $('#coupon-res-message').text(
                                        "{{ customTrans('payment.coupon_applied') }}");
                                }

                                // Set value in table
                                const newVal = (res.amount_after_discount + allothers).toFixed(2)
                                $('#afteramount').text(`${newVal} SAR`)
                                $('#distype').text(res.discount_type)
                                $('#disvalue').text(res.discount_value)
                                $('#service_fee').text(`${res.new_servicefee} SAR`)
                                $('#total').text(`${newVal} SAR`)
                                $('#total').data('wallet-total', newVal)
                                $('.disamount_row').removeClass('d-none')
                                $('#final_amount').text(`${newVal} SAR`)
                                $('.discountTable').removeClass('d-none')

                                handleWalletPayChange()

                                resolve(res) // Resolve the promise on success
                            } else {
                                $('#coupon-res-message').addClass('text-danger').removeClass(
                                    'text-success').text(res.message);
                                $('.discountTable, .disamount_row, .finalamount_row').addClass(
                                    'd-none');
                                // _this.removeClass('d-none');
                                // $('.remove_coupon').addClass('d-none');

                                reject(res) // Reject the promise if coupon is invalid
                            }

                            _this.next('.loadergif').addClass('d-none')
                        },
                        error: function(reject) {
                            _this.next('.loadergif').addClass('d-none');
                            _this.removeClass('d-none');
                            alert('Internet Connection Error!');
                            reject(reject); // Reject the promise on AJAX error
                        }
                    })
                } else {
                    $('#coupon-res-message').addClass('text-danger').removeClass('text-success').text(
                        'Please Enter Coupon Code');
                    reject({
                        message: 'Please Enter Coupon Code'
                    });
                }
            })
        }

        function proceedWithBooking() {
            var formData = $('#booking_form').serialize();
            $('#go_to_payment').prop('disabled', true);
            if (!$('#booking_form').data('submitted')) {
                let checkedRadioButton = $('input[name="pay"]:checked');
                var selectedPaymentId = checkedRadioButton.attr('id');
                checkedRadioButton.trigger('click');
                console.log(selectedPaymentId);
                $('#selectedPaymentId').val(selectedPaymentId);

                var apply_coupon_btn = $('#apply_coupon_btn');

                if (!apply_coupon_btn.hasClass('d-none')) {
                    $('#coupon').val('');
                }

                $('#booking_form').data('submitted', true);
                $('#booking_form').submit();
                $('#reservationModal').modal('hide');

                // Continue with the rest of your code
                let guestChild1 = isNaN(parseInt($('input[name="guest_child"]').val())) ? 0 : parseInt($(
                    'input[name="guest_child"]').val());
                let guestAdult1 = isNaN(parseInt($('input[name="guest_adult"]').val())) ? 0 : parseInt($(
                    'input[name="guest_adult"]').val());
                let service_fee_amount = "{{ numberFormat($result->property_price->price, 2) }}" * (parseInt(
                    "{{ $guest_service_charge }}") / 100);
                service_fee_amount = service_fee_amount.toFixed(2);

                // WebEngage Integration
                const currentDate = new Date();
                const day = currentDate.getDate();
                const month = currentDate.getMonth() + 1;
                const year = currentDate.getFullYear();
                const formattedDate = `${day}-${month}-${year}`;

                let authcheck = '{{ auth()->check() }}';
                if (authcheck) {
                    let payload = {
                        'Cost Per Night': parseInt("{{ numberFormat($result->property_price->price, 2) }}"),
                        'Service fees': parseInt(service_fee_amount),
                        'Date': formattedDate,
                        'Number of Adults': guestAdult1,
                        'Number of Children': guestChild1,
                        'Unit Code': "{{ $result->property_code }}",
                        'Name': "{{ $result->name }}",
                        @auth 'User': @json(auth()->user()->is_host) == true ? 'Host' : DEFAULT_USER
                    @endauth
                };
                // webEngageTracking(PLACE_RESERVATION_STARTED, payload);
            }

            // trackEvent('begin_checkout', {
            //     value: '{{ number_format($result->total_price, 2, '.', '') }}',
            //     currency: '{{ Session::get('currency') }}',
            //     item_id: '{{ $result->id }}',
            //     affiliation: '',
            //     discount: '{{ number_format($result->before_discount - $result->total_price, 2, '.', '') }}',
            //     item_type: '{{ $result->property_type_name }}',
            //     item_city_name: '{{ $result->city_name }}',
            //     item_host_id: '{{ $result->host_id }}',
            //     price: '{{ number_format($result->day_price, 2, '.', '') }}',
            //     quantity: '{{ $result->number_of_days }}',
            //     total_price: '{{ number_format($result->total_price, 2, '.', '') }}',
            // }, 'ga');

            // trackEvent('InitiateCheckout', {
            //     contents: [{
            //         content_id: '{{ $result->property_code }}',
            //         content_type: 'Property',
            //         content_name: '{{ $result->name }}'
            //     }],
            //     num_items: '{{ $result->number_of_days }}',
            //     currency: '{{ Session::get('currency') }}',
            //     value: $('input[name="total_night_price"]').val(),
            // }, ['tik']);

            // trackEvent('START_CHECKOUT', {
            //     price: $('input[name="total_night_price"]').val(),
            //     currency: '{{ Session::get('currency') }}',
            //     item_ids: ['{{ $result->property_code }}'],
            //     item_category: 'product',
            //     number_items: '{{ $result->number_of_days }}',
            //     user_email: '{{ auth()->user()?->email }}',
            //     user_phone_number: '{{ auth()->user()?->phone }}'
            // }, ['snap']);
        }
        }

        $(document).on('click', '.apply_coupon', function(e) {
            e.preventDefault()
            applycoupon()
        })

        $(document).on('click', '.applePay', function() {
            $('.coupon-main').show()
            $('#booking_type_1').val(1)
            $('.cardfields-note').addClass('d-none')
            $('.cardfields-div').addClass('d-none')
            $('#wallet-pay').prop('disabled', false)
        })


        $(document).on('click', '.tabbyPay', function() {
            $('.coupon-main').hide()
            removeCoupon()
            $('#booking_type_1').val(2)
            $('.cardfields-note').addClass('d-none')
            $('.cardfields-div').addClass('d-none')
            $('#wallet-pay').prop('checked', false)
            handleWalletPayChange()
            $('#wallet-pay').prop('disabled', true)
        })

        $(document).on('click', '.cardPay', function() {
            $('#wallet-pay').prop('disabled', false)
            $('.coupon-main').show()
            $('#booking_type_1').val(0)
            $('#paymentMethodId').val(2)
            if (!check_card && bookingType == 'instant') {
                $('.cardfields-note').removeClass('d-none')
                $('.cardfields-div').removeClass('d-none')
            }
        })
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>
    <script>
        const phoneInputFieldforSummary = document.getElementById("phone-input-signup");



        // Validation function
        function validatePhoneNumber() {
            const phoneErrorDiv = document.getElementById("phone-error");
            if (iti.isValidNumber()) {
                // Valid phone number
                phoneErrorDiv.style.display = "none";
            } else {
                // Invalid phone number
                phoneErrorDiv.style.display = "block";
            }
        }

        // Add event listener to validate the number on input or change
        phoneInputFieldforSummary.addEventListener('input', validatePhoneNumber);
        phoneInputFieldforSummary.addEventListener('change', validatePhoneNumber);
        const iti_summary = intlTelInput(document.getElementById("phone-input-signup"), {
            initialCountry: "sa",
            nationalMode: false,
            formatAsYouType: true,
            formatOnDisplay: true,
            separateDialCode: true,
            isValidNumberPrecise: true,
            onlyCountries: ['sa', 'ae', 'kw', 'qa', 'om', 'bh', 'iq', 'jo', 'lb', 'sy', 'ps', 'ye', 'dz', 'eg',
                'ly', 'ma', 'tn', 'sd', 'bf'
            ],
        });
    </script>
@endpush
