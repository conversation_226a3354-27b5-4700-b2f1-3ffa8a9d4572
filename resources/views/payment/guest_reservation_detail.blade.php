{{-- {{ dd(request()->route()) }} --}}
@extends('template')

@push('css')
    <style>
        #footerforPDF {
            display: none !important;
        }
    </style>
@endpush

@php
    use Carbon\Carbon;
@endphp

@section('main')
    <div class="gr-detail-main">
        <div class="row g-0">
            <div class="col-xxl-3 col-xl-4 col-lg-4 col-md-5">
                <div class="gr-detail-sec">
                    <div class="gr-detail-sec-inner grey-scroller">
                        <h4 class="gr-ds-margin gr-card-ptb py-3">{{ customTrans('host_dashboard.reservation_detail') }}</h4>
                        <div class="swiper gr-ds-propImages gr-card-ptb gr-ds-margin">
                            <div class="swiper-wrapper">
                                @for ($i = 0; $i < (count($booking->properties->images) > 4 ? 4 : count($booking->properties->images)); $i++)
                                    <div class="swiper-slide">
                                        <img src="{{ asset($booking->properties->property_photos[$i]['photo']) }}"
                                            alt="property-image-{{ $i + 1 }}" class="parallax-image" alt="">
                                    </div>
                                @endfor

                                {{-- <div class="swiper-slide">
                                    <img src="{{asset('images/p2-webp')}}" alt="property-image">
                                </div>
                                <div class="swiper-slide">
                                    <img src="{{asset('images/p3.png')}}" alt="property-image">
                                </div>
                                <div class="swiper-slide">
                                    <img src="{{asset('images/p4.png')}}" alt="property-image">
                                </div> --}}
                            </div>
                            <div class="swiper-pagination"></div>
                        </div>
                        <div class="gr-ds-body">
                            <div class="gr-ds-checkDate df-align-center gr-ds-margin gr-card-ptb gr-card-bd">
                                <div class="gr-ds-checkIn gr-ds-cd-inner">
                                    <p class="mb-1 check">{{ customTrans('header.check_in') }}</p>
                                    <p class="mb-0 date">{{ Carbon::parse($booking->start_date)->format('D, M j') }}</p>
                                    <p class="mb-0 time">{{ Carbon::parse($booking->checkin_time)->format('g:i A') }}</p>
                                </div>
                                <div class="gr-ds-checkOut gr-ds-cd-inner">
                                    <p class="mb-1 check">{{ customTrans('header.check_out') }}</p>
                                    <p class="mb-0 date">{{ Carbon::parse($booking->end_date)->format('D, M j') }}</p>
                                    <p class="mb-0 time">{{ Carbon::parse($booking->checkout_time)->format('g:i A') }}</p>
                                </div>
                            </div>
                            <ul class="gr-ds-hostInfo">
                                <li class="gr-ds-margin gr-card-ptb gr-card-bd">
                                    @if ((request()->booking == 'incoming' || request()->booking == 'ongoing') && $booking->status == 'Accepted')
                                        <a href="#" class="df-align-center" data-bs-toggle="modal"
                                            data-bs-target="#property-location-md">
                                            <i class="ri-map-pin-line"></i>
                                            <div class="gr-ds-hostInfo-inner">
                                                <h6>{{ customTrans('reservation.getting_there') }}</h6>
                                                <p class="mb-0">Address:
                                                    {{ $booking->properties->property_address->address_line_1 ?? 'null' }}
                                                </p>
                                            </div>
                                        </a>
                                    @else
                                        <div href="#" class="df-align-center">
                                            <i class="ri-map-pin-line"></i>
                                            <div class="gr-ds-hostInfo-inner">
                                                <h6>{{ customTrans('reservation.getting_there') }}</h6>
                                                <p class="mb-0">{{ customTrans('account_preference.address') }}:
                                                    {{ Illuminate\Support\Str::limit($booking->properties->property_address->address_line_1 ?? 'No Address Available', 5, '...') }}
                                                </p>
                                            </div>
                                        </div>
                                    @endif
                                </li>
                                <li class="gr-ds-margin gr-card-ptb gr-card-bd">
                                    <a href="#" class="df-align-center" data-bs-toggle="modal"
                                        data-bs-target="#gr-ds-hoseRules">
                                        <i class="ri-book-open-line"></i>
                                        <div class="gr-ds-hostInfo-inner">
                                            <h6>{{ customTrans('listing_sidebar.things_to_know') }}</h6>
                                            <p class="mb-0">{{ customTrans('reservation.Instructions_and_house_rules') }}
                                            </p>
                                        </div>
                                    </a>
                                </li>
                                @if ($booking->status == 'Accepted')
                                    <li class="gr-ds-margin gr-card-ptb gr-card-bd df-align-center">
                                        <a href="{{ route('properties.chat.view', ['type' => 'guest', 'chat_head_id' => $booking->chat_head_id]) }}"
                                            class="df-align-center">
                                            <i class="ri-message-3-line"></i>
                                            <div class="gr-ds-hostInfo-inner">
                                                <h6>{{ customTrans('reservation.message_your_host') }}</h6>
                                                <p class="mb-0">{{ $booking->host->getFullNameAttribute() }}</p>
                                            </div>
                                        </a>
                                    </li>
                                @endif
                                <li class="gr-ds-margin gr-card-ptb gr-card-bd df-align-center">
                                    <a href="{{ route('property.single', ['slug' => $booking->properties?->slug]) }}"
                                        class="df-align-center">
                                        <i class="ri-home-4-line"></i>
                                        <div class="gr-ds-hostInfo-inner">
                                            <h6>{{ customTrans('reservation.your_place') }}</h6>
                                            <p class="mb-0">{{ $booking->properties->name }}</p>
                                        </div>
                                    </a>
                            </ul>
                            <div class="gr-ds-reservDetail gr-ds-detailContent gr-ds-paddingtb-border">
                                <h5 class="gr-ds-dc-title">{{ customTrans('host_dashboard.reservation_detail') }}</h5>
                                <div class="gr-ds-dc-inner gr-card-bd">
                                    <p class="gr-ds-dc-innerTitle">{{ customTrans('reservation.reservation_number') }}</p>
                                    <div class="df-align-center">
                                        <input type="text" value="{{ $booking->code }}" id="copyText" readonly>
                                        <button onclick="copyToClipboard('bookingcode', this)"
                                            class="transparent-btn text-copy-btn">
                                            <span class="textCopy-tooltip">{{ customTrans('reservation.copied') }}</span>
                                            <i class="ri-file-copy-line"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="gr-ds-dc-inner">
                                    <p class="gr-ds-dc-innerTitle">{{ customTrans('header.unit_code') }}</p>
                                    <p class="gr-ds-dc-innerContent">
                                        {{ $booking->properties->property_code }}
                                    </p>
                                </div>
                            </div>
                            <div class="gr-ds-termPolicies gr-ds-detailContent gr-ds-paddingtb-border">
                                <h5 class="gr-ds-dc-title">{{ customTrans('reservation.terms_policies') }}</h5>
                                <div class="gr-ds-dc-inner gr-card-bd">
                                    <p class="gr-ds-dc-innerTitle">
                                        {{-- Booking Terms (by {{ $booking->host->getFullNameAttribute() }}) --}}
                                        {{ customTrans('reservation.booking_terms_host', [':host_name' => $booking->host->getFullNameAttribute()]) }}
                                    </p>
                                    <ul class="mb-0">

                                        <li class="gr-ds-dc-innerContent">{{ $booking->properties->cancellation }}</li>
                                    </ul>
                                </div>
                                <div class="gr-ds-dc-inner">
                                    <p class="gr-ds-dc-innerTitle">{{ customTrans('reservation.cancel_refund_policy') }}
                                    </p>
                                    <p class="gr-ds-dc-innerContent">
                                        @if (app()->getLocale() == 'ar')
                                            @switch($booking->properties?->cancellation)
                                                @case('Flexible')
                                                    {{ 'استرداد كامل المبلغ قبل يوم واحد من الوصول' }}
                                                @break

                                                @case('Moderate')
                                                    {{ 'رد المبلغ كاملاً قبل خمسة أيام من الوصول' }}
                                                @break

                                                @case('Firm')
                                                    {{ 'رد كامل للمبلغ المدفوع للإلغاءات التي تتم حتى قبل 30
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                يومًا من تاريخ الوصول. إذا تم الحجز قبل أقل من 30 يومًا
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                من تسجيل
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                الوصول، فسيتم استرداد المبلغ بالكامل للإلغاءات التي تتم
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                في غضون 48 ساعة بعد إتمام الحجز وقبل 14 يومًا على الأقل
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                من تسجيل
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                الوصول. وبعد ذلك، يتم رد 50% من المبلغ المدفوع إذا تم
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                الإلغاء حتى 7 أيام قبل تسجيل الوصول. ولن يتم رد المبالغ
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                المدفوعة
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                للإلغاءات التي تتم بعد ذلك.' }}
                                                @break

                                                @case('Strict')
                                                    {{ 'استرداد كامل للإلغاءات التي تمت في غضون 48 ساعة من الحجز ، إذا كان تاريخ تسجيل الوصول بعد 14 يومًا على الأقل. استرداد 50٪ للإلغاء الذي تم إجراؤه قبل 7 أيام على الأقل من تسجيل الوصول. لا يوجد استرداد لعمليات الإلغاء خلال 7 أيام من تسجيل الوصول' }}
                                                @break

                                                @default
                                            @endswitch
                                        @else
                                            @switch($booking->properties?->cancellation)
                                                @case('Flexible')
                                                    {{ 'Full refund 1 day prior to arrival' }}
                                                @break

                                                @case('Moderate')
                                                    {{ 'Full refund 5 days prior to arrival' }}
                                                @break

                                                @case('Firm')
                                                    {{ 'Full refund for cancellations up to 30 days before                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 days before check-in. No refund after that.' }}
                                                @break

                                                @case('Strict')
                                                    {{ 'Full refund for cancellations made within 48 hours of booking, if the check-in date is at least 14 days away. 50% refund for cancellations made at least 7 days before check-in. No refunds for cancellations made within 7 days of check-in.' }}
                                                @break

                                                @default
                                            @endswitch
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="gr-ds-htInfo gr-ds-detailContent gr-ds-paddingtb-border">
                                <h5 class="gr-ds-dc-title">{{ customTrans('reservation.host_information') }}</h5>
                                <div class="gr-ds-dc-inner gr-card-bd">
                                    <p class="gr-ds-dc-innerTitle">{{ $booking->host->getFullNameAttribute() }}</p>

                                </div>
                                @if ($booking->status == 'Accepted')
                                    <a href="#" class="gr-ds-dc-inner df-align-center" data-bs-toggle="modal"
                                        data-bs-target="#contact-host-md">
                                        <div class="gr-ds-contIcon df-align-center">
                                            <i class="ri-phone-line gr-ds-dc-smIcon"></i>
                                            <p class="gr-ds-dc-innerContent">{{ customTrans('contact_host.contact_host') }}
                                            </p>
                                        </div>
                                        <i class="ri-arrow-right-s-lin rtl-leftArrowRotate gr-ds-dc-lgIcon"></i>
                                    </a>
                                @endif
                            </div>
                            <div class="gr-ds-reservSummary gr-ds-detailContent gr-ds-paddingtb-border">
                                <h5 class="gr-ds-dc-title">{{ customTrans('reservation.reservation_summary') }}</h5>
                                <ul class="gr-card-bd">
                                    <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                        <p class="gr-ds-dc-innerTitle mb-0">
                                            {{ $booking->total_night }}
                                            {{ customTrans('reservation.per_night_sar', [':booking_base_price' => number_format((($booking->base_price)/$booking->total_night),2)]) }}
                                        </p>

                                        <p class="gr-ds-dc-innerContent">
                                            {{ $booking->base_price }}
                                            {{ customTrans('utility.sar') }}
                                        </p>

                                    </li>
                                    @if($booking->discount_type == 'weekly_discount')
                                    <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                        <p class="gr-ds-dc-innerTitle mb-0">
                                            {{ customTrans('host_reservation.weekly_discount_applied') }}
                                        </p>
                                        <p class="gr-ds-dc-innerContent text-danger">
                                            - {{ $booking->host_discount_amount }}
                                            {{ customTrans('utility.sar') }}
                                        </p>
                                    </li>
                                    @elseif($booking->discount_type == 'monthly_discount')
                                    <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                        <p class="gr-ds-dc-innerTitle mb-0">
                                            {{ customTrans('host_reservation.monthly_discount_applied') }}
                                        </p>
                                        <p class="gr-ds-dc-innerContent text-danger">
                                           - {{ $booking->host_discount_amount }}
                                            {{ customTrans('utility.sar') }}
                                        </p>
                                    </li>
                                    @endif
                                    <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                        <p class="gr-ds-dc-innerTitle mb-0">
                                            {{ customTrans('host_reservation.darent_discount') }}
                                        </p>
                                        <p class="gr-ds-dc-innerContent">
                                            -{{ $booking->total_discount }}
                                            {{ customTrans('utility.sar') }}
                                        </p>

                                    </li>

                                    <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                        <p class="gr-ds-dc-innerTitle mb-0">
                                            {{ customTrans('property_single.cleaning_fee') }}</p>
                                        <p class="gr-ds-dc-innerContent">{{ $booking->cleaning_charge }}
                                            {{ customTrans('utility.sar') }}
                                        </p>
                                    </li>

                                    <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                        <p class="gr-ds-dc-innerTitle mb-0">
                                            {{ customTrans('property_single.service_fee') }}</p>
                                        <p class="gr-ds-dc-innerContent">{{ $booking->service_charge }}
                                            {{ customTrans('utility.sar') }}
                                        </p>
                                    </li>

                                    @if ($booking->properties->platform_id == 4)
                                        <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                            <p class="gr-ds-dc-innerTitle mb-0">
                                                {{ customTrans('property_single.iva_tax') }}</p>
                                            <p class="gr-ds-dc-innerContent">{{ $booking->iva_tax }}
                                                {{ customTrans('utility.sar') }}
                                            </p>
                                        </li>
                                    @endif
                                    {{-- @if ($wallet_deduction)
                                        <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                            <p class="gr-ds-dc-innerTitle mb-0">
                                                Paid By Wallet</p>
                                            <p class="gr-ds-dc-innerContent">
                                                {{ $booking->pay_by_wallet ?? '0.0' }}
                                                {{ $wallet_deduction }}
                                                {{ customTrans('utility.sar') }}
                                            </p>
                                        </li>
                                    @endif --}}
                                    <?php
                                    $WalletAmount = $booking->pay_by_wallet ?? 0.0;
                                    $depositedAmount = 0;
                                    if($booking->shifted_booking == 1){
                                        if($booking->transactions->count() > 0){
                                            foreach($booking->transactions as $transaction) {
                                                if($transaction->payment_type_id == \App\Enums\PaymentTypeEnum::Wallet && $transaction->transaction_type_id == \App\Models\TransactionTypes::CREDIT && $transaction->transaction_category_id == \App\Models\TransactionCategories::WALLET_DEPOSIT){
                                                    $depositedAmount += $transaction->amount;

                                                }
                                            }

                                        }
                                    }
                                    ?>
                                    @if ($booking->status == 'Accepted')
                                        <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                            <p class="gr-ds-dc-innerTitle mb-0">{{ customTrans('sidenav.wallet') }}</p>
                                            <p class="gr-ds-dc-innerContent">
                                                -{{ $WalletAmount }}
                                                {{ customTrans('utility.sar') }}
                                            </p>
                                        </li>

                                        @if($booking->shifted_booking == 1)
                                            <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                                <p class="gr-ds-dc-innerTitle mb-0">{{ customTrans('sidenav.deposited_wallet') }}</p>
                                                <p class="gr-ds-dc-innerContent">
                                                    {{ $depositedAmount }}
                                                    {{ customTrans('utility.sar') }}
                                                </p>
                                            </li>
                                        @endif
                                    @endif

                                    <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                        <p class="gr-ds-dc-innerTitle mb-0">
                                            {{ customTrans('property_single.total_include_vat') }}</p>
                                        @if ($booking->status == 'Accepted')
                                            {{-- @if (!$wallet_deduction) --}}
                                            <p class="gr-ds-dc-innerContent">
                                                {{ ($booking->total_with_discount != 0 ? $booking->total_with_discount : $booking->total) - $WalletAmount }}
                                                {{ customTrans('utility.sar') }}
                                            </p>
                                        @else
                                            <p class="gr-ds-dc-innerContent">
                                                {{ $booking->total_with_discount != 0 ? $booking->total_with_discount : $booking->total }}
                                                {{ customTrans('utility.sar') }}
                                            </p>
                                        @endif
                                    </li>
                                </ul>
                                @if ($booking->status == 'Accepted')
                                    <a href="{{ route('user.receipt', ['code' => $booking->code]) }}"
                                        class="gr-ds-dc-inner df-align-center" target="_blank">
                                        <div class="gr-ds-contIcon df-align-center">
                                            <i class="ri-file-paper-2-line gr-ds-dc-smIcon"></i>
                                            <p class="gr-ds-dc-innerContent">
                                                {{ customTrans('reservation.view_reservation_summary') }}</p>
                                        </div>
                                        <i class="ri-arrow-right-s-line gr-ds-dc-lgIcon rtl-leftArrowRotat"></i>
                                    </a>
                                @elseif(
                                    ($booking->status == 'Processing' || $booking->status == 'Unpaid') &&
                                        $booking->status != 'Expired' &&
                                        $booking->payment_method_id != 4)
                                    @if ($property_status == 'Not available')
                                        <a class="property_is_booked">
                                            <button class="theme-btn w-100 mb-3">
                                                <i class="fab fa-cc-amazon-pay"></i>
                                                {{ customTrans('payment.make_payment') }}
                                            </button>
                                        </a>
                                    @else
                                        <a class="coupon_code_btn" data-bookingId="{{ $booking->id }}"
                                            data-propertyId="{{ $booking->property_id }}"
                                            data-amount="{{ $booking->base_price }}"
                                            data-cleaning_charge="{{ $booking->cleaning_charge }}"
                                            data-security_charges="{{ $booking->security_money }}"
                                            data-additional_guest="{{ $booking->additional_guest }}"
                                            data-iva_tax="{{ $booking->iva_tax }}"
                                            data-accomodation_tax="{{ $booking->accomodation_tax }}"
                                            data-has_discount="{{ $booking->properties->property_discount ? 'true' : 'false' }}"
                                            data-discount_applied="{{ $booking->total_with_discount ? 'true' : 'false' }}">

                                            <button class="theme-btn w-100 mb-3">
                                                <i class="fab fa-cc-amazon-pay"></i>
                                                {{ customTrans('payment.make_payment') }}
                                            </button>
                                        </a>
                                    @endif
                                    {{-- <a class="rcpt-btn coupon_code_btn theme-btn text-center fc-white" --}}

                                @endif
                                @if ($booking->end_date <= date(now()->toDateString()) && $booking->status == 'Accepted')

                                    @if (!!$booking->review)
                                        <div class="gr-ds-dc-inner">
                                            <div class="gr-ds-contIcon df-align-center">
                                                <i class="ri-star-line gr-ds-dc-lgIcon color-success"></i>
                                                <p class="gr-ds-dc-innerContent text-success">
                                                    {{ customTrans('reservation.rated') }}</p>
                                            </div>
                                        </div>
                                        {{-- <div class="gr-ds-dc-inner df-align-center">
                                        <span class="list-rate text-success">{{ customTrans('reservation.rated') }}</span>
                                        <span><strong>{{ customTrans('account_transaction.amount') }}
                                                :</strong>
                                            {{ $booking->total_with_discount ? $booking->total_with_discount : $booking->total }}</span>

                                    </div> --}}
                                    @else
                                        @if ($booking->end_date <= date(now()->toDateString()) && $booking->status == 'Accepted')
                                            <button href="#" id="review-btn"
                                                class="gr-ds-dc-inner df-align-center guestrate transparent-btn justify-content-between w-100"
                                                data-propertyid="{{ $booking->properties->id }}"
                                                data-propertyhostid="{{ $booking->properties->host_id }}"
                                                data-bookingid="{{ $booking->id }}"
                                                onclick="setReviewById('{{ $booking->id }}', 'booking')">
                                                <div class="gr-ds-contIcon df-align-center">
                                                    <i class="ri-star-line gr-ds-dc-lgIcon"></i>
                                                    <p class="gr-ds-dc-innerContent">{{ customTrans('reservation.rate') }}
                                                    </p>
                                                    {{-- <span><strong>{{ customTrans('account_transaction.amount') }}
                                                        :</strong>
                                                    {{ $booking->total_with_discount ? $booking->total_with_discount : $booking->total }}</span> --}}
                                                </div>
                                                <i class="ri-arrow-right-s-line gr-ds-dc-lgIcon rtl-leftArrowRotat"></i>
                                            </button>

                                            {{-- <button
                                                    class="list-rate text-success mb-0 guestrate transparent-btn d-flex align-items-baseline"
                                                    data-propertyid="{{ $booking->properties->id }}"
                                                    data-propertyhostid="{{ $booking->properties->host_id }}"
                                                    data-bookingid="{{ $booking->id }}"
                                                    onclick="setReviewById('{{ $booking->id }}', 'booking')">
                                                    <img class="me-1"
                                                        src="{{ asset('icons/green-rate.svg') }}">{{ customTrans('reservation.rate') }}</button>
                                                <span><strong>{{ customTrans('account_transaction.amount') }}
                                                        :</strong>
                                                    {{ $booking->total_with_discount ? $booking->total_with_discount : $booking->total }}</span> --}}

                                            <!-- <button class="">Rate</button> -->
                                        @endif
                                    @endif
                                @endif
                                {{-- <button class="theme-btn w-100">
                                    {{ customTrans('payment.make_payment') }}
                                </button> --}}
                            </div>
                            @if ($booking->status == 'Accepted')
                                <div class="gr-ds-reservPayment gr-ds-detailContent gr-ds-paddingtb-border">
                                    <h5 class="gr-ds-dc-title">{{ customTrans('reservation.payment_information') }}</h5>
                                    <ul class="">
                                        <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                            <p class="gr-ds-dc-innerTitle mb-0">{{ customTrans('wallet.payment_method') }}
                                            </p>
                                            <p class="gr-ds-dc-innerContent">
                                                {{ $booking->paymentType->title ?? customTrans('payment.card_payment') }}
                                            </p>
                                        </li>
                                        <li class="gr-ds-dc-inner df-align-center justify-content-between">
                                            <p class="gr-ds-dc-innerTitle mb-0">{{ customTrans('reservation.paid_full') }}
                                            </p>
                                            <p class="gr-ds-dc-innerContent">
                                                {{ $booking->total_with_discount != 0 ? $booking->total_with_discount : $booking->total }}
                                                {{ customTrans('utility.sar') }}
                                            </p>
                                        </li>
                                    </ul>
                                </div>
                            @endif
                            <div class="gr-ds-reservSupport gr-ds-detailContent gr-ds-paddingtb-border">
                                <h3 class="gr-ds-dc-title">{{ customTrans('reservation.get_support_anytime') }}</h3>
                                <p class="gr-ds-dc-innerContent">{{ customTrans('reservation.if_you_need_help') }}</p>
                                {{-- <a href="{{ route('customerServices') }}" class="gr-ds-dc-inner df-align-center">
                                    <div class="gr-ds-contIcon df-align-center">
                                        <i class="ri-customer-service-2-line gr-ds-dc-smIcon"></i>
                                        <p class="gr-ds-dc-innerContent">{{ customTrans('account_mobile.customer_services') }}</p>
                                    </div>
                                    <i class="ri-arrow-right-s-line gr-ds-dc-lgIcon rtl-leftArrowRotat"></i>
                                </a> --}}
                                <a href="{{ route('tickets.create_ticket', ['bookingid' => $booking->id]) }}"
                                    class="gr-ds-dc-inner df-align-center">
                                    <div class="gr-ds-contIcon df-align-center">
                                        <i class="ri-error-warning-line gr-ds-dc-smIcon"></i>
                                        <p class="gr-ds-dc-innerContent">{{ customTrans('reservation.report_problem') }}
                                        </p>
                                    </div>
                                    <i class="ri-arrow-right-s-line gr-ds-dc-lgIcon rtl-leftArrowRotat"></i>
                                </a>

                                @php
                                    $daysBeforeCheckIn = now()->diffInDays($booking->start_date, false);
                                    $hoursSinceBooking = now()->diffInHours($booking->created_at);
                                    $isNotCancelled = !in_array($booking->status, ['Cancelled', 'Expired']);
                                @endphp

                                @if ($booking->properties->cancellation == 'Flexible' && $booking->start_date > date(now()->toDateString()) && $isNotCancelled)
                                    <button id="btn_cancel_booking"
                                        class="transparent-bnt theme-btn w-100 host guest-cancel-reserv-btn">
                                        {{ customTrans('reservation.cancel_reservation') }}
                                    </button>
                                @elseif ($booking->properties->cancellation == 'Moderate' && $daysBeforeCheckIn >= 5 && $isNotCancelled)
                                    <button id="btn_cancel_booking"
                                        class="transparent-bnt theme-btn w-100 host guest-cancel-reserv-btn">
                                        {{ customTrans('reservation.cancel_reservation') }}
                                    </button>
                                @elseif ($booking->properties->cancellation == 'Strict' && $isNotCancelled)
                                    @if ($daysBeforeCheckIn >= 30 || ($daysBeforeCheckIn >= 14 && $hoursSinceBooking <= 48) || $daysBeforeCheckIn >= 7)
                                        <button id="btn_cancel_booking"
                                            class="transparent-bnt theme-btn w-100 host guest-cancel-reserv-btn">
                                            {{ customTrans('reservation.cancel_reservation') }}
                                        </button>
                                    @endif
                                @elseif ($booking->properties->cancellation == 'Firm' && $isNotCancelled)
                                    @if (($daysBeforeCheckIn >= 14 && $hoursSinceBooking <= 48) || $daysBeforeCheckIn >= 7)
                                        <button id="btn_cancel_booking"
                                            class="transparent-bnt theme-btn w-100 host guest-cancel-reserv-btn">
                                            {{ customTrans('reservation.cancel_reservation') }}
                                        </button>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-9 col-xl-8 col-lg-8 col-md-7">
                <div class="gr-ds-checkinInfo gr-ds-detailContent">
                    <div id="displaymap"
                    @if($booking->status != 'Accepted')
                    style="pointer-events: none;" @endif
                    class="gr-ds-dc-inner">

                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- property location modal --}}
    <div class="modal fade dubai-ff modal-dr-bottom gr-card-infoMd" id="property-location-md" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="property-location-md" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-bd-content">
                <div class="modal-body cm-bd-body">
                    <div class="gr-ci-infoBtn">
                        <a href="https://www.google.com/maps/search/?api=1&query={{ $booking->properties->property_address->latitude }},{{ $booking->properties->property_address->longitude }}"
                            class="gr-card-bd df-align-center">
                            <img src="{{ asset('images/google-maps.png') }}" alt="">
                            <span>{{ customTrans('reservation.google_map') }}</span>
                        </a>
                        @if ($showPayOpts)
                            <a href="https://maps.apple.com/?q={{ $booking->properties->property_address->latitude }},{{ $booking->properties->property_address->longitude }}"
                                class="gr-card-bd df-align-center">
                                <img src="{{ asset('images/apple-maps.png') }}" alt="">
                                <span>{{ customTrans('reservation.apple_map') }}</span>
                            </a>
                        @endif
                    </div>
                </div>
                <div class="modal-footer cm-bd-footer gr-ci-infoFooter">
                    <button type="button" class="grey-btn" data-bs-dismiss="modal" aria-label="Close">
                        {{ customTrans('booking_my.cancel') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
    {{-- contact host modal --}}
    <div class="modal fade dubai-ff modal-dr-bottom gr-card-infoMd" id="contact-host-md" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="contact-host-md" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-bd-content">
                <div class="modal-body cm-bd-body">
                    <div class="gr-ci-infoBtn">
                        <a href="{{ route('properties.chat.view', ['type' => 'guest', 'chat_head_id' => $booking->chat_head_id]) }}"
                            class="gr-card-bd df-align-center">
                            <i class="ri-message-3-line"></i>
                            <span>{{ customTrans('reservation.through_chat') }}</span>
                        </a>
                        @if (
                            // (request()->booking == 'incoming' || request()->booking == 'ongoing') &&
                            $booking->status == 'Accepted')
                            <a href="tel:{{ $booking->host->formatted_phone }}" class="gr-card-bd df-align-center">
                                <i class="ri-phone-line"></i>
                                <span>{{ customTrans('reservation.call_host') }}</span>
                            </a>
                            {{-- @else
                        <div class="gr-card-bd df-align-center gr-ci-no-phone-num">
                            <i class="ri-phone-line"></i>
                            <span>{{ customTrans('reservation.you_will_see_contact') }}</span>
                        </div> --}}
                        @endif
                    </div>
                </div>
                <div class="modal-footer cm-bd-footer gr-ci-infoFooter">
                    <button type="button" class="grey-btn" data-bs-dismiss="modal" aria-label="Close">
                        {{ customTrans('booking_my.cancel') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade dubai-ff modal-dr-bottom" id="gr-ds-hoseRules" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="gr-ds-hoseRules" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-bd-content">
                <div class="modal-header cm-bd-header">
                    <h5 class="w-100 text-center mb-0" id="gr-ds-hoseRules">
                        {{ customTrans('listing_sidebar.things_to_know') }}</h5>
                    <p class="calendar-m-msg" id="wishlist-model-message"></p>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body cm-bd-body">
                    <h4 class="gr-ds-dc-innerContent">{{ customTrans('payment.house_rule') }}</h4>
                    <p class="mb-0">{{ customTrans('reservation.You_will_be_staying_in_someone') }}</p>
                    <div class="gr-ds-hr-checkInOut">
                        <div class="gr-ds-detailContent">
                            <h5 class="gr-ds-dc-title">{{ customTrans('property_single.check_in_out') }}</h5>
                            <ul class="gr-ds-hr-checkInOut-list mb-0">
                                <li class="gr-ds-dc-inner df-align-center gr-card-bd">
                                    <i class="ri-time-line gr-ds-dc-lgIcon"></i>
                                    <p class="gr-ds-dc-innerContent">{{ customTrans('property_single.check_in') }}
                                        <span>{{ Carbon::parse($booking->checkin_time)->format('g:i A') }}</span>
                                    </p>
                                </li>
                                <li class="gr-ds-dc-inner df-align-center gr-card-bd">
                                    <i class="ri-time-line gr-ds-dc-lgIcon"></i>
                                    <p class="gr-ds-dc-innerContent">{{ customTrans('property_single.check_out') }}
                                        <span>{{ Carbon::parse($booking->checkout_time)->format('g:i A') }}</span>
                                    </p>
                                </li>
                                <li class="gr-ds-dc-inner df-align-center">
                                    <i class="ri-door-open-line gr-ds-dc-lgIcon"></i>
                                    <p class="gr-ds-dc-innerContent">Self check-in with Smart lock</p>
                                </li>
                            </ul>
                        </div>
                        <div class="gr-ds-detailContent">
                            <h5 class="gr-ds-dc-title">{{ customTrans('reservation.during_your_stay') }}</h5>
                            <ul class="gr-ds-hr-checkInOut-list mb-0">
                                <li class="gr-ds-dc-inner df-align-center">
                                    <i class="ri-user-line gr-ds-dc-lgIcon"></i>
                                    <p class="gr-ds-dc-innerContent"><span>{{ $booking->guest }}</span> guest maximum</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (isset($booking) )
        {{-- new rating modal --}}
        <div class="modal fade modal-dr-bottom" id="rating" data-bs-backdrop="static" data-bs-keyboard="false"
            tabindex="-1" aria-labelledby="ratingLabel" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable bt-grey-scroller">
                <div class="modal-content cm-bd-content">
                    <div class="modal-header cm-bd-header">
                        <h6 class="w-100 text-center mb-0">Chalet for recreation and relaxation</h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body cm-bd-body">
                        <div class="reserv-reviews mb-3">
                            <div class="reviews">
                                <div class="row">
                                    <div class="col-md-4 col-12">
                                        <div class="ls-img reserv-prop-img">
                                            <img src="{{ asset($booking->properties->cover_photo) }}">
                                        </div>
                                    </div>
                                    <div class="col-md-8 col-12">
                                        <div class="ls-content">
                                            <span>{{ customTrans('reservation.booking_id') . ': ' . strtoUpper($booking->code) }}
                                            </span>
                                            <div>
                                                {{ customTrans('reservation.status') . ': ' . strtoUpper($booking->status) }}
                                            </div>

                                            <span><strong>{{ customTrans('account_transaction.amount') }}
                                                    :</strong>
                                                {{ $booking->total_with_discount ? $booking->total_with_discount : $booking->total }}</span>
                                            <div class="date-mark m-0">
                                                <span class="st-date">{{ $booking->startdate_dmy }}</span>
                                                <span class="date-ar"><i class="fas fa-angle-right"></i></span>
                                                <span class="end-date">{{ $booking->enddate_dmy }}</span>
                                            </div>
                                        </div>
                                        <div class="rs-title justify-content-start">
                                            <h4 class="reserv-product-detail mb-0 fs-16">
                                                {{ $booking->properties->bedrooms . ' ' . trans('messages.reservation.bedrooms_appartment') }}
                                            </h4>
                                        </div>
                                        <div class="action reserv-user-detail">
                                            <div class="mini-profile justify-content-start p-0">
                                                <div class="pr-img rs-img">
                                                    <img class="profile-user"
                                                        src="{{ $booking->properties->users->profile_src }}">
                                                </div>
                                                <div class="pr-mini-detail">
                                                    <p>{{ $booking->properties->users->full_name }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="rev-descrip mt-3">
                                            <p class="mb-0 p-2 bg-theme-color ">
                                                {{ $booking->properties->property_address->address_line_1 ?? 'null' }},
                                                {{ $booking->properties->property_address->city ?? 'null' }},
                                                {{ $booking->properties->property_address->state ?? 'null' }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="rb-fillter-main">
                            <div class="rb-fillter-inner mb-4">
                                <h5 class="inner-main-title mb-0">{{ customTrans('reservation.q_cleannes') }}</h5>
                                <div class="rb-fi-rating">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="request" name="cln-lvl"
                                            id="cln-1">
                                        <label class="form-check-label" for="cln-1">
                                            <p class="mb-0">{{ customTrans('reservation.cleanness_option1') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="cln-lvl"
                                            id="cln-2">
                                        <label class="form-check-label" for="cln-2">
                                            <p class="mb-0">{{ customTrans('reservation.cleanness_option2') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="cln-lvl"
                                            id="cln-3">
                                        <label class="form-check-label" for="cln-3">
                                            <p class="mb-0">{{ customTrans('reservation.cleanness_option3') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="cln-lvl"
                                            id="cln-4">
                                        <label class="form-check-label" for="cln-4">
                                            <p class="mb-0">{{ customTrans('reservation.cleanness_option4') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="cln-lvl"
                                            id="cln-5">
                                        <label class="form-check-label" for="cln-5">
                                            <p class="mb-0">{{ customTrans('reservation.cleanness_option5') }}</p>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="rb-fillter-inner mb-4">
                                <h5 class="inner-main-title mb-0">{{ customTrans('reservation.q_communication') }}</h5>
                                <div class="rb-fi-rating">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="request" name="com-lvl"
                                            id="com-1">
                                        <label class="form-check-label" for="com-1">
                                            <p class="mb-0">{{ customTrans('host_dashboard.communication_option1') }}
                                            </p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="com-lvl"
                                            id="com-2">
                                        <label class="form-check-label" for="com-2">
                                            <p class="mb-0">{{ customTrans('host_dashboard.communication_option2') }}
                                            </p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="com-lvl"
                                            id="com-3">
                                        <label class="form-check-label" for="com-3">
                                            <p class="mb-0">{{ customTrans('reservation.communication_option3') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="com-lvl"
                                            id="com-4">
                                        <label class="form-check-label" for="com-4">
                                            <p class="mb-0">{{ customTrans('reservation.communication_option4') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="com-lvl"
                                            id="com-5">
                                        <label class="form-check-label" for="com-5">
                                            <p class="mb-0">{{ customTrans('reservation.communication_option5') }}</p>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="rb-fillter-inner mb-4">
                                <h5 class="inner-main-title mb-0">{{ customTrans('reservation.q_states') }}</h5>
                                <div class="rb-fi-rating">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="request" name="st-lvl"
                                            id="st-1">
                                        <label class="form-check-label" for="st-1">
                                            <p class="mb-0">{{ customTrans('reservation.states_option1') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="st-lvl"
                                            id="st-2">
                                        <label class="form-check-label" for="st-2">
                                            <p class="mb-0">{{ customTrans('reservation.states_option2') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="st-lvl"
                                            id="st-3">
                                        <label class="form-check-label" for="st-3">
                                            <p class="mb-0">{{ customTrans('reservation.states_option3') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="st-lvl"
                                            id="st-4">
                                        <label class="form-check-label" for="st-4">
                                            <p class="mb-0">{{ customTrans('reservation.states_option4') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant" name="st-lvl"
                                            id="st-5">
                                        <label class="form-check-label" for="st-5">
                                            <p class="mb-0">{{ customTrans('reservation.states_option5') }}</p>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="rb-fillter-inner mb-4">
                                <h5 class="inner-main-title mb-0">{{ customTrans('reservation.location_convenient') }}</h5>
                                <div class="rb-fi-ratingl">
                                    <div class="form-check">
                                        <input id="lo-1" class="form-check-input" type="radio" name="lo-lvl" />
                                        <label for="lo-1" class="form-check-label">{{ customTrans('reservation.location_option_one') }}</label>
                                    </div>
                                    <div class="form-check">
                                        <input id="lo-2" class="form-check-input" type="radio" name="lo-lvl" />
                                        <label for="lo-2" class="form-check-label">{{ customTrans('reservation.location_option_two') }}</label>
                                    </div>
                                    <div class="form-check">
                                        <input id="lo-3" class="form-check-input" type="radio" name="lo-lvl" />
                                        <label for="lo-3" class="form-check-label">{{ customTrans('reservation.location_option_three') }}</label>
                                    </div>
                                    <div class="form-check">
                                        <input id="lo-4" class="form-check-input" type="radio" name="lo-lvl" />
                                        <label for="lo-4" class="form-check-label">{{ customTrans('reservation.location_option_four') }}</label>
                                    </div>
                                    <div class="form-check">
                                        <input id="lo-5" class="form-check-input" type="radio" name="lo-lvl" />
                                        <label for="lo-5" class="form-check-label">{{ customTrans('reservation.location_option_five') }}</label>
                                    </div>
                                </div>
                            </div>
                            {{-- <div class="rb-fillter-inner mb-4">
                                <h5 class="inner-main-title mb-0">Question 1: How was the cleanliness of the property?</h5>
                                <div class="swiper ratingSlide mb-3 reserv-lvl">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="cln-1" class="rb-radio-button" type="radio"
                                                    name="cln-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="cln-1" class="rb-radio-tile-label">1</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="cln-2" class="rb-radio-button" type="radio"
                                                    name="cln-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="cln-2" class="rb-radio-tile-label">2</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="cln-3" class="rb-radio-button" type="radio"
                                                    name="cln-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="cln-3" class="rb-radio-tile-label">3</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="cln-4" class="rb-radio-button" type="radio"
                                                    name="cln-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="cln-4" class="rb-radio-tile-label">4</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="cln-5" class="rb-radio-button" type="radio"
                                                    name="cln-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="cln-5" class="rb-radio-tile-label">5</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between reserv-lvl-content">
                                    <p class="mb-0">Not clean</p>
                                    <p class="mb-0">Very clean</p>
                                </div>
                            </div>
                            <div class="rb-fillter-inner">
                                <h5 class="inner-main-title mb-0">How was property states?</h5>
                                <div class="swiper ratingSlide mb-3 reserv-lvl">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="st-1" class="rb-radio-button" type="radio"
                                                    name="st-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="st-1" class="rb-radio-tile-label">1</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="st-2" class="rb-radio-button" type="radio"
                                                    name="st-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="st-2" class="rb-radio-tile-label">2</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="st-3" class="rb-radio-button" type="radio"
                                                    name="st-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="st-3" class="rb-radio-tile-label">3</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="st-4" class="rb-radio-button" type="radio"
                                                    name="st-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="st-4" class="rb-radio-tile-label">4</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="st-5" class="rb-radio-button" type="radio"
                                                    name="st-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="st-5" class="rb-radio-tile-label">5</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between reserv-lvl-content">
                                    <p class="mb-0">Old</p>
                                    <p class="mb-0">New/refurbished</p>
                                </div>
                            </div>
                            <div class="rb-fillter-inner">
                                <h5 class="inner-main-title mb-0">How was property location?</h5>
                                <div class="swiper ratingSlide mb-3 reserv-lvl">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="lo-1" class="rb-radio-button" type="radio"
                                                    name="lo-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="lo-1" class="rb-radio-tile-label">1</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="lo-2" class="rb-radio-button" type="radio"
                                                    name="lo-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="lo-2" class="rb-radio-tile-label">2</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="lo-3" class="rb-radio-button" type="radio"
                                                    name="lo-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="lo-3" class="rb-radio-tile-label">3</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="lo-4" class="rb-radio-button" type="radio"
                                                    name="lo-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="lo-4" class="rb-radio-tile-label">4</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="lo-5" class="rb-radio-button" type="radio"
                                                    name="lo-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="lo-5" class="rb-radio-tile-label">5</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between reserv-lvl-content">
                                    <p class="mb-0">Difficult to Find</p>
                                    <p class="mb-0">Easy to Find</p>
                                </div>
                            </div>
                            <div class="rb-fillter-inner">
                                <h5 class="inner-main-title mb-0">How was host communication?</h5>
                                <div class="swiper ratingSlide mb-3 reserv-lvl">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="com-1" class="rb-radio-button" type="radio"
                                                    name="com-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="com-1" class="rb-radio-tile-label">1</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="com-2" class="rb-radio-button" type="radio"
                                                    name="com-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="com-2" class="rb-radio-tile-label">2</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="com-3" class="rb-radio-button" type="radio"
                                                    name="com-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="com-3" class="rb-radio-tile-label">3</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="com-4" class="rb-radio-button" type="radio"
                                                    name="com-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="com-4" class="rb-radio-tile-label">4</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="com-5" class="rb-radio-button" type="radio"
                                                    name="com-lvl" />
                                                <div class="rb-radio-tile">
                                                    <label for="com-5" class="rb-radio-tile-label">5</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between reserv-lvl-content">
                                    <p class="mb-0">Rude to Talk</p>
                                    <p class="mb-0">Friendly Communication</p>
                                </div>
                            </div> --}}
                        </div>
                    </div>
                    <div class="modal-footer cm-bd-footer">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                            data-bs-target="#reser-ratingTwo" data-id="">Next</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade modal-dr-bottom" id="reser-ratingTwo" data-bs-backdrop="static" data-bs-keyboard="false"
            tabindex="-1" aria-labelledby="reser-ratingTwo" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable bt-grey-scroller">
                <div class="modal-content cm-bd-content">
                    <div class="modal-header cm-bd-header">
                        <button type="button" class="transparent-btn md-back-btn" id="" data-bs-toggle="modal"
                            data-bs-target="#rating">
                            <img src="{{ asset('icons/black-larrow.svg') }}" alt="" class="rtl-leftArrowRotat">
                        </button>
                        <h6 class="w-100 text-center mb-0">Chalet for recreation and relaxation</h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body cm-bd-body">
                        <div class="reserv-reviews mb-3">
                            <div class="reviews">
                                <div class="row">
                                    <div class="col-md-4 col-12">
                                        <div class="ls-img reserv-prop-img">
                                            <img src="{{ asset($booking->properties->cover_photo) }}">
                                        </div>
                                    </div>
                                    <div class="col-md-8 col-12">
                                        <div class="ls-content">
                                            <span>{{ customTrans('reservation.booking_id') . ': ' . strtoUpper($booking->code) }}</span>
                                            <div>
                                                {{ customTrans('reservation.status') . ': ' . strtoUpper($booking->status) }}
                                            </div>

                                            <span><strong>{{ customTrans('account_transaction.amount') }}
                                                    :</strong>
                                                {{ $booking->total_with_discount ? $booking->total_with_discount : $booking->total }}</span>
                                            <div class="date-mark m-0">
                                                <span class="st-date">{{ $booking->startdate_dmy }}</span>
                                                <span class="date-ar"><i class="fas fa-angle-right"></i></span>
                                                <span class="end-date">{{ $booking->enddate_dmy }}</span>
                                            </div>
                                        </div>
                                        <div class="rs-title justify-content-start">
                                            <h4 class="reserv-product-detail mb-0 fs-16">
                                                {{ $booking->properties->bedrooms . ' ' . trans('messages.reservation.bedrooms_appartment') }}
                                            </h4>
                                        </div>
                                        <div class="action reserv-user-detail">
                                            <div class="mini-profile justify-content-start p-0">
                                                <div class="pr-img rs-img">
                                                    <img class="profile-user"
                                                        src="{{ $booking->properties->users->profile_src }}">
                                                </div>
                                                <div class="pr-mini-detail">
                                                    <p>{{ $booking->properties->users->full_name }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="rev-descrip mt-3">
                                            <p class="mb-0 p-2 bg-theme-color ">
                                                {{ $booking->properties->property_address->address_line_1 ?? 'null' }},
                                                {{ $booking->properties->property_address->city ?? 'null' }},
                                                {{ $booking->properties->property_address->state ?? 'null' }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h5 class="text-black mb-3 fw-600">{{ customTrans('reservation.q_what_likes') }}</h5>
                        <div class="rating-starts">
                            <div class="rating-area">
                                <div class="review">
                                    <textarea class="form-control" id="message" name="message" placeholder="Type Here ..." required></textarea>
                                    <div class="align-items-center fade  message d-none">
                                        <p class="error-tag alert alert-danger">
                                            <i class="fas fa-exclamation-circle"></i><span class="fs-para"><strong
                                                    class="mx-1">Error</strong>{{ $errors->first('message') }}</span>
                                        </p>
                                    </div>
                                </div>
                                <div class="review resrv-chosefile">
                                    <input type="file" class="form-control" id="file" name="file"
                                        accept="image/png, image/jpg, image/jpeg" multiple>
                                    <div id="images-box" class="d-none"></div>
                                    <div class="align-items-center fade file d-none">
                                        <p class="error-tag alert alert-danger">
                                            <i class="fas fa-exclamation-circle"></i><span class="fs-para"><strong
                                                    class="mx-1">Error</strong>{{ $errors->first('file') }}</span>
                                        </p>
                                    </div>
                                    <p class="error-tag alert alert-danger d-none" id="picErrorMessage">The
                                        file
                                        must be a file of type: png, JPG, jpeg, gif, jpg.</p>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer cm-bd-footer">
                        <button type="button" class="btn btn-primary" onclick="checkRatingModal2()">
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade modal-dr-bottom" id="reser-ratingThree" data-bs-backdrop="static"
            data-bs-keyboard="false" tabindex="-1" aria-labelledby="reser-ratingThree" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable bt-grey-scroller">
                <div class="modal-content cm-bd-content">
                    <div class="modal-header cm-bd-header">
                        <button type="button" class="transparent-btn md-back-btn" id="" data-bs-toggle="modal"
                            data-bs-target="#reser-ratingTwo">
                            <img src="{{ asset('icons/black-larrow.svg') }}" alt="" class="rtl-leftArrowRotat">
                        </button>
                        <h6 class="w-100 text-center mb-0">Chalet for recreation and relaxation</h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body cm-bd-body">
                        <div class="reserv-reviews mb-3">
                            <div class="reviews">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="rev-descrip">
                                            <p class="mb-0 p-2 bg-theme-color ">
                                                {{ $booking->properties->property_address->address_line_1 ?? 'null' }},
                                                {{ $booking->properties->property_address->city ?? 'null' }},
                                                {{ $booking->properties->property_address->state ?? 'null' }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="rb-fillter-main">
                            <div class="rb-fillter-inner mb-4">
                                <h5 class="inner-main-title mb-0">{{ customTrans('reservation.q_service') }}</h5>
                                <div class="rb-fi-rating">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="request"
                                            name="darent-service" id="ds-1">
                                        <label class="form-check-label" for="ds-1">
                                            <p class="mb-0">{{ customTrans('reservation.service_option1') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant"
                                            name="darent-service" id="ds-2">
                                        <label class="form-check-label" for="ds-2">
                                            <p class="mb-0">{{ customTrans('reservation.service_option2') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant"
                                            name="darent-service" id="ds-3">
                                        <label class="form-check-label" for="ds-3">
                                            <p class="mb-0">{{ customTrans('reservation.service_option3') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant"
                                            name="darent-service" id="ds-4">
                                        <label class="form-check-label" for="ds-4">
                                            <p class="mb-0">{{ customTrans('reservation.service_option4') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant"
                                            name="darent-service" id="ds-5">
                                        <label class="form-check-label" for="ds-5">
                                            <p class="mb-0">{{ customTrans('reservation.service_option5') }}</p>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="rb-fillter-inner mb-4">
                                <h5 class="inner-main-title mb-0">{{ customTrans('reservation.q_recomend') }}</h5>
                                <div class="rb-fi-rating">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="request"
                                            name="darent-recom" id="dr-1">
                                        <label class="form-check-label" for="dr-1">
                                            <p class="mb-0">{{ customTrans('reservation.recomend_option1') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant"
                                            name="darent-recom" id="dr-2">
                                        <label class="form-check-label" for="dr-2">
                                            <p class="mb-0">{{ customTrans('reservation.recomend_option2') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant"
                                            name="darent-recom" id="dr-3">
                                        <label class="form-check-label" for="dr-3">
                                            <p class="mb-0">{{ customTrans('reservation.recomend_option3') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant"
                                            name="darent-recom" id="dr-4">
                                        <label class="form-check-label" for="dr-4">
                                            <p class="mb-0">{{ customTrans('reservation.recomend_option4') }}</p>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" value="instant"
                                            name="darent-recom" id="dr-5">
                                        <label class="form-check-label" for="dr-5">
                                            <p class="mb-0">{{ customTrans('reservation.recomend_option5') }}</p>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            {{-- <div class="rb-fillter-inner mb-4">
                                <h5 class="inner-main-title mb-0">Darent Service</h5>
                                <div class="swiper ratingSlide mb-3 reserv-lvl">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="ds-1" class="rb-radio-button" type="radio"
                                                    name="darent-service" />
                                                <div class="rb-radio-tile">
                                                    <label for="ds-1" class="rb-radio-tile-label">1</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="ds-2" class="rb-radio-button" type="radio"
                                                    name="darent-service" />
                                                <div class="rb-radio-tile">
                                                    <label for="ds-2" class="rb-radio-tile-label">2</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="ds-3" class="rb-radio-button" type="radio"
                                                    name="darent-service" />
                                                <div class="rb-radio-tile">
                                                    <label for="ds-3" class="rb-radio-tile-label">3</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="ds-4" class="rb-radio-button" type="radio"
                                                    name="darent-service" />
                                                <div class="rb-radio-tile">
                                                    <label for="ds-4" class="rb-radio-tile-label">4</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="ds-5" class="rb-radio-button" type="radio"
                                                    name="darent-service" />
                                                <div class="rb-radio-tile">
                                                    <label for="ds-5" class="rb-radio-tile-label">5</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between reserv-lvl-content">
                                    <p class="mb-0">Bad</p>
                                    <p class="mb-0">Great</p>
                                </div>
                            </div>
                            <div class="rb-fillter-inner">
                                <h5 class="inner-main-title mb-0">Do you recomended your friends to Darent</h5>
                                <div class="swiper ratingSlide mb-3 reserv-lvl">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="dr-1" class="rb-radio-button" type="radio"
                                                    name="darent-recom" />
                                                <div class="rb-radio-tile">
                                                    <label for="dr-1" class="rb-radio-tile-label">1</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="dr-2" class="rb-radio-button" type="radio"
                                                    name="darent-recom" />
                                                <div class="rb-radio-tile">
                                                    <label for="dr-2" class="rb-radio-tile-label">2</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="dr-3" class="rb-radio-button" type="radio"
                                                    name="darent-recom" />
                                                <div class="rb-radio-tile">
                                                    <label for="dr-3" class="rb-radio-tile-label">3</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="dr-4" class="rb-radio-button" type="radio"
                                                    name="darent-recom" />
                                                <div class="rb-radio-tile">
                                                    <label for="dr-4" class="rb-radio-tile-label">4</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="rb-input-container reserv-input-lvl">
                                                <input id="dr-5" class="rb-radio-button" type="radio"
                                                    name="darent-recom" />
                                                <div class="rb-radio-tile">
                                                    <label for="dr-5" class="rb-radio-tile-label">5</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between reserv-lvl-content">
                                    <p class="mb-0">Never</p>
                                    <p class="mb-0">Sure</p>
                                </div>
                            </div> --}}
                        </div>
                        <h5 class="text-black mb-3 mt-4 fw-600">{{ customTrans('reservation.q_improment') }}</h5>
                        <div class="rating-starts">
                            <div class="rating-area">
                                <textarea class="form-control" id="darent_message" name="message" placeholder="Write Here ..."></textarea>
                                <div class="align-items-center fade  message d-none">
                                    <p class="error-tag alert alert-danger">
                                        <i class="fas fa-exclamation-circle"></i><span class="fs-para"><strong
                                                class="mx-1">Error</strong>{{ $errors->first('message') }}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer cm-bd-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewByType(event)">
                            Finish
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Rating Modal End -->
        <div class="modal fade" id="review-submit-success" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
            aria-labelledby="review-submit-success-Label" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog custom-small-modal-width modal-dialog-centered">
                <div class="modal-content custom-modal-content">
                    <div class="modal-body">
                        <div class="alert-modal">
                            <img src="{{ asset('icons/check-success.svg') }}" alt="" class="mb-3">
                            <h5 class="text-success mb-2 fw-600 ">
                                Thank you for your feedback! Your review will be public soon.
                            </h5>
                            <button class="theme-btn btn-success w-100">
                                {{ customTrans('listing.publish_ok') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- cancel confirm modal  Modal -->
    <div class="modal fade delete_modal modal-dr-bottom" id="cancelConfirmationModal" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="cancelLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content custom-modal-content">
                <div class="modal-header  custom-small-modal-header">

                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert-modal">
                        <div class="sure"><i class="ri-information-line"></i></div>
                        <h3 class="text-danger mb-4 fw-600 ">
                            {{ customTrans('general.warning') }}</h3>
                        <p class="mb-4">
                            {{ customTrans('general.before_cancel_see_cancel_policy') }}
                        </p>
                        <p class="mb-4">
                            {{ customTrans('general.host_cancel_policy_determine') }}
                        </p>
                        <p class="mb-4">
                            {{ customTrans('general.view_host_cancel_policy') }}
                        </p>
                        <div class="delete-pr">
                            {{-- <a href="#">
        <button class="theme-btn btn-success w-100 no-del"
            data-bs-dismiss="modal"
            aria-label="Close">No</button>
    </a> --}}
                            <a href="javascript:;">
                                <button class="theme-btn btn-danger w-100 del">
                                    <a href="{{ route('cancelbyguest', $booking->id) }}"
                                        class="anchor-prnt-btn">{{ customTrans('reservation.cancel_reservation') }}</a>
                                </button>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- cancel confirm modal  Modal -->

    {{-- Add Card Modal --}}
    <div class="modal fade dubai-ff modal-dr-bottom" id="add-card" data-bs-backdrop="add-cardlabel"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="calendarLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-simple-content">
                <div class="modal-header cm-simple-header justify-content-center">
                    <button type="button" class="cancel-addcard transparent-btn" data-bs-dismiss="modal"
                        aria-label="Close">
                        <img src="{{ asset('icons/black-larrow.svg') }}" alt="">
                    </button>
                    <h3 class="mb-0 card-modal-heading">{{ customTrans('wallet.add_card') }}</h3>
                </div>
                <div class="modal-body cm-simple-body">
                    <div id="custom_error" class="text-danger error text-center mb-2"></div>
                    <div class="add-card-main">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for="" class="">{{ customTrans('wallet.holder_name') }}</label>
                                    <input type="text" name="card_name" id="name" placeholder=""
                                        class="form-control only-character-valid">
                                </div>
                                <div id="name_error" class="text-danger error text-left"></div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for=""
                                        class="">{{ customTrans('payment.card_number') }}</label>
                                    {{-- <input type="text"  maxlength="14" name="card_number" id="card" placeholder="0000 0000 0000 0000" class="form-control" onKeyDown="if(this.value.length==16 && event.keyCode!=8) return false;"> --}}
                                    <input type="text" maxlength="16" name="card_number" id="card"
                                        placeholder="----  ---- ---- ----" class="form-control text-center"
                                        oninput="this.value=this.value.replace(/[^0-9]/g,'');" pattern="[0-9]{16}"
                                        required>
                                </div>
                                <div id="number_error" class="text-danger error text-left"></div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for=""
                                        class="">{{ customTrans('payment.select_card') }}</label>
                                    <select class="form-control" name="paymentMethodId" id="paymentMethodId">
                                        <option value="2">Visa/Master</option>
                                        <option value="12">STC</option>
                                        <option value="101">Mada</option>
                                    </select>
                                </div>
                                <div id="" class="text-danger error text-center"></div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="row en-position">
                                    <div class="col-6">
                                        <div class="input-withlable">
                                            <label for="" class="">{{ customTrans('payment.mm') }}</label>
                                            {{-- <input type="text"  maxlength="2" name="expiry_month" pattern="" id="expiry_month" placeholder="MM" class="form-control"> --}}
                                            <select class="form-control" name="expiry_month" pattern=""
                                                id="expiry_month">
                                                <option value="01">01</option>
                                                <option value="02">02</option>
                                                <option value="03">03</option>
                                                <option value="04">04</option>
                                                <option value="05">05</option>
                                                <option value="06">06</option>
                                                <option value="07">07</option>
                                                <option value="08">08</option>
                                                <option value="09">09</option>
                                                <option value="10">10</option>
                                                <option value="11">11</option>
                                                <option value="12">12</option>
                                            </select>
                                        </div>
                                        <div id="month_error" class="text-danger error text-center"></div>
                                    </div>
                                    <div class="col-6">
                                        <div class="input-withlable">
                                            <label for="" class="">{{ customTrans('payment.yy') }}</label>
                                            {{-- <input type="text"  maxlength="2" name="expiry_year" pattern="" id="expiry_year" placeholder="YY" class="form-control"> --}}
                                            <select class="form-control year" name="expiry_year" pattern=""
                                                id="expiry_year">


                                            </select>
                                        </div>
                                        <div id="year_error" class="text-danger error text-center"></div>
                                    </div>
                                </div>
                                <input type="hidden" name="bid" id="bid">
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for="" class="">CVV</label>
                                    <input type="number" name="cvv" id="cvv" maxlength="4"
                                        placeholder="----" class="form-control numberInput text-center"
                                        onKeyDown="if(this.value.length==4 && event.keyCode!=8) return false;">
                                </div>
                                <div id="cvv_error" class="text-danger error text-left"></div>
                            </div>
                            <div class="col-md-12 mt-3 text-center">

                                <p class="text-start mb-3">
                                    {{ customTrans('payment.your_card_info_secure_no_one_reach') }}</p>
                                <div class="loadergif btn-ld d-none">
                                    <img src="{{ asset('icons/loader.gif') }}" class="">
                                </div>
                                <a href="#" class="add-card-btn">
                                    <button class="card-svae-btn theme-btn w-100">Save</button>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Coupon Code Modal --}}
    <div class="modal fade modal-dr-bottom" id="coupon_code" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="cancelLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content custom-modal-content">
                <div class="modal-header  custom-small-modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"> </button>
                </div>
                <div class="modal-body">
                    <div class="alert-modal">

                        <h3 class="text-black mb-4 fw-600 m-heading">Summary</h3>
                        <table class="table table-bordered discountTable">
                            <tr>
                                <th class="text-left">
                                    {{ $booking->total_night }}
                                    {{ customTrans('reservation.per_night_sar', [':booking_base_price' => $booking->base_price]) }}
                                </th>
                                <td class="text-right">
                                    {{ $booking->base_price }}
                                    {{ customTrans('utility.sar') }}
                                </td>
                            </tr>




                            @if ($booking->cleaning_charge != 0)
                                <tr>
                                    <th class="text-left">
                                        {{ customTrans('property_single.cleaning_fee') }}
                                    </th>
                                    <td class="text-right">
                                        {{ $booking->cleaning_charge }}
                                        {{ customTrans('utility.sar') }}
                                    </td>
                                </tr>
                            @endif



                            <tr>
                                <th class="text-left">
                                    {{ customTrans('property_single.service_fee') }}
                                </th>
                                <td class="text-right">
                                    {{ $booking->service_charge }}
                                    {{ customTrans('utility.sar') }}
                                </td>
                            </tr>

                            @if ($booking->properties->platform_id == 4)
                                <tr>
                                    <th class="text-left">
                                        {{ customTrans('property_single.iva_tax') }}
                                    </th>
                                    <td class="text-right">
                                        {{ $booking->iva_tax }}
                                        {{ customTrans('utility.sar') }}
                                    </td>
                                </tr>
                            @endif

                            @if ($wallet_deduction)
                                <tr>
                                    <th class="text-left">
                                        {{ customTrans('property_single.wallet_deduction') }}
                                    </th>
                                    <td class="text-right">
                                        {{ $wallet_deduction }}
                                        {{ customTrans('utility.sar') }}
                                    </td>
                                </tr>
                            @endif

                            <tr>
                                <th class="text-left">Discount: </th>
                                <td id="disvalue" class="text-danger text-right">-{{ $booking->total_discount }}</td>
                            </tr>

                            <tr>
                                <th class="text-left">Wallet: </th>
                                <td id="payByWalletAmount" class="text-danger text-right">-0</td>
                            </tr>

                            <tr>
                                <th class="text-left">
                                    {{ customTrans('property_single.total_include_vat') }}
                                </th>
                                <td class="text-right" id="afteramount"
                                    data-total="{{ !$wallet_deduction ? ($booking->total_with_discount != 0 ? $booking->total_with_discount : $booking->total) : $booking->total }}">
                                    @if (!$wallet_deduction)
                                        {{ $booking->total_with_discount != 0 ? $booking->total_with_discount : $booking->total }}
                                    @else
                                        {{ $booking->total }}
                                    @endif
                                    {{ customTrans('utility.sar') }}
                                </td>
                            </tr>
                        </table>

                        {{-- <h3 class="text-black mb-4 fw-600 m-heading">Coupon Code</h3>
                        <p class="mb-4 c-message ">Please enter your coupon code.</p> --}}
                        <div class="coupon-checkbox">
                            <div class="form-check cust-check coupon-check">
                                <label class="form-check-label" for="coupon_check">
                                    Have Coupon Code ?
                                </label>
                                <input class="form-check-input cust-form-check-input" type="checkbox" name="coupon-check"
                                    id="coupon_check">
                            </div>


                            <div class="coupon-inp">
                                <input type="hidden" name="booking_type_1" id="booking_type_1" value="0">
                                {{-- <input type="hidden" name="paymentMethodId" id="paymentMethodId" value="2"> --}}
                                {{-- COUPON --}}
                                <div class="gr-code ">
                                    <input type="text" name="coupon" id="coupon" class="form-control"
                                        placeholder="{{ customTrans('property_single.coupon_code') }}" required>

                                    <button class="pd-promo-btn apply_coupon">
                                        {{ customTrans('filter.apply') }}
                                    </button>
                                    <button class="pd-promo-btn remove_coupon d-none">
                                        Remove
                                    </button>
                                </div>
                                <div class="text-danger mt-3" id="coupon-res-message"></div>
                                {{-- COUPON END --}}


                            </div>
                        </div>
                        <div class="rmv-coupon d-none">
                            <form onsubmit="removeCoupon(event)">
                                <img src="{{ asset('icons/info-alert.svg') }}" alt="" class="rm-coupon">
                                @csrf
                                <button type="submit"
                                    class="theme-btn-light w-100 mb-3">{{ customTrans('property_single.remove_coupon') }}</button>
                            </form>
                        </div>

                        <div class="reserv-pay-sec pay-sec">
                            <div class="prop-payment-option">
                                <label class="pay-sec-label" for="ch-card-pay">
                                    <input type="radio" name="pay" id="ch-card-pay" class="cardPay" checked />
                                    <div class="pay-sl-content">
                                        <img src="{{ asset('images/visa-master-pay.png') }}" alt="">
                                        {{-- <h6 class="mb-0"> {{ customTrans('general.pay_with_fatoorah') }}</h6> --}}
                                    </div>
                                </label>
                                <label class="pay-sec-label" for="ch-stc-pay">
                                    <input type="radio" name="pay" id="ch-stc-pay" class="sctPay" />
                                    <div class="pay-sl-content">
                                        <img src="{{ asset('images/stc-pay.png') }}" alt="">
                                    </div>
                                </label>
                                <label class="pay-sec-label" for="ch-mada-pay">
                                    <input type="radio" name="pay" id="ch-mada-pay" class="madaPay" />
                                    <div class="pay-sl-content">
                                        <img src="{{ asset('images/mada-pay.png') }}" alt="">
                                        {{-- <h6 class="mb-0"> {{ customTrans('general.pay_with_fatoorah') }}</h6> --}}
                                    </div>
                                </label>
                                @if ($showPayOpts)
                                    <label class="pay-sec-label mt-1" for="ch-apple-pay">
                                        <input type="radio" id="ch-apple-pay" name="pay" class="applePay" />
                                        <div class="pay-sl-content">
                                            <img src="{{ asset('images/apple_pay.png') }}" alt="">
                                            {{-- <h6 class="mb-0"> {{ customTrans('general.pay_with_apple') }}</h6> --}}
                                        </div>
                                    </label>
                                @endif
                                <label class="pay-sec-label mt-1" for="ch-tabby-pay">
                                    <input type="radio" id="ch-tabby-pay" name="pay" class="tabbyPay" />
                                    <div class="pay-sl-content">
                                        <img src="{{ asset('icons/tabby-logo-en.svg') }}" alt="">
                                        {{-- <h6 class="mb-0">
                                                        {{ customTrans('general.pay_with_tabby') }}</h6> --}}
                                    </div>
                                </label>
                            </div>

                            <div class="pay-sec-label ch-wallet-pay">
                                <input type="checkbox" id="wallet-pay-check" onchange="handleWalletToggle()"
                                    name="pay_by_wallet" @if ($user_wallet->balance <= 0) disabled @endif
                                    data-amount="{{ $user_wallet->balance }}">
                                <label for="wallet-pay-check"></label>
                                <div class="pay-sl-content d-flex align-items-end">
                                    <img src="{{ asset('images/wallet.png') }}" alt="">
                                    <h6 class="mb-0"> {{ customTrans('general.wallet_pay') }}
                                        <span>({{ $user_wallet->balance }})</span>
                                    </h6>
                                </div>
                            </div>
                        </div>

                        <a href="#">
                            <button class="theme-btn w-100 mkpayment">{{ customTrans('property.continue') }}</button>
                            <img src="{{ asset('icons/loader.gif') }}" class="loadergif d-none" height="65">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Property is already reserved  Modal -->
    <div class="modal fade" id="property_booked" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="cancelLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog custom-small-modal-width modal-dialog-centered">
            <div class="modal-content custom-modal-content">
                <div class="modal-header  custom-small-modal-header">

                    <button class="btn-close" class="btn-close iqama-close-btn" data-bs-dismiss="modal"
                        aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert-modal">
                        <img src="{{ asset('logo/logo.svg') }}" alt="" class="mb-3">
                        <h3 class="text-yellow mb-4 fw-600 ">{{ customTrans('payment.greeting_guest') }}</h3>
                        <p class="mb-4 tabby-error-message">{{ customTrans('payment.property_is_booked_msg') }}
                        </p>
                        <button class="theme-btn w-100"
                            data-bs-dismiss="modal">{{ customTrans('account.continue') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (session()->has('bookingcancelled'))
        {{-- booking Cancelled Successfully Modal --}}
        <div class="modal fade modal-dr-bottom" id="bookingCancelled" data-bs-backdrop="static" data-bs-keyboard="false"
            tabindex="-1" aria-labelledby="successLabel" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
                <div class="modal-content custom-modal-content">
                    <div class="modal-body">
                        <div class="alert-modal">
                            <img src="{{ asset('icons/check-success.svg') }}" alt="" class="mb-3">
                            <h3 class="text-success mb-4 fw-600 ">{{ customTrans('general.success') }}</h3>
                            <p class="mb-4">{{ customTrans('general.your_reservation_cancel_successfully') }}</p>
                            <p class="mb-4">{{ customTrans('general.would_you_like_to_reserve_more') }}</p>
                            <button class="theme-btn btn-success w-100 successmodalbtn" data-bs-dismiss="modal"
                                aria-label="Close">
                                <a href="{{ route('home') }}" class="anchor-prnt-btn">
                                    {{ customTrans('general.see_more_properties') }}
                                </a>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{-- booking Cancelled Successfully modal end --}}
    @endif
    {{-- Coupon Code Modal END --}}
    <input type="hidden" name="" id="userlogin" value="{{ auth()->check() ? auth()->user()->id : '' }}">
@stop
@push('scripts')
    <script src="{{ asset('js/front.js') }}?v={{ $currentAppVersion }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
    @if (request()->query('review') == 1)
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                $('#rating').modal('show');
            });
        </script>
    @endif
    <script>

        $(document).ready(function() {
            $('.cardPay').click();
        });
        let coupon = null
        let has_coupon = false
        let booking_id = null
        async function removeCoupon(e) {
            const smbBtnEl = e.submitter
            try {
                e.preventDefault()
                const bid = document.querySelector(".mkpayment").dataset.bookingid
                const formData = new FormData(e.target)
                if (!bid) {
                    return
                }
                if (document.querySelector(".loadergif").classList.contains("d-none")) {
                    document.querySelector(".loadergif").classList.remove("d-none");
                    document.querySelector(".mkpayment").classList.add("d-none");
                }
                const response = await fetch(`/v1/bookings/${bid}/remove/coupon`, {
                    headers: {
                        "accept": "application/json"
                    },
                    method: "POST",
                    body: formData
                })
                const data = await response.json()
                if (!response.ok) throw data;
                if (data.status) {
                    location.reload()
                }
            } catch (errorRes) {
                console.log(errorRes);
            } finally {
                smbBtnEl.disabled = false
            }
        }

        function handleCouponToggle() {
            if ($('#coupon_check').is(':checked')) {
                $(".coupon-inp").slideDown();
                $('.mkpayment').addClass('disabled-btn');
            } else {
                $(".coupon-inp").slideUp();
                $('.mkpayment').removeClass('disabled-btn');
            }
        }

        // Event listener for the checkbox change
        $('#coupon_check').on('change', function() {
            handleCouponToggle();
            $('#coupon-res-message').text('');
        });

        // Event listener for the modal close

        // review modal btn disabled
        function checkFirstThreeRadios() {
        const groups = ['cln-lvl', 'com-lvl', 'st-lvl', 'lo-lvl'];
        const allChecked = groups.every(group => {
            return document.querySelector(`input[name="${group}"]:checked`);
        });

        const nextBtn = document.querySelector('[data-bs-target="#reser-ratingTwo"]');
        nextBtn.disabled = !allChecked;
        }

        function checkLastTwoRadios() {
            const groups = ['darent-service', 'darent-recom'];
            const allChecked = groups.every(group => {
                return document.querySelector(`input[name="${group}"]:checked`);
            });

            const finishBtn = document.querySelector('button[onclick="reviewByType(event)"]');
            finishBtn.disabled = !allChecked;
        }

        // Attach event listeners to all radio buttons
        document.addEventListener('DOMContentLoaded', () => {
            const allRadioInputs = document.querySelectorAll('input[type="radio"]');
            allRadioInputs.forEach(radio => {
                radio.addEventListener('change', () => {
                    checkFirstThreeRadios();
                    checkLastTwoRadios();
                });
            });

            // Run initially to set disabled state on page load
            checkFirstThreeRadios();
            checkLastTwoRadios();
        });
        function checkRatingModal2() {
            const textarea = document.querySelector("#reser-ratingTwo .rating-area textarea")
            const fileEl = document.querySelector("#reser-ratingTwo .rating-area input")
            removeErrors()
            let error = false
            if (!textarea.value.trim()) {
                if (!textarea.classList.contains("is-invalid")) {
                    textarea.classList.add("is-invalid")
                    textarea.insertAdjacentHTML("afterend", "<div class='invalid-feedback'>Message is required.</div>")
                }
                error = true
            }
            if (Object.values(fileEl.files).filter(file => {
                    const fileNameArr = file.name.split(".");
                    return ['jpeg', 'jpg', 'png'].indexOf(fileNameArr[fileNameArr.length - 1].trim()
                        .toLowerCase()) < 0
                }).length > 0) {
                if (!fileEl.classList.contains("is-invalid")) {
                    fileEl.classList.add("is-invalid")
                    fileEl.insertAdjacentHTML("afterend", "<div class='invalid-feedback'>Invalid file type.</div>")
                }
                error = true
            } else if (fileEl.files.length > 5) {
                if (!fileEl.classList.contains("is-invalid")) {
                    fileEl.classList.add("is-invalid")
                    fileEl.insertAdjacentHTML("afterend", "<div class='invalid-feedback'>Max 5 files are allowed.</div>")
                }
                error = true
            }
            if (!error) {
                $(document).ready(function() {
                    $("#reser-ratingTwo").modal("hide")
                    $("#reser-ratingThree").modal("show")
                })
            }
        }

        function setReviewById(id, type) {
            booking_id = id
        }

        async function reviewByType(e) {
            try {
                e.target.disabled = true
                const formData = new FormData()
                formData.append("_token", "{{ csrf_token() }}")
                formData.append("cleanliness", document.querySelector("[id^='cln-']:checked").id.split("-").pop())
                formData.append("accuracy", document.querySelector("[id^='st-']:checked").id.split("-").pop())
                formData.append("location", document.querySelector("[id^='lo-']:checked").id.split("-").pop())
                formData.append("communication", document.querySelector("[id^='com-']:checked").id.split("-").pop())
                formData.append("darent_service", document.querySelector("[id^='ds-']:checked").id.split("-").pop())
                formData.append("darent_recomended", document.querySelector("[id^='dr-']:checked").id.split("-").pop())
                formData.append("message", document.getElementById("message").value.trim())
                formData.append("darent_message", document.getElementById("darent_message").value.trim())
                if (document.getElementById("file").files.length > 0) {
                    Object.values(document.getElementById("file").files).forEach((file, i) => {
                        formData.append(`images[${i}]`, file)
                    });
                }
                const response = await fetch(`/v1/guest/bookings/${booking_id}/review`, {
                    headers: {
                        'accept': 'application/json'
                    },
                    method: "POST",
                    body: formData
                });
                const data = await response.json();
                if (!response.ok) throw data;
                // if (data.status) {
                //     window.location.reload()
                // }
                if (data.status) {
                    $('#reser-ratingThree').modal('hide');
                    setTimeout(function () {
                        let modal = new bootstrap.Modal(document.getElementById('review-submit-success'));
                        modal.show();
                        document.querySelector('#review-submit-success button').addEventListener('click', function () {
                            window.location.reload();
                        });
                    }, 500);
                }
            } catch (errorRes) {
                console.log(errorRes);
                e.target.disabled = false
            }
        }

        $('#message_init').on('click', function(e) {
            e.preventDefault();
            var auth = '{{ auth()->check() }}';
            // alert(auth);
            if (auth) {
                var property_id = $(this).data('propertyid');
                var receiver_id = $(this).data('receiverid');
                var host_id = $(this).data('hostid');

                $.ajax({
                    url: "{{ route('sendmsg') }}",
                    type: "get",
                    data: {
                        property_id,
                        receiver_id,
                        host_id
                    },
                    cache: false,
                    success: function(response) {
                        if (response.message == "success") {
                            $('#messageForm').submit();

                        } else {
                            console.log("Failed");

                        }
                    }
                });
            } else {
                window.location.href = '{{ url('/login') }}';
            }
        });

        $(document).on('submit', '.rating-form', function(event) {
            event.preventDefault();

            var formData = new FormData(this);
            var url = $(this).attr('action');
            $('.RateBtn').prop('disabled', true);
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('.rating-form')[0].reset();
                    console.log(response);
                    $('.firstname').addClass('d-none');
                    $('.message').addClass('d-none');
                    $('.file').addClass('d-none');
                    window.location.href = "{{ route('guest_reservation') }}";
                    $('.RateBtn').prop('disabled', true);
                    // handle successful response
                },
                error: function(xhr) {
                    console.log(xhr);
                    let responseJSON = xhr.responseJSON;
                    if (responseJSON.errors.rating) {
                        $('.firstname').addClass('show');
                        $('.firstname').removeClass('d-none');
                        $('.firstname').text(responseJSON.errors.rating[0]).css('color', 'red');
                    } else {
                        $('.firstname').removeClass('show');
                        $('.firstname').addClass('d-none');
                    }
                    if (responseJSON.errors.message) {
                        $('.message').addClass('show');
                        $('.message').removeClass('d-none');
                        $('.message').text(responseJSON.errors.message[0]).css('color', 'red');
                    } else {
                        $('.message').addClass('d-none');
                        $('.message').removeClass('show');
                    }
                    if (responseJSON.errors.file) {
                        $('.file').addClass('show');
                        $('.file').removeClass('d-none');
                        $('.file').text(responseJSON.errors.file[0]).css('color', 'red');
                    } else {
                        $('.file').addClass('d-none');
                        $('.file').removeClass('show');
                    }
                    $('.RateBtn').prop('disabled', false);
                    // handle error response
                }
            });
        });

        var make_payment_url = "{{ route('makePayment') }}";

        $(document).on('click', '.mkpayment', function() {
            mkpayment($(this));
        });

        var guest_service_charge = "{{ $guest_service_charge }}";

        $(document).on('click', '.apply_coupon', function() {
            let allothers = 0;
            let userlogin = $('#userlogin').val();
            let amount = $(this).attr('data-amount');
            let propertyId = $(this).attr('data-propertyId');
            let platform_id = {{ $booking->properties->platform_id }}

            let cleaningCharges = parseFloat($(this).attr('data-cleaning_charge'));
            let service_on_cleaning = parseFloat($(this).attr('data-service_on_cleaning'));
            let securityFee = parseFloat($(this).attr('data-security_charges'));
            let securityFee_cleaning = parseFloat($(this).attr('data-service-service_on_securityFee'));
            let iva_tax = parseFloat($(this).attr('data-iva_tax')) || 0;
            let additional_guest = parseFloat($(this).attr('data-additional_guest')) || 0;
            let accomodation_tax = parseFloat($(this).attr('data-accomodation_tax'));

            if (platform_id == 4) {

                let cleaningservicefee = (cleaningCharges / 100) * 11;
                allothers = cleaningCharges + iva_tax + cleaningservicefee;
            } else {
                allothers = cleaningCharges + service_on_cleaning + securityFee + securityFee_cleaning + iva_tax +
                    additional_guest + accomodation_tax;

            }
            consolelog("ALL OTHERS - ", cleaningCharges, service_on_cleaning, securityFee, iva_tax,
                additional_guest, accomodation_tax);

            console.log(amount);

            var couponNumber = $('#coupon').val();
            // let couponcheck = $('#coupon_check').is(':checked');
            if (couponNumber) {
                $.ajax({
                    url: "{{ route('couponCheck') }}",
                    type: 'post',
                    data: {
                        "_token": "{{ csrf_token() }}",
                        'couponNumber': couponNumber,
                        'propertyid': propertyId,
                        'amount': amount,
                        'user': userlogin,
                        'checkin': '{{ $booking->start_date }}',
                        'checkout': '{{ $booking->end_date }}',

                    },
                    beforeSend: function() {
                        $(this).next(".loadergif").removeClass('d-none');
                        $(this).addClass('d-none');
                    },
                    success: function(res) {
                        console.log(res);
                        if (res.status == 'success' || res.status == 'campaign') {
                            $('#coupon-res-message').text('');
                            const newVal = (res.amount_after_discount + allothers).toFixed(2)
                            $('#total').text(newVal);
                            $('#coupon-res-message').removeClass('text-danger')
                            $('#coupon-res-message').addClass('text-success');

                            if (res.status == 'campaign') {
                                $('#coupon-res-message').text(
                                    "{{ customTrans('payment.campaign_coupon_applied') }}");
                            } else {
                                $('#coupon-res-message').text(
                                    "{{ customTrans('payment.coupon_applied') }}");
                            }

                            // Set value in table
                            $('#afteramount').text(`${newVal} SAR`);
                            $('#distype').text(res.discount_type);
                            $('#disvalue').text(res.discount_value);

                            $("#coupon").attr('disabled', true);
                            $(this).addClass("d-none");
                            $(".remove_coupon").removeClass("d-none");

                            $('.mkpayment').removeClass('disabled-btn');
                            has_coupon = true
                        } else {
                            $("#coupon").attr('disabled', false);
                            $('#coupon-res-message').addClass('text-danger');
                            $('#coupon-res-message').removeClass('text-success');
                            $('#coupon-res-message').text(res.message);
                            $('.mkpayment').addClass('disabled-btn');
                            has_coupon = false

                            $(this).removeClass("d-none");
                            $(".remove_coupon").addClass("d-none");
                        }

                        handleWalletToggle()
                    },
                    error: function(reject) {
                        has_coupon = false
                        if (reject.status === 422) {
                            var errors = $.parseJSON(reject.responseText);
                            $.each(errors, function(key, val) {
                                // $("#" + key + "_error").text(val[0]);
                                consolelog(key, val);
                            });
                        }
                        $(this).next(".loadergif").addClass('d-none');
                        $(this).removeClass('d-none');
                        $("#coupon").attr('disabled', false);
                    }
                });
            } else {
                $('#coupon-res-message').addClass('text-danger');
                $('#coupon-res-message').removeClass('text-success');
                $('#coupon-res-message').text("Please Enter Coupon Code");
            }
        });

        $(document).on('click', '.coupon_code_btn', function() {
            let bookingid = $(this).attr('data-bookingId');
            let amount = $(this).attr('data-amount');

            let cleaningFee = parseInt($(this).attr('data-cleaning_charge'));
            let service_on_cleaning = guest_service_charge / 100 * cleaningFee;
            let securityFee = parseInt($(this).attr('data-security_charges'));
            let service_on_securityFee = guest_service_charge / 100 * securityFee;
            let accomodation_tax = parseInt($(this).attr('data-accomodation_tax'));

            let has_discount = $(this).attr('data-has_discount');
            let discount_applied = $(this).attr('data-discount_applied');


            let additional_guest = parseInt($(this).attr('data-additional_guest')) || 0;
            let iva_tax = parseInt($(this).attr('data-iva_tax'));
            let propertyId = $(this).attr('data-propertyId');
            consolelog("ALL OTHERS 1st- ", service_on_cleaning, securityFee, iva_tax, additional_guest,
                accomodation_tax);

            $('.mkpayment').attr("data-bookingId", bookingid);
            $('.mkpayment').attr("data-amount", amount);
            $('.mkpayment').attr("data-propertyId", propertyId);

            $('.mkpayment').attr("data-cleaning_charge", cleaningFee);
            $('.mkpayment').attr("data-security_charges", securityFee);

            $('.mkpayment').attr("data-service_on_cleaning", service_on_cleaning);
            $('.mkpayment').attr("data-service-service_on_securityFee", service_on_securityFee);
            $('.mkpayment').attr("data-iva_tax", iva_tax);
            $('.mkpayment').attr("data-additional_guest", additional_guest);
            $('.mkpayment').attr("data-accomodation_tax", accomodation_tax);

            $('.apply_coupon').attr("data-bookingId", bookingid);
            $('.apply_coupon').attr("data-amount", amount);
            $('.apply_coupon').attr("data-propertyId", propertyId);

            $('.apply_coupon').attr("data-cleaning_charge", cleaningFee);
            $('.apply_coupon').attr("data-security_charges", securityFee);
            $('.apply_coupon').attr("data-service_on_cleaning", service_on_cleaning);
            $('.apply_coupon').attr("data-service-service_on_securityFee", service_on_securityFee);
            $('.apply_coupon').attr("data-iva_tax", iva_tax);
            $('.apply_coupon').attr("data-additional_guest", additional_guest);
            $('.apply_coupon').attr("data-accomodation_tax", accomodation_tax);


            if (has_discount == 'true' || discount_applied == 'true') {
                $('.coupon-checkbox').addClass('d-none');
                $('.m-heading').addClass('d-none');
                $('.c-message').text("{{ customTrans('payment.continue_to_make_payment') }}")
                $(".rmv-coupon").removeClass("d-none")
                $("#ch-tabby-pay").prop("disabled", true);
                $("#tabby-msg").show();

            } else {
                $('.coupon-checkbox').removeClass('d-none');
                $('.m-heading').removeClass('d-none');
                $('.c-message').text('Please enter your coupon code.')
                $(".rmv-coupon").addClass("d-none")
                $("#ch-tabby-pay").prop("disabled", false);
                $("#tabby-msg").hide();
            }

            $('#coupon_code').modal('show');
            //Set in modal button
        });

        $(document).on('click', '.property_is_booked', function() {
            $('#property_booked').modal('show');
        });

        function handleCoupon() {
            $('#coupon_code').modal('hide')
            if (has_coupon) {
                coupon = document.querySelector("#coupon").value;
                has_coupon = false
            } else {
                coupon = null
            }
            $('#coupon').val("")
        }

        $("#coupon_check").change(function(e) {
            e.preventDefault();
            if (!this.checked) $(".remove_coupon").trigger("click");
        });

        $(document).on('click', '.remove_coupon', function() {
            $(this).addClass("d-none");
            $('.apply_coupon').removeClass("d-none");
            $("#coupon").val("");
            $("#coupon").attr('disabled', false);
            $("#disvalue").text(`-0`);
            $("#coupon-res-message").text("Coupon removed successfully");
            handleWalletToggle();
        });

        function handleWalletToggle() {
            let total = $("#afteramount");
            let total_amount = total.data("total");

            let discount = $("#disvalue");
            let discount_amount = discount.text();

            let wallet = $("#wallet-pay-check");
            let wallet_amount = $("#payByWalletAmount");
            let wallet_balance = $("#wallet-pay-check").data("amount");

            total_amount = total_amount - discount_amount;
            if (wallet.prop('checked')) {
                if (total_amount - wallet_balance > 0) {
                    total.text(`${(total_amount - wallet_balance).toFixed(2)} {{ customTrans('utility.sar') }}`);
                    wallet_amount.text(`-${wallet_balance.toFixed(2)} {{ customTrans('utility.sar') }}`);
                } else {
                    total.text(`0 {{ customTrans('utility.sar') }}`);
                    wallet_amount.text(`-${total_amount.toFixed(2)} {{ customTrans('utility.sar') }}`);
                }
            } else {
                wallet_amount.text(`-0 {{ customTrans('utility.sar') }}`);
                total.text(`${total_amount.toFixed(2)} {{ customTrans('utility.sar') }}`);
            }
        }

        function mkpayment(_this) {
            let checkedRadioButton = $('input[name="pay"]:checked');
            var selectedPaymentId = checkedRadioButton.attr('id'); // Get the id attribute of the checked radio button
            if (selectedPaymentId == "ch-tabby-pay") {
                make_payment_url = "{{ route('tabbyPaymentInitiate') }}";
            }
            const checkVal = $('#booking_type_1').val()
            let userlogin = $('#userlogin').val();
            let amount = $(this).attr('data-amount');
            let propertyId = $(this).attr('data-propertyId');
            console.log(checkVal);



            let bookingid = _this.attr('data-bookingId');

            var this_element = _this;
            // if (!check_card && checkVal == 0) {
            //     // Modal text change
            //     $('.card-modal-heading').text('Enter card details');
            //     $('.card-svae-btn').text('Continue to pay');
            //     $('#bid').val(bookingid);
            //     //Modal show
            //     $('#add-card').modal('show');
            //     //get values and submit
            // } else {
            handleCoupon()
            $.ajax({
                type: "POST",
                url: make_payment_url,
                data: {
                    "_token": "{{ csrf_token() }}",
                    'id': bookingid,
                    'couponCode': coupon,
                    'pay_by_wallet': $('#wallet-pay-check').prop('checked') ? 1 : 0,
                    'paymentMethodId': $('#paymentMethodId').val()
                },
                beforeSend: function() {
                    this_element.next(".loadergif").removeClass('d-none');
                    this_element.addClass('d-none');
                },
                success: function(res) {
                    console.log(res);

                    if (!res.error) {

                        if (res.type == "CardPayment") {
                            var baseURL = window.location.origin;
                            window.location.href = baseURL + '/process/payment';

                        } else {

                            window.location.href = res.data;
                        }

                    } else {
                        console.log(res.error_message);
                        alert('res.error_message');
                        this_element.next(".loadergif").addClass('d-none');
                        this_element.removeClass('d-none');
                    }
                },
                error: function(reject) {
                    // console.log();
                    if (reject.status === 422) {
                        var errors = $.parseJSON(reject.responseText);
                        $.each(errors, function(key, val) {
                            // $("#" + key + "_error").text(val[0]);
                            consolelog(key, val);
                        });
                    }
                    if (reject.status === 400) {
                        $('#property_booked').modal('show');
                        $('.tabby-error-message').text(reject.responseJSON.error_message);
                    }
                    this_element.next(".loadergif").addClass('d-none');
                    this_element.removeClass('d-none');
                }
            });
            // }

        }
        $(".add-card-btn").on('click', function() {
            $('.error').text(' '); //Clear errors
            $.ajax({
                type: "POST",
                url: make_payment_url,
                data: {
                    "_token": "{{ csrf_token() }}",
                    'name': $('#name').val(),
                    'number': $('#card').val(),
                    'month': $('#expiry_month').val(),
                    'year': $('#expiry_year').val(),
                    'cvv': $('#cvv').val(),
                    'pay_by_wallet': $('#wallet-pay-check').prop('checked') ? 1 : 0,
                    'id': $('#bid').val(),
                    'couponCode': coupon,
                    'paymentMethodId': $('#paymentMethodId').val()
                },
                beforeSend: function() {
                    $(".loadergif").removeClass('d-none');
                    $(".add-card-btn").addClass('d-none');
                    $(".form-control").prop('disabled', true);
                },
                success: function(res) {
                    // console.log(res);
                    // alert(123);
                    // location.reload()
                    if (!res.error) {
                        $('#add-card').modal('hide');
                        window.location.href = res.data;
                    } else {
                        $("#custom_error").text(res.error_message);
                    }
                },
                error: function(reject) {

                    if (reject.status === 422) {
                        var errors = $.parseJSON(reject.responseText);
                        $.each(errors, function(key, val) {
                            $("#" + key + "_error").text(val[0]);
                            // console.log(key, val);
                        });
                        $("#custom_error").text(reject?.responseJSON?.custom_error);
                    }

                    $(".loadergif").addClass('d-none');
                    $(".add-card-btn").removeClass('d-none');
                    $(".form-control").prop('disabled', false);
                }
            });

        });
        $('#coupon_code').on('hidden.bs.modal', function() {
            // Uncheck the "#coupon_check" checkbox and clear the input field when the modal is closed
            handleCouponToggle(); // Call the toggle function to handle the UI changes
            $('#coupon_check').prop('checked', false);
            $('#coupon-res-message').text('');

            $(".loadergif").addClass('d-none');
            $('.mkpayment').removeClass('d-none');
        });
        $('#add-card').on('shown.bs.modal', function() {
            handleCoupon()
        });
        $('#add-card').on('hidden.bs.modal', function() {
            $('#name').val("")
            $('#card').val("")
            $('#expiry_month').val("")
            $('#expiry_year').val("")
            $('#cvv').val("")
            $('#bid').val("")
            $(".loadergif").addClass('d-none');
            $(".add-card-btn").removeClass('d-none');
            $(".form-control").prop('disabled', false);
        })

        // review slider
        let swiperRating = new Swiper(".ratingSlide", {
            slidesPerView: 5,
            spaceBetween: 10,
            freeMode: true,
        });

        $(document).on('click', '.applePay', function() {
            $("#coupon_check").prop("disabled", false);
            $('#wallet-pay-check').prop('disabled', false);
            if (make_payment_url.indexOf("?apple=1") < 0) {
                make_payment_url += "?apple=1"
                $('#booking_type_1').val(1);
            }
        });
        $(document).on('click', '.tabbyPay', function() {
            $('#wallet-pay-check').prop('disabled', true);
            if (make_payment_url.indexOf("?apple=1") < 0) {
                make_payment_url += "?apple=1"
                $('#booking_type_1').val(2);
            }
        });

        $(document).on('click', '.cardPay', function() {
            $('#wallet-pay-check').prop('disabled', false);
            $("#coupon_check").prop("disabled", false);
            $("#wallet-pay-check").prop("disabled", false);
            if (make_payment_url.indexOf("?apple=1") >= 0) {
                make_payment_url = make_payment_url.split("?")[0]
                $('#booking_type_1').val(0);

            }
            $('#paymentMethodId').val(2);
        });

        $(document).on('click', '.sctPay', function() {
            $('#wallet-pay-check').prop('disabled', false);
            $("#coupon_check").prop("disabled", false);
            $("#wallet-pay-check").prop("disabled", false);
            if (make_payment_url.indexOf("?apple=1") >= 0) {
                make_payment_url = make_payment_url.split("?")[0]
                $('#booking_type_1').val(0);

            }
            $('#paymentMethodId').val(12);
        });

        $(document).on('click', '.guest-cancel-reserv-btn', function() {
            $('#cancelConfirmationModal').modal('show');
        });

        $(document).ready(function() {
            $('#bookingCancelled').modal('show');
            // Remove the session key after showing the modal
            @php session()->forget('bookingcancelled'); @endphp
        });

        var swiper = new Swiper(".gr-ds-propImages", {
            pagination: {
                el: ".swiper-pagination",
                dynamicBullets: true
            }
        });
        window.addEventListener("scroll", function() {
            let header = document.getElementsByClassName("gr-detail-header")[0];
            if (window.scrollY > 70) {
                header.classList.add("gr-detail-header-scroll");
            } else {
                header.classList.remove("gr-detail-header-scroll");
            }
        });

        function copyToClipboard(param, button) {
            var copyText;
            if (param === "bookingcode") {
                copyText = document.getElementById("copyText");
            }

            copyText.select();
            document.execCommand("copy");

            let tooltip = $(button).children(".textCopy-tooltip");
            tooltip.addClass("appear");

            setTimeout(function() {
                tooltip.removeClass("appear");
            }, 1000);
        };

        $('#displaymap').locationpicker({

            location: {
                latitude: "{{ $booking->properties->property_address->latitude != '' ? $booking->properties->property_address->latitude : 0 }}",
                longitude: "{{ $booking->properties->property_address->longitude != '' ? $booking->properties->property_address->longitude : 0 }}"
            },
            radius: 0,
            accuracy: 100, // set the accuracy threshold to 100 meters
            addressFormat: "",
            zoom: 10,
            // inputBinding: {
            //     latitudeInput: $('#latitude'),
            //     longitudeInput: $('#longitude'),
            //     locationNameInput: $('#address_line_1')
            // },
            enableAutocomplete: false,
            enableDraggable: false,
            @if ( $booking->status == 'Accepted')
            mapOptions: {
                zoomControl: true,
                mapTypeControl: true,
                streetViewControl: true,
                fullscreenControl: true
            },
            @endif
            onchanged: function(currentLocation, radius, isMarkerDropped) {
                var addressComponents = $(this).locationpicker('map').location.addressComponents;
                // updateControls(addressComponents);
                // alert(1);
            },
            oninitialized: function(component) {
                var addressComponents = $(component).locationpicker('map').location.addressComponents;
                // updateControls(addressComponents);
                // alert(2);
            }
        });

        $('#shareProperty').click(function() {
            $('#share-property').modal('show');
        });

        $(document).on('click', '#btn_cancel_booking', function() {
            //-----------WebEngage Integration------------(Verified)
            let user = @json(auth()->user()->is_host) == true ? 'Host' : DEFAULT_USER;
            let payload = {
                "Cost per night": parseFloat("{{ numberFormat($booking->per_night, 2) }}".replace(',',
                    '')),
                "Unit Code": "{{ $booking->properties->property_code }}",
                "Name": "{{ $booking->properties->name }}",
                "Cancellation Policy": "{{ $booking->cancellation }}",
                "Reservation number": "{{ $booking->code }}",
                "Status": "By the Guest",
                "Checkin Date": "{{ $booking->start_date }}",
                "Checkout Date": "{{ $booking->end_date }}",
                "Category Name": "{{ $booking->properties->property_type_name }}",
                "User": user
            }
            webEngageTracking(CANCEL_BOOKING, payload)
            //-----------WebEngage Integration------------
        });

        $('#ch-tabby-pay').click(function() {
            $('#coupon_check').prop('checked', false);
            $('#wallet-pay-check').prop('checked', false);
            $(".remove_coupon").click();
            handleCouponToggle()
            handleWalletToggle()
            $('#coupon-res-message').text('');
            $("#coupon_check").prop("disabled", true);
            $('#wallet-pay-check').prop('disabled', true);
        });

        const url = new URL(window.location.href)
        const params = url.searchParams
        const rbtn = document.getElementById("review-btn")
        if (params.get("review") == "1" && !!rbtn) {
            rbtn.click()
        }
    </script>
@endpush
