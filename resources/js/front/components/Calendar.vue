<script setup lang="ts">
import type { CalendarOptions, DateSpan<PERSON><PERSON>, DateSelectArg, EventInput, CustomButtonInput, EventClickArg, DatesSetArg, DayCellMountArg, DayCellContentArg } from "@fullcalendar/core"
import type { ReservationCalnedarEventType, ReservationType } from "@/types/dao/Reservation"
import FullCalendar from "@fullcalendar/vue3"
import dayGridPlugin from "@fullcalendar/daygrid"
import interactionPlugin, { type DateClickArg } from "@fullcalendar/interaction"
import multiMonthPlugin from "@fullcalendar/multimonth"
import ReservationDetailCard from "@/components/Comman/ReservationDetailCard.vue";
import Sidebar from "primevue/sidebar"
import ToggleButton from "primevue/togglebutton"
import SelectButton from 'primevue/selectbutton'
import InputNumber from "primevue/inputnumber"
import Dropdown from "primevue/dropdown"
import Button from "primevue/button"
import { deepCopy, monthDayStr, ucFirst, convertDateStr, dateByTz, toDecimalPlaces } from "@/utils"
import { computed, reactive, ref, watch, onMounted } from "vue"
import usePromise from "@/hooks/usePromise"
import type { VerboseFormattingArg } from "@fullcalendar/core/internal"
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
// import Loader from '@/components/Base/loader.vue';
import { useToast } from "primevue/usetoast";

const ASSET_URL = import.meta.env.VITE_ASSET_URL

const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
type WeekdayType = keyof typeof days
type PriceObjType = {
    per_night: number,
    custom_prices: { [key: string]: { price: number, available: boolean, is_price_custom: boolean, discount: number } },
    special_prices: { [key in WeekdayType]?: string }
}
type GetPriceObjType = { price: number, is_price_custom: boolean, discount_price: number, discount_percent: number, discounted: boolean, available: boolean }

const props = defineProps<{
    property_id: number | string,
    low?: boolean,
    is_mob: 0 | 1,
    lang: { [key: string]: string },
    locale: string
}>()

const { result, loading, fetching, createPromise } = usePromise()
const calendar = ref()
const viewType = ref<"month" | "year">("month")
// initialView: "dayGridMonth",
// initialView: "multiMonthYear",

const calendarOptions = reactive<CalendarOptions>({
    locale: props.locale,
    timeZone: "UTC",
    plugins: [
        multiMonthPlugin,
        dayGridPlugin,
        interactionPlugin // needed for dateClick
    ],
    headerToolbar: {
        start: "title",
        center: "",
        end: "",
    },
    titleFormat: function (info: VerboseFormattingArg) {
        return info.date.marker.getFullYear() + " " + info.date.marker.toLocaleDateString(props.locale, { month: viewType.value == "month" ? "long" : undefined })
    },
    initialView: "dayGridMonth",
    selectMirror: true,
    showNonCurrentDates: false,
    validRange: {
        start: "2022-01-01",
        end: convertDateStr(new Date((new Date()).getFullYear() + 1, 12, 0), false)
    },
    selectConstraint: {
        start: dateByTz()
    },
    selectAllow: function (info: DateSpanApi): boolean {
        return !reservations.value.find(reservation => {
            const start = dateByTz(reservation.start)
            const end = dateByTz(reservation.end)
            return info.start <= end && info.end >= start
        })
    },
    selectable: true,
    weekends: true,
    dateClick: props.is_mob ? onDateSelect : undefined,
    select: !props.is_mob ? onDateSelect : undefined,
    dayCellDidMount: initializeDateCell,
    dayCellContent: function (info: DayCellContentArg) {
        if (!!isNaN(parseFloat(info.dayNumberText))) {
            const arabicDigits = "٠١٢٣٤٥٦٧٨٩"
            info.dayNumberText = info.dayNumberText.split("").map((d: string) => arabicDigits.indexOf(d)).join("")
        }
    },
    eventClick: onEventClick,
    loading: () => isLoading.value,
    customButtons: {
        prev: {
            text: 'Previous',
            click: () => onNavClick("prev")
        },
        next: {
            text: 'Next',
            click: () => onNavClick("next")
        },
        "viewBtn": {
            text: viewType.value == "month" ? "Year" : "Month",
            click: () => changeView()
        }
    }
})
const toggleModel = ref<boolean>(false)
const customPrice = ref<number>(0)
const customDiscount = ref<number>(0)
const discountType = ref<string>("calendar")
const selectedYear = ref<number>(dateByTz().getFullYear())
const selectedMonth = ref<number>(dateByTz().getMonth() + 1)
const selectedDates = ref<string[]>([])
const pageChanged = ref<boolean>(false)
const sideBarOpen = ref<boolean>(false)
const priceObj = reactive<PriceObjType>({
    per_night: 0,
    custom_prices: {},
    special_prices: {}
})
const reservations = ref<ReservationCalnedarEventType[]>([])
const selectedReservation = ref<ReservationType>()
const action = ref<"selectDate" | "selectReservation">()

function getPriceObj(date: string | Date, skipCustomPrice: boolean = false): GetPriceObjType {
    const temp: string = convertDateStr(date, false)
    date = dateByTz(date)
    const datePriceObj: GetPriceObjType = {
        price: priceObj.per_night,
        discounted: false,
        is_price_custom: false,
        available: true,
        discount_price: 0,
        discount_percent: 0,
    }
    if (!!priceObj.custom_prices[temp]) {
        datePriceObj.is_price_custom = priceObj.custom_prices[temp].is_price_custom
        datePriceObj.available = priceObj.custom_prices[temp].available
    }
    if (!!priceObj.custom_prices[temp]?.is_price_custom && !skipCustomPrice) {
        datePriceObj.price = priceObj.custom_prices[temp].price;
    } else if (!!priceObj.special_prices[date.toLocaleDateString("en", { weekday: "long" }).toLocaleLowerCase() as WeekdayType]) {
        datePriceObj.price = parseFloat(priceObj.special_prices[date.toLocaleDateString("en", { weekday: "long" }).toLocaleLowerCase() as WeekdayType] || "0")
    }
    if (!!priceObj.custom_prices[temp]?.discount) {
        datePriceObj.discount_percent = priceObj.custom_prices[temp].discount
        datePriceObj.discount_price = toDecimalPlaces(datePriceObj.price - (datePriceObj.price * (priceObj.custom_prices[temp].discount / 100)))
        datePriceObj.discounted = true
    }
    return datePriceObj
}
function onDateSelect(info: DateSelectArg | DateClickArg) {
    if ("date" in info && info.date < dateByTz()) {
        return
    }
    calendar.value?.getApi()?.unselect();
    const start: string = "startStr" in info ? info.startStr : info.dateStr
    const end: string = "startStr" in info ? convertDateStr((new Date(info.endStr)).setDate(info.end.getDate() - 1), false) : info.dateStr
    if (sideBarOpen.value && action.value == "selectReservation") {
        sideBarOpen.value = false
        selectedReservation.value = undefined
    }
    if (start == end) {
        const index: number = selectedDates.value.indexOf(start)
        if (index < 0) {
            const month = "startStr" in info ? info.start.getMonth() : info.date.getMonth()
            const year = "startStr" in info ? info.start.getFullYear() : info.date.getFullYear()
            if (!!selectedDates.value.length && (dateByTz(selectedDates.value[0]).getMonth() != month || dateByTz(selectedDates.value[0]).getFullYear() != year)) {
                selectedDates.value = []
            }
            selectedDates.value.push(start)
        } else {
            selectedDates.value.splice(index, 1)
        }
    } else if (start != end && "startStr" in info) {
        const temp: string[] = []
        let currentDate: Date = dateByTz(info.startStr)
        while (currentDate < info.end) {
            temp.push(convertDateStr(currentDate.setHours(0, 0, 0), false))
            currentDate = dateByTz(currentDate.setDate(currentDate.getDate() + 1))
        }
        selectedDates.value = temp
    }
}
function isDateSelected(date: Date) {
    return selectedDates.value.indexOf(convertDateStr(date, false)) >= 0
}
function initializeDateCell(cell: DayCellMountArg) {
    if ((!cell.isDisabled || viewType.value == "month") && !!cell.el.firstElementChild) {
        setDateCell(cell.el.firstElementChild, cell.date)
    }
}
function setDateCell(el: Element, date: Date) {
    const price_obj: GetPriceObjType = getPriceObj(date)
    const dayEl: Element | null = el.querySelector("a") ?? null
    if (!price_obj.available && !dayEl?.parentElement?.classList.contains("line-through")) {
        dayEl?.parentElement?.classList.add("line-through")
    } else if (price_obj.available && dayEl?.parentElement?.classList.contains("line-through")) {
        dayEl?.parentElement?.classList.remove("line-through")
    }
    const priceEl: Element | null = el.querySelector(".price-tag") ?? null
    const innerPriceEl = `<span ${price_obj.discounted ? "class='line-through'" : ""}>${price_obj.price}</span>${price_obj.discounted ? ` ${price_obj.discount_price}` : ""}`
    if (!priceEl) {
        el.insertAdjacentHTML("afterbegin", `<div class="price-tag">${innerPriceEl}</div>`)
    } else {
        priceEl.innerHTML = innerPriceEl
    }
}
function onLoadData() {
    asyncFunc(`/v1/properties/${props.property_id}/calendar`, {
        params: {
            "year": viewType.value == "year" ? selectedYear.value : undefined,
            "combos": viewType.value == "month" ? [`${selectedYear.value}-${("0" + selectedMonth.value).slice(-2)}`] : undefined
        }
    });
}
function asyncFunc(url: string, data: { [key: string]: any } | undefined = undefined) {
    setTimeout(async () => {
        calendar.value?.getApi().setOption('headerToolbar', {
            start: "title",
            center: "",
            end: ""
        })
        await createPromise(url, data)
        calendar.value?.getApi().setOption('headerToolbar', {
            start: "prev title next",
            center: "",
            end: (!props.low ? "viewBtn" : "")
        })
    }, 500)
}
function onNavClick(nav: "prev" | "next") {
    if (!!calendar.value) {
        const year = calendar.value.getApi().getDate().getFullYear()
        if (nav == "prev") {
            if (viewType.value == "year") {
                selectedYear.value = selectedYear.value != 2022 ? year - 1 : 2022
            } else {
                if (selectedMonth.value != 1) {
                    selectedMonth.value--
                } else if (selectedMonth.value == 1 && selectedYear.value != 2022) {
                    selectedMonth.value = 12
                    selectedYear.value--
                }
            }
        } else if (nav == "next") {
            const lastYear = new Date().getFullYear() + 1
            if (viewType.value == "year") {
                selectedYear.value = selectedYear.value != lastYear ? year + 1 : lastYear
            } else {
                if (selectedMonth.value != 12) {
                    selectedMonth.value++
                } else if (selectedMonth.value == 12 && selectedYear.value != lastYear) {
                    selectedMonth.value = 1
                    selectedYear.value = lastYear
                }
            }
        }
        pageChanged.value = true
        onLoadData()
    }
}
function saveDateChanges() {
    const formData = new FormData()
    selectedDates.value.forEach((date: string) => {
        formData.append("dates[]", date)
    })
    formData.append("available", (toggleModel.value ? 1 : 0).toString())
    formData.append("price", customPrice.value.toString())
    formData.append("discount", customDiscount.value.toString())
    formData.append("type", discountType.value)
    asyncFunc(`/v1/properties/${props.property_id}/calendar/save`, {
        method: "POST",
        data: formData
    })
}
function onSiderBarClose() {
    selectedDates.value = []
}
function onEventClick(info: EventClickArg) {
    asyncFunc(`/v1/properties/${props.property_id}/calendar/bookings/${info.event.id}`)
}
function changeView() {
    viewType.value = viewType.value == "month" ? "year" : "month"
    const customButtons = calendar.value?.getApi()?.getOption("customButtons");
    customButtons.viewBtn.text = ucFirst(viewType.value == "month" ? "year" : "month");
    calendar.value?.getApi()?.setOption("customButtons", customButtons)
    calendar.value?.getApi()?.changeView(viewType.value == "month" ? "dayGridMonth" : "multiMonthYear")
    if (viewType.value == "month") {
        reloadByDate(dateByTz(`${selectedYear.value}-${("0" + selectedMonth.value).slice(-2)}-01`))
    }
}
function reloadByDate(date: Date | null = null, reload = false) {
    if (!date || reload) {
        calendar.value?.getApi()?.gotoDate(`${viewType.value == "month" ? selectedYear.value : (dateByTz()).getFullYear() - 1}-01-01`)
    }
    calendar.value?.getApi()?.gotoDate(!reload && !date ? dateByTz() : date)
}

const getReservationEvents = computed<EventInput[]>(() => (reservations.value.map((reservation: ReservationCalnedarEventType) => {
    const end = dateByTz(reservation.end)
    return {
        id: reservation.id.toString(),
        allDay: true,
        title: reservation.guest_name + (reservation.guests ? " " + reservation.guests : ""),
        start: dateByTz(reservation.start),
        end: dateByTz(end.setDate(end.getDate() + 1)),
        extendedProps: {
            guest_image: reservation.guest_image,
        }
    }
})))
const selectedDatesStrObj = computed<{
    dateStr: string,
    type: "Single Date" | "Custom Dates" | "Date Range",
    price: string,
    availablity: "blocked" | "open" | "mixed",
    discount_price: string,
    discount_percent: string,
    isCustomPrice: boolean,
    isCustomDiscount: boolean
}>(() => {
    const dateObj: { dateStr: string, type: "Single Date" | "Custom Dates" | "Date Range", price: string, availablity: "blocked" | "open" | "mixed", discount_price: string, discount_percent: string, isCustomPrice: boolean, isCustomDiscount: boolean } = {
        dateStr: "",
        type: "Single Date",
        price: "",
        availablity: "open",
        discount_percent: "",
        discount_price: "",
        isCustomPrice: false,
        isCustomDiscount: false
    }
    if (selectedDates.value.length > 0) {
        if (selectedDates.value.length == 1) {
            const priceObj: GetPriceObjType = getPriceObj(selectedDates.value[0])
            dateObj.dateStr = monthDayStr(selectedDates.value[0])
            dateObj.price = priceObj.price.toString()
            dateObj.availablity = priceObj.available ? "open" : "blocked"
            dateObj.price = priceObj.price.toString()
            dateObj.discount_percent = priceObj.discount_percent.toString()
            dateObj.discount_price = priceObj.discount_price.toString()
            dateObj.isCustomPrice = priceObj.is_price_custom
            dateObj.isCustomDiscount = !!priceObj.discount_percent
        } else {
            const dates = selectedDates.value.map((date: string) => dateByTz(date))
                .sort((a: Date, b: Date) => a.getTime() - b.getTime())
            let isRange: boolean = true
            for (let i = 1; i < dates.length; i++) {
                const timeDiff = dates[i].getTime() - dates[i - 1].getTime();
                const oneDay = 24 * 60 * 60 * 1000;
                if (timeDiff !== oneDay && i !== 0) {
                    isRange = false
                    break
                }
            }
            const priceObj = selectedDates.value.map((date: string) => getPriceObj(date))
                .reduce((iniVal: { prices: number[], discounts: number[], available: boolean[], isCustomPrice: boolean[], isCustomDiscount: boolean[] }, obj: GetPriceObjType) => {
                    iniVal.prices.push(obj.price)
                    iniVal.discounts.push(obj.discount_percent)
                    iniVal.available.push(obj.available)
                    iniVal.isCustomPrice.push(!!obj.is_price_custom)
                    iniVal.isCustomDiscount.push(!!obj.discount_percent)
                    return iniVal
                }, { prices: [], discounts: [], available: [], isCustomPrice: [], isCustomDiscount: [] })
            priceObj.prices.sort((a: number, b: number) => a - b)
            priceObj.discounts.sort((a: number, b: number) => a - b)
            if (isRange) {
                dateObj.dateStr = monthDayStr(dates[0]) + " - " + monthDayStr(dates[dates.length - 1], false)
                dateObj.type = "Date Range"
            } else {
                dateObj.dateStr = dates.map((date: Date, i: number) => monthDayStr(date, i == 0)).join(", ")
                dateObj.type = "Custom Dates"
            }
            // Fix here
            dateObj.price = priceObj.prices[0] == priceObj.prices[priceObj.prices.length - 1] ? priceObj.prices[0].toString() : `${priceObj.prices[0]}-${priceObj.prices[priceObj.prices.length - 1]}`
            // dateObj.discount = !isRange?"": priceObj.discounts[0]==priceObj.discounts[priceObj.discounts.length-1]?priceObj.discounts[0].toString():`${priceObj.discounts[0]}-${priceObj.discounts[priceObj.discounts.length-1]}`
            dateObj.discount_percent = priceObj.discounts[0] == priceObj.discounts[priceObj.discounts.length - 1] ? priceObj.discounts[0].toString() : [...new Set(priceObj.discounts)].join(", ").replace(/^0, /, "")
            dateObj.availablity = priceObj.available.indexOf(false) >= 0 && priceObj.available.indexOf(true) >= 0 ? "mixed" : (priceObj.available.indexOf(true) >= 0 ? "open" : "blocked")
            dateObj.isCustomPrice = priceObj.isCustomPrice.indexOf(false) < 0
            dateObj.isCustomDiscount = priceObj.isCustomDiscount.indexOf(false) < 0
        }
    }
    return dateObj
})
const isLoading = computed<boolean>(() => loading.value)

watch(result,
    (newVal: { [key: string]: any }) => {
        if (newVal.status) {
            if (!!newVal.data?.price_obj) {
                reservations.value = newVal.data.reservations
                Object.assign(priceObj, newVal.data.price_obj)
                if (pageChanged.value) {
                    reloadByDate(dateByTz(`${selectedYear.value}-${viewType.value == "month" ? ("0" + selectedMonth.value).slice(-2) : "01"}-01`))
                    pageChanged.value = false
                } else {
                    reloadByDate()
                }
                // calendar.value?.getApi()?.render()
            } else if (!!newVal.data?.reservation) {
                selectedReservation.value = newVal.data.reservation
                sideBarOpen.value = true
                action.value = "selectReservation"
            } else if (action.value == "selectDate") {
                const customPrices = priceObj.custom_prices
                selectedDates.value.forEach((date: string) => {
                    const priceObjKey: string | null = Object.keys(customPrices).find((key: string) => key == date) ?? null
                    const datePriceObj: GetPriceObjType = getPriceObj(date, true)
                    const isCustom: boolean = datePriceObj.price != customPrice.value && !!customPrice.value
                    if (!!priceObjKey) {
                        if (customPrice.value == 0 && customDiscount.value == 0) {
                            toggleModel.value ? delete customPrices[priceObjKey] : customPrices[priceObjKey].available = false
                        } else {
                            customPrices[priceObjKey].price = customPrice.value || datePriceObj.price
                            customPrices[priceObjKey].is_price_custom = isCustom
                            customPrices[priceObjKey].discount = customDiscount.value
                            customPrices[priceObjKey].available = toggleModel.value
                        }
                    } else {
                        customPrices[date] = { price: customPrice.value || datePriceObj.price, is_price_custom: isCustom, available: toggleModel.value, discount: customDiscount.value }
                    }
                    !!customPrices[date] ? priceObj.custom_prices[date] = customPrices[date] : delete priceObj.custom_prices[date]
                    const el: Element | null = document.querySelector(`[data-date='${date}']`)?.firstElementChild ?? null
                    if (!!el) setDateCell(el, dateByTz(date));
                    onSiderBarClose()
                })
            }
        }
    }
);
watch(selectedDatesStrObj, (newVal) => {
    if (!newVal.dateStr && sideBarOpen.value) {
        sideBarOpen.value = false
    } else if (!!newVal.dateStr && !sideBarOpen.value) {
        sideBarOpen.value = true
        action.value = "selectDate"
    }
    toggleModel.value = newVal?.availablity == "open" || newVal?.availablity == "mixed"
    customDiscount.value = newVal.isCustomDiscount && newVal.discount_percent.indexOf(",") < 0 ? Number(newVal.discount_percent) : 0
    customPrice.value = newVal.isCustomPrice && newVal.price.indexOf("-") < 0 ? Number(newVal.price) : 0
    discountType.value = newVal.isCustomPrice ? 'calendar' : 'daily_discount'
})
watch(() => props.property_id, () => {
    onLoadData();
}, { immediate: true })
watch([selectedYear, viewType], ([newVal1, newVal2], [oldVal1, oldVal2]) => {
    if (newVal2 != oldVal2 && newVal2 == "year") {
        onLoadData()
    } else if (newVal2 != oldVal2 && newVal2 == "month") {
        priceObj.custom_prices = deepCopy(Object.keys(priceObj.custom_prices).reduce<PriceObjType["custom_prices"]>((iniVal: PriceObjType["custom_prices"], date: string) => {
            if (dateByTz(date).getMonth() + 1 == selectedMonth.value && dateByTz(date).getFullYear() == selectedYear.value) {
                iniVal[date] = priceObj.custom_prices[date]
            }
            return iniVal
        }, {}));
        reservations.value = reservations.value.filter((reservation: ReservationCalnedarEventType) =>
            (dateByTz(reservation.start).getMonth() + 1 == selectedMonth.value && dateByTz(reservation.start).getFullYear() == selectedYear.value) ||
            (dateByTz(reservation.end).getMonth() + 1 == selectedMonth.value && dateByTz(reservation.end).getFullYear() == selectedYear.value))
    }
}, { immediate: false })

const importCalendarMd = ref(false);
const exportCalendarMd = ref(false);
const calendarUrl = ref('');
const exportCalendarUrl = ref('');
const selectedCalendarColor = ref(null);
const calendarName = ref('');
const calendarLinks = ref([]);
const addButtonDisabled = ref(false);

const validateUrl = (url) => {
  const urlPattern = new RegExp(
    '^(https?:\\/\\/)?' + // protocol
    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|' + // domain name
    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
    '(\\#[-a-z\\d_]*)?$', // fragment locator
    'i'
  );
  return !!urlPattern.test(url);
};

const errors = ref({
  calendarUrl: '',
  selectedCalendarColor: '',
  calendarName: ''
});

const validateFields = () => {
  let isValid = true;
  errors.value = {
    calendarUrl: '',
    selectedCalendarColor: '',
    calendarName: ''
  };

  if (!calendarUrl.value) {
    errors.value.calendarUrl = 'Calendar URL is required.';
    isValid = false;
  } else if (!validateUrl(calendarUrl.value)) {
    errors.value.calendarUrl = 'Please enter a valid URL.';
    isValid = false;
  }

  if (!selectedCalendarColor.value) {
    errors.value.selectedCalendarColor = 'Calendar color is required.';
    isValid = false;
  }

  if (!calendarName.value) {
    errors.value.calendarName = 'Calendar name is required.';
    isValid = false;
  } else if (calendarName.value.length > 20) {
    errors.value.calendarName = 'Calendar name must be at most 20 characters.';
    isValid = false;
  }

  return isValid;
};

const colors = ref([
  { name: 'Red', code: '#F44336' },
  { name: 'Blue', code: '#2196F3' },
  { name: 'Green', code: '#4CAF50' },
]);
// Modify fetchCalendarLinks to use the base URL from the configuration object


const addNewCalendar = () => {

    if (!validateFields()) {
        return;
    }
  const payload = {
    icalendar_url: calendarUrl.value,
    color: selectedCalendarColor.value,
    icalendar_name: calendarName.value,
    property_id:props.property_id
  };

  // Disable the button while the request is being processed
  addButtonDisabled.value = true;
  isLoading.value = true;


  // Call createPromise with proper arguments
  createPromise('v1/calendar-urls-update/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    // Pass the payload directly as body
    data: payload,
  })
    .then((response) => {
      console.log('New calendar added:', response);
      // Optionally, you can reset the input fields after successful addition
      calendarUrl.value = '';
      selectedCalendarColor.value = null;
      calendarName.value = '';
      fetchCalendarLinks();
      addButtonDisabled.value = false;
    })
    .catch((error) => {
      console.error('Error adding new calendar:', error);
    }).finally(() => {
      // Enable the button and hide the loader after the request completes
      setTimeout(() => {
        addButtonDisabled.value = false;
        isLoading.value = false;
      }, 2000); // 2000 milliseconds = 2 seconds
    });
};




const fetchCalendarLinks =  () => {
     createPromise(`v1/calendar-urls/${props.property_id}`).then(()=> {
            console.log(result.value.data);
            calendarLinks.value = result.value.data.links; // Assuming `calendarLinks` is a ref
            exportCalendarUrl.value = result.value.data.calendarlink
        });
    };

const removeLink = (id) => {
     createPromise(`v1/calendar-urls-remove/${id}`, {method:"POST"})
     fetchCalendarLinks();
};

const SyncCalendar = () => {
  createPromise(`v1/host/icalendar/synchronization/${props.property_id}`, {method:"GET"}).then(()=> {
            console.log(result);
            // calendarLinks.value = result.value.data; // Assuming `calendarLinks` is a ref
            onLoadData()
        });
}


onMounted(()=>{fetchCalendarLinks()});




// const exportCalendarUrl = ref<string>(''); // Your calendar URL goes here
    const toast = useToast();
const copyFunc = () => {
  const clipboard = exportCalendarUrl.value; // Get the value of the input field
  // Create a temporary textarea element
  const tempInput = document.createElement('textarea');
  document.body.appendChild(tempInput);
  tempInput.value = clipboard;
  tempInput.select();
  document.execCommand('copy');
  tempInput.remove(); // Remove the temporary element

  // Update the button text to "Copied"
  const button = document.querySelector('.copy-url') as HTMLButtonElement;
    toast.add({ severity: 'success', summary: 'Success', detail: 'Copied to clipboard', life: 3000 });
  if (button) {
    button.textContent = 'Copied';
    // Optionally reset the button text after a delay
    setTimeout(() => {
      button.textContent = 'Copy';
    }, 2000); // Reset after 2 seconds
  }
};



const discountOptions = [
  { label: 'Calendar', value: 'calendar' },
  { label: 'Daily Discount', value: 'daily_discount' }
]
</script>
<template>
  <div class="container-cal">
    <!-- <Loader :loading="loading" /> -->
    <div class="host-full-calendar inner" v-if="!fetching">
      <div class="calendar-btn-main">
        <Button class="import-calendar-btn" @click="SyncCalendar">Sync Calendar</Button>
        <Button class="import-calendar-btn" @click="importCalendarMd = true"
          >Import Calendar</Button
        >
        <Button class="import-calendar-btn" @click="exportCalendarMd = true"
          >Export Calendar</Button
        >
      </div>
      <FullCalendar
        class="demo-app-calendar calendar-scroller"
        :options="{ ...calendarOptions, events: getReservationEvents }"
        ref="calendar"
      >
        <template v-slot:dayCellContent="arg">
          <span class="selected-date" v-if="isDateSelected(arg.date)">
            {{ arg.dayNumberText }}
          </span>
          <template v-else>{{ arg.dayNumberText }}</template>
        </template>
        <template v-slot:eventContent="arg">
          <div :class="{ 'visibility-hidden': !arg.isStart }" class="user-booking-box">
            <img
              :src="ASSET_URL + arg.event.extendedProps.guest_image"
              alt="guest-image"
              width="27"
              height="27"
              class="ob-br"
            />
            <p class="mb-0">{{ arg.event.title }}</p>
            <small>{{ arg.event.extendedProps.description }}</small>
          </div>
        </template>
      </FullCalendar>
      <Sidebar
        :visible="sideBarOpen"
        :dismissable="false"
        :modal="false"
        :showCloseIcon="false"
      >
        <template #header>
          <Button icon="pi pi-times" @click="onSiderBarClose" text />
        </template>
        <template v-if="action == 'selectDate'">
          <div>
            <h4>{{ selectedDatesStrObj.type }} Selected</h4>
            <p>{{ selectedDatesStrObj.dateStr }}</p>
          </div>
          <div>
            <h4>Price</h4>
            <p>{{ selectedDatesStrObj.price }}</p>
          </div>
          <div v-show="selectedDatesStrObj.discount_percent != '0'">
            <h4>Discount</h4>
            <p>{{ selectedDatesStrObj.discount_percent }}</p>
          </div>
          <div>
            <div class="w-full mb-2">
                <SelectButton
                v-model="discountType"
                :options="discountOptions"
                optionLabel="label"
                optionValue="value"
                class="w-full"
                />
            </div>

            <h4 v-if="discountType === 'calendar'">Custom Discount %</h4>
            <h4 v-if="discountType === 'daily_discount'">Daily Discount %</h4>
            <InputNumber
              v-model="customDiscount"
              inputId="minmaxfraction"
              :min="0"
              :max="99.99"
              :minFractionDigits="0"
              :maxFractionDigits="2"
            />
          </div>


          <div v-if="discountType === 'calendar'">
            <h4>Custom Price</h4>
            <InputNumber
              v-model="customPrice"
              inputId="minmaxfraction"
              :min="0"
              :minFractionDigits="0"
              :maxFractionDigits="2"
            />
          </div>
          <!-- <div v-if="selectedDatesStrObj.discount">
                        <h4>Discount</h4>
                        <p>{{ selectedDatesStrObj.discount }}</p>
                    </div> -->
          <div>
            <h4>Availablity</h4>
            <p>{{ ucFirst(selectedDatesStrObj.availablity) }}</p>
            <div class="card flex justify-content-center">
              <ToggleButton
                onLabel="Close"
                offLabel="Open"
                v-model="toggleModel"
                class="w-8rem"
                v-if="selectedDatesStrObj.availablity != 'mixed'"
              />
              <Dropdown
                v-model="toggleModel"
                optionValue="code"
                :options="[
                  { name: 'Open', code: true },
                  { name: 'Close', code: false },
                ]"
                optionLabel="name"
                placeholder="Select Availablity"
                class="w-full md:w-14rem"
                v-else
              />
            </div>
          </div>
          <div>
            <Button label="Save" @click="saveDateChanges()" :disabled="loading" />
          </div>
        </template>
        <ReservationDetailCard
          :lang="lang"
          :reservation="selectedReservation"
          v-else-if="action == 'selectReservation' && !!selectedReservation"
        />
      </Sidebar>
      <Dialog
        v-model:visible="importCalendarMd"
        modal
        header="Import a New Calendar"
        :style="{ width: '45rem' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
        class="import-calendar-dailog-main"
      >
        <div class="import-calendar-dailog">
          <div class="import-calendar-fields">
            <div class="d-flex flex-column gap-2 mb-3">
              <label for="username" class="">Calendar Address (URL)</label>
              <InputText v-model="calendarUrl" placeholder="Calendar Address (URL)" />
              <span v-if="errors.calendarUrl" class="error">{{
                errors.calendarUrl
              }}</span>
            </div>
            <div class="d-flex flex-column gap-2 mb-3">
              <label for="username" class="">Colour of your calendar</label>
              <Dropdown
                v-model="selectedCalendarColor"
                :options="colors"
                optionLabel="name"
                placeholder="Select Color"
                class="w-full"
              />
              <span v-if="errors.selectedCalendarColor" class="error">{{
                errors.selectedCalendarColor
              }}</span>
            </div>
            <div class="d-flex flex-column gap-2 mb-3">
              <label for="username" class="">Name Your Calendar </label>
              <InputText v-model="calendarName" placeholder="Name Your Calendar" />
              <span v-if="errors.calendarName" class="error">{{
                errors.calendarName
              }}</span>
            </div>
            <div class="import-calendar-footbtn">
              <Button @click="addNewCalendar">Add New Calendar</Button>
            </div>
          </div>
          <div class="import-calendar-link">
            <h4 class="mb-4">Import Calendar Link</h4>
            <div
              v-for="link in calendarLinks"
              :key="link.id"
              class="ic-link-box d-flex align-items-center justify-content-between"
            >
              <div class="ic-lb-content d-flex align-items-center">
                <h6>{{ link.icalendar_name }}</h6>
                <InputText type="text" v-model="link.icalendar_url" />
              </div>
              <div class="ic-lb-btn">
                <Button label="Edit" />
                <Button label="Remove" severity="danger" @click="removeLink(link.id)" />
              </div>
            </div>
            <!-- <div class="ic-link-box d-flex align-items-center justify-content-between">
                            <div class="ic-lb-content d-flex align-items-center">
                                <h6>Demo Two</h6>
                                <InputText type="text" v-model="calendarLink" />
                            </div>
                            <div class="ic-lb-btn">
                                <Button label="Edit" />
                                <Button label="Remove" severity="danger" />
                            </div>
                        </div> -->
          </div>
        </div>
      </Dialog>
      <Dialog
        v-model:visible="exportCalendarMd"
        modal
        header="Export Calendar"
        :style="{ width: '45rem' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
        class="import-calendar-dailog-main"
      >
        <div class="import-calendar-dailog">
          <div class="import-calendar-fields">
            <div class="d-flex flex-column gap-2 mb-3">
              <label for="username" class=""
                >Copy and paste the link into other ICAL applications</label
              >
              <div class="copy-url-bx">
                <InputText
                v-model="exportCalendarUrl"
                placeholder="Calendar Address (URL)"
                :readonly="true"
                variant="filled"
                id="url-cp"
              />
              <button class="copy-url"  @click="copyFunc">Copy</button>
              </div>
            </div>
          </div>
        </div>
      </Dialog>
    </div>
  </div>
</template>
<style>
.line-through {
  text-decoration: line-through !important;
}

.fc-day:has(.selected-date) {
  color: white !important;
  background-color: #181818 !important;
  border-radius: 10px;
}

.fc-day .selected-date {
  color: #fff;
}

.fc-day-past {
  cursor: not-allowed !important;
  background-color: #f7f7f7 !important;
  border-color: #ebebeb;
}

.fc-highlight {
  background-color: #f7f7f7 !important;
}

.fc-day-disabled {
  visibility: hidden;
  border: none !important;
}

.fc-event-main {
  cursor: pointer !important;
}

.visibility-hidden {
  visibility: hidden !important;
}

.fc-daygrid-event-harness {
  visibility: initial !important;
}

.fc-more-link {
  display: none !important;
}

.fc.fc-theme-standard
  .fc-view-harness
  .fc-event.fc-daygrid-block-event:has(.fc-event-main) {
  color: #fff !important;
  background: #b0b0b0 !important;
  border-color: #b0b0b0 !important;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 0px 10px;
}

.fc.fc-theme-standard .fc-view-harness th {
  background: #fff;
  border-color: #fff;
  color: #181818;
  padding: 0px 0 15px;
}

.fc .fc-multimonth-title {
  text-align: left;
  color: #181818;
  font-weight: 500;
}

.fc .fc-multimonth-daygrid-table {
  border-top-style: inherit !important;
}

.calendar-scroller ::-webkit-scrollbar-thumb {
  border-radius: 0;
  background: #b0b0b0;
}

.calendar-scroller ::-webkit-scrollbar-track {
  border-radius: 0;
}

.price-tag {
  position: sticky;
  font-size: smaller;
  font-weight: bolder;
  z-index: 4;
}
.p-selectbutton .p-button.p-highlight {
    background-color: #181818 !important;
    border-color: #181818 !important;
    color: #fff !important;
}
</style>
